import { Request } from 'express';

// Cloudflare Turnstile configuration
const ENABLE_TURNSTILE = process.env.ENABLE_TURNSTILE === 'true';
const TURNSTILE_SECRET_KEY = process.env.TURNSTILE_SECRET_KEY || '0x4AAAAAAAkqiE_JYWMwJGzh'; // Test secret key
const TURNSTILE_VERIFY_URL = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';

interface TurnstileResponse {
  success: boolean;
  'error-codes'?: string[];
  challenge_ts?: string;
  hostname?: string;
  action?: string;
  cdata?: string;
}

interface TurnstileVerificationResult {
  success: boolean;
  error?: string;
  errorCodes?: string[];
  challengeTimestamp?: string;
  hostname?: string;
}

// Get client IP address
function getClientIP(req: Request): string {
  return req.ip || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         (req.connection as any)?.socket?.remoteAddress ||
         'unknown';
}

// Verify Turnstile token with Cloudflare
export async function verifyTurnstileToken(
  token: string, 
  req: Request
): Promise<TurnstileVerificationResult> {
  if (!token) {
    return {
      success: false,
      error: 'No Turnstile token provided'
    };
  }

  const clientIP = getClientIP(req);
  
  try {
    console.log(`🔍 Verifying Turnstile token for IP: ${clientIP}`);
    
    const formData = new URLSearchParams();
    formData.append('secret', TURNSTILE_SECRET_KEY);
    formData.append('response', token);
    formData.append('remoteip', clientIP);

    const response = await fetch(TURNSTILE_VERIFY_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData,
    });

    if (!response.ok) {
      console.error(`❌ Turnstile API error: ${response.status} ${response.statusText}`);
      return {
        success: false,
        error: 'Failed to verify with Turnstile service'
      };
    }

    const result: TurnstileResponse = await response.json();
    
    console.log(`🔍 Turnstile verification result:`, {
      success: result.success,
      errorCodes: result['error-codes'],
      hostname: result.hostname,
      challengeTs: result.challenge_ts
    });

    if (result.success) {
      console.log(`✅ Turnstile verification successful for IP: ${clientIP}`);
      return {
        success: true,
        challengeTimestamp: result.challenge_ts,
        hostname: result.hostname
      };
    } else {
      console.log(`❌ Turnstile verification failed for IP: ${clientIP}`, result['error-codes']);
      
      // Map error codes to user-friendly messages
      const errorMessages: Record<string, string> = {
        'missing-input-secret': 'Server configuration error',
        'invalid-input-secret': 'Server configuration error',
        'missing-input-response': 'Security verification required',
        'invalid-input-response': 'Security verification failed',
        'bad-request': 'Invalid security verification request',
        'timeout-or-duplicate': 'Security verification expired or already used',
        'internal-error': 'Security verification service temporarily unavailable'
      };

      const errorCodes = result['error-codes'] || [];
      const primaryError = errorCodes[0];
      const userMessage = errorMessages[primaryError] || 'Security verification failed';

      return {
        success: false,
        error: userMessage,
        errorCodes: errorCodes
      };
    }
  } catch (error) {
    console.error('❌ Turnstile verification error:', error);
    return {
      success: false,
      error: 'Security verification service temporarily unavailable'
    };
  }
}

// Middleware to verify Turnstile token
export function turnstileMiddleware(req: Request, res: any, next: any) {
  const { turnstileToken } = req.body;

  // Skip verification if Turnstile is disabled
  if (!ENABLE_TURNSTILE) {
    console.log('⚠️ Turnstile verification disabled (ENABLE_TURNSTILE=false)');
    return next();
  }

  // Skip verification in development if no token provided and TURNSTILE_SECRET_KEY is test key
  if (!turnstileToken && TURNSTILE_SECRET_KEY === '0x4AAAAAAAkqiE_JYWMwJGzh') {
    console.log('⚠️ Skipping Turnstile verification in development mode');
    return next();
  }

  verifyTurnstileToken(turnstileToken, req)
    .then(result => {
      if (result.success) {
        // Store verification result for logging
        (req as any).turnstileVerified = true;
        (req as any).turnstileHostname = result.hostname;
        next();
      } else {
        console.log(`🚫 Turnstile verification failed: ${result.error}`);
        res.status(400).json({
          message: 'Security verification failed',
          error: result.error || 'Please complete the security verification and try again.'
        });
      }
    })
    .catch(error => {
      console.error('❌ Turnstile middleware error:', error);
      res.status(500).json({
        message: 'Security verification error',
        error: 'Security verification service temporarily unavailable. Please try again.'
      });
    });
}

// Check if Turnstile is properly configured
export function isTurnstileConfigured(): boolean {
  return ENABLE_TURNSTILE && !!(TURNSTILE_SECRET_KEY && TURNSTILE_SECRET_KEY !== '0x4AAAAAAAkqiE_JYWMwJGzh');
}

// Get Turnstile configuration status for admin
export function getTurnstileStatus() {
  return {
    enabled: ENABLE_TURNSTILE,
    configured: isTurnstileConfigured(),
    secretKeySet: !!TURNSTILE_SECRET_KEY,
    usingTestKey: TURNSTILE_SECRET_KEY === '0x4AAAAAAAkqiE_JYWMwJGzh'
  };
}
