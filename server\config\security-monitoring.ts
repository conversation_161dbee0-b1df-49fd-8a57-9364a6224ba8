import * as fs from 'fs';
import * as path from 'path';
import { SecurityContext } from './access-control';

// Security event types
export enum SecurityEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILED = 'LOGIN_FAILED',
  LOGIN_BLOCKED = 'LOGIN_BLOCKED',
  PASSWORD_CHANGED = 'PASSWORD_CHANGED',
  ADMIN_ACCESS = 'ADMIN_ACCESS',
  SUSPICIOUS_QUERY = 'SUSPICIOUS_QUERY',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  DATA_EXPORT = 'DATA_EXPORT',
  BACKUP_CREATED = 'BACKUP_CREATED',
  BACKUP_RESTORED = 'BACKUP_RESTORED',
  DATABASE_ERROR = 'DATABASE_ERROR',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION'
}

// Security event severity levels
export enum SecuritySeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Security event interface
export interface SecurityEvent {
  id: string;
  timestamp: Date;
  type: SecurityEventType;
  severity: SecuritySeverity;
  context: SecurityContext;
  details: any;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
}

// Security monitoring configuration
interface MonitoringConfig {
  enableQueryLogging: boolean;
  enableSecurityLogging: boolean;
  logFilePath: string;
  maxLogFileSize: number;
  alertThresholds: {
    failedLoginsPerMinute: number;
    suspiciousQueriesPerMinute: number;
    unauthorizedAccessPerMinute: number;
  };
  retentionDays: number;
}

// Default monitoring configuration
const DEFAULT_CONFIG: MonitoringConfig = {
  enableQueryLogging: process.env.ENABLE_QUERY_LOGGING === 'true',
  enableSecurityLogging: process.env.ENABLE_SECURITY_LOGGING !== 'false',
  logFilePath: path.join(process.cwd(), 'app', 'logs', 'security.log'),
  maxLogFileSize: 10 * 1024 * 1024, // 10MB
  alertThresholds: {
    failedLoginsPerMinute: 5,
    suspiciousQueriesPerMinute: 10,
    unauthorizedAccessPerMinute: 3
  },
  retentionDays: 30
};

// Security monitoring class
export class SecurityMonitor {
  private config: MonitoringConfig;
  private eventCounts: Map<string, number> = new Map();
  private alertCooldowns: Map<string, number> = new Map();

  constructor(config: Partial<MonitoringConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initializeLogging();
    this.startCleanupSchedule();
  }

  // Initialize logging directory and files
  private initializeLogging(): void {
    const logDir = path.dirname(this.config.logFilePath);
    
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true, mode: 0o750 });
    }
  }

  // Log security event
  logSecurityEvent(
    type: SecurityEventType,
    context: SecurityContext,
    details: any = {},
    severity: SecuritySeverity = SecuritySeverity.MEDIUM
  ): void {
    if (!this.config.enableSecurityLogging) {
      return;
    }

    const event: SecurityEvent = {
      id: this.generateEventId(),
      timestamp: new Date(),
      type,
      severity,
      context,
      details,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    };

    // Write to log file
    this.writeToLogFile(event);

    // Check for alerts
    this.checkAlertThresholds(event);

    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[SECURITY] ${event.type}:`, event);
    }
  }

  // Log database query
  logDatabaseQuery(
    query: string,
    context: SecurityContext,
    executionTime?: number,
    error?: Error
  ): void {
    if (!this.config.enableQueryLogging) {
      return;
    }

    const isSuspicious = this.isSuspiciousQuery(query);
    
    const event: SecurityEvent = {
      id: this.generateEventId(),
      timestamp: new Date(),
      type: isSuspicious ? SecurityEventType.SUSPICIOUS_QUERY : SecurityEventType.LOGIN_SUCCESS,
      severity: isSuspicious ? SecuritySeverity.HIGH : SecuritySeverity.LOW,
      context,
      details: {
        query: query.substring(0, 500), // Limit query length in logs
        executionTime,
        error: error?.message,
        isSuspicious
      }
    };

    this.writeToLogFile(event);

    if (isSuspicious) {
      this.checkAlertThresholds(event);
    }
  }

  // Check if query is suspicious
  private isSuspiciousQuery(query: string): boolean {
    const suspiciousPatterns = [
      /DROP\s+TABLE/i,
      /DELETE\s+FROM.*WHERE\s+1\s*=\s*1/i,
      /UNION\s+SELECT/i,
      /INSERT\s+INTO.*VALUES.*\(/i,
      /UPDATE.*SET.*WHERE\s+1\s*=\s*1/i,
      /--.*$/m,
      /\/\*.*\*\//,
      /;\s*DROP/i,
      /;\s*DELETE/i,
      /;\s*INSERT/i,
      /;\s*UPDATE/i
    ];

    return suspiciousPatterns.some(pattern => pattern.test(query));
  }

  // Write event to log file
  private writeToLogFile(event: SecurityEvent): void {
    try {
      const logEntry = JSON.stringify(event) + '\n';
      
      // Check file size and rotate if necessary
      if (fs.existsSync(this.config.logFilePath)) {
        const stats = fs.statSync(this.config.logFilePath);
        if (stats.size > this.config.maxLogFileSize) {
          this.rotateLogFile();
        }
      }

      fs.appendFileSync(this.config.logFilePath, logEntry, { mode: 0o640 });
    } catch (error) {
      console.error('Failed to write security log:', error);
    }
  }

  // Rotate log file when it gets too large
  private rotateLogFile(): void {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const rotatedPath = this.config.logFilePath.replace('.log', `_${timestamp}.log`);
      
      fs.renameSync(this.config.logFilePath, rotatedPath);
      
      // Compress old log file
      const gzip = require('zlib').createGzip();
      const input = fs.createReadStream(rotatedPath);
      const output = fs.createWriteStream(rotatedPath + '.gz');
      
      input.pipe(gzip).pipe(output);
      
      // Remove uncompressed file after compression
      output.on('finish', () => {
        fs.unlinkSync(rotatedPath);
      });
      
      console.log(`📋 Rotated security log: ${rotatedPath}.gz`);
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  // Check alert thresholds
  private checkAlertThresholds(event: SecurityEvent): void {
    const now = Date.now();
    const minute = Math.floor(now / 60000);
    const alertKey = `${event.type}_${minute}`;
    
    // Increment event count for this minute
    const currentCount = this.eventCounts.get(alertKey) || 0;
    this.eventCounts.set(alertKey, currentCount + 1);

    // Check thresholds
    let threshold = 0;
    let alertType = '';

    switch (event.type) {
      case SecurityEventType.LOGIN_FAILED:
        threshold = this.config.alertThresholds.failedLoginsPerMinute;
        alertType = 'Failed Login Attempts';
        break;
      case SecurityEventType.SUSPICIOUS_QUERY:
        threshold = this.config.alertThresholds.suspiciousQueriesPerMinute;
        alertType = 'Suspicious Database Queries';
        break;
      case SecurityEventType.UNAUTHORIZED_ACCESS:
        threshold = this.config.alertThresholds.unauthorizedAccessPerMinute;
        alertType = 'Unauthorized Access Attempts';
        break;
    }

    if (threshold > 0 && currentCount >= threshold) {
      this.triggerAlert(alertType, event, currentCount);
    }

    // Clean up old event counts
    this.cleanupEventCounts(minute);
  }

  // Trigger security alert
  private triggerAlert(alertType: string, event: SecurityEvent, count: number): void {
    const cooldownKey = `${alertType}_${Math.floor(Date.now() / 300000)}`; // 5-minute cooldown
    
    if (this.alertCooldowns.has(cooldownKey)) {
      return; // Alert already sent in this cooldown period
    }

    this.alertCooldowns.set(cooldownKey, Date.now());

    const alertMessage = {
      type: 'SECURITY_ALERT',
      alertType,
      count,
      threshold: this.getThresholdForType(event.type),
      event,
      timestamp: new Date().toISOString()
    };

    // Log the alert
    console.error('🚨 SECURITY ALERT:', alertMessage);

    // In production, you might want to:
    // - Send email notifications
    // - Send to monitoring service (e.g., Sentry, DataDog)
    // - Trigger automated responses
    // - Send to Slack/Discord webhook
    
    this.logSecurityEvent(
      SecurityEventType.SECURITY_VIOLATION,
      event.context,
      alertMessage,
      SecuritySeverity.CRITICAL
    );
  }

  // Get threshold for event type
  private getThresholdForType(type: SecurityEventType): number {
    switch (type) {
      case SecurityEventType.LOGIN_FAILED:
        return this.config.alertThresholds.failedLoginsPerMinute;
      case SecurityEventType.SUSPICIOUS_QUERY:
        return this.config.alertThresholds.suspiciousQueriesPerMinute;
      case SecurityEventType.UNAUTHORIZED_ACCESS:
        return this.config.alertThresholds.unauthorizedAccessPerMinute;
      default:
        return 0;
    }
  }

  // Clean up old event counts
  private cleanupEventCounts(currentMinute: number): void {
    const cutoff = currentMinute - 5; // Keep last 5 minutes
    
    for (const [key] of this.eventCounts) {
      const minute = parseInt(key.split('_').pop() || '0');
      if (minute < cutoff) {
        this.eventCounts.delete(key);
      }
    }
  }

  // Generate unique event ID
  private generateEventId(): string {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Start cleanup schedule for old logs
  private startCleanupSchedule(): void {
    // Run cleanup daily
    setInterval(() => {
      this.cleanupOldLogs();
    }, 24 * 60 * 60 * 1000);
  }

  // Clean up old log files
  private cleanupOldLogs(): void {
    try {
      const logDir = path.dirname(this.config.logFilePath);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);

      const files = fs.readdirSync(logDir);
      
      for (const file of files) {
        if (file.startsWith('security_') && (file.endsWith('.log') || file.endsWith('.log.gz'))) {
          const filePath = path.join(logDir, file);
          const stats = fs.statSync(filePath);
          
          if (stats.mtime < cutoffDate) {
            fs.unlinkSync(filePath);
            console.log(`🗑️ Removed old security log: ${file}`);
          }
        }
      }
    } catch (error) {
      console.error('Failed to cleanup old logs:', error);
    }
  }

  // Get security statistics
  getSecurityStats(): any {
    // This would typically query the log files or a database
    // For now, return basic stats
    return {
      eventsToday: this.getEventCountForPeriod(24 * 60 * 60 * 1000),
      alertsToday: this.getAlertCountForPeriod(24 * 60 * 60 * 1000),
      logFileSize: this.getLogFileSize(),
      lastAlert: this.getLastAlert()
    };
  }

  // Helper methods for statistics
  private getEventCountForPeriod(periodMs: number): number {
    // Implementation would read from log files
    return 0;
  }

  private getAlertCountForPeriod(periodMs: number): number {
    // Implementation would read from log files
    return 0;
  }

  private getLogFileSize(): number {
    try {
      if (fs.existsSync(this.config.logFilePath)) {
        return fs.statSync(this.config.logFilePath).size;
      }
    } catch (error) {
      console.error('Failed to get log file size:', error);
    }
    return 0;
  }

  private getLastAlert(): Date | null {
    // Implementation would read from log files
    return null;
  }
}

// Global security monitor instance
export const securityMonitor = new SecurityMonitor();
