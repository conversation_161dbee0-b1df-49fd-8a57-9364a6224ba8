import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import { createSecureBackup, restoreFromSecureBackup } from './database-security';

// Database security manager for comprehensive protection
export class DatabaseSecurityManager {
  private dbPath: string;
  private backupDir: string;
  private encryptionKey: string;

  constructor(dbPath: string, backupDir: string) {
    this.dbPath = dbPath;
    this.backupDir = backupDir;
    this.encryptionKey = this.getOrCreateEncryptionKey();
    this.initializeSecurity();
  }

  // Initialize security measures
  private initializeSecurity(): void {
    this.ensureSecureDirectories();
    this.setDatabasePermissions();
    this.setupBackupDirectory();
  }

  // Ensure directories exist with secure permissions
  private ensureSecureDirectories(): void {
    const dbDir = path.dirname(this.dbPath);
    
    // Create database directory if it doesn't exist
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true, mode: 0o750 });
      console.log(`🔒 Created secure database directory: ${dbDir}`);
    }

    // Create backup directory if it doesn't exist
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true, mode: 0o750 });
      console.log(`🔒 Created secure backup directory: ${this.backupDir}`);
    }
  }

  // Set secure permissions on database file
  private setDatabasePermissions(): void {
    try {
      if (fs.existsSync(this.dbPath)) {
        // Set database file permissions to owner read/write only
        fs.chmodSync(this.dbPath, 0o600);
        
        // Set database directory permissions
        const dbDir = path.dirname(this.dbPath);
        fs.chmodSync(dbDir, 0o750);
        
        console.log(`🔒 Set secure permissions on database: ${this.dbPath}`);
      }
    } catch (error) {
      console.warn('Could not set database file permissions:', error);
    }
  }

  // Setup backup directory with secure permissions
  private setupBackupDirectory(): void {
    try {
      if (fs.existsSync(this.backupDir)) {
        fs.chmodSync(this.backupDir, 0o750);
        
        // Set secure permissions on existing backup files
        const backupFiles = fs.readdirSync(this.backupDir);
        backupFiles.forEach(file => {
          const filePath = path.join(this.backupDir, file);
          if (file.endsWith('.db.enc')) {
            fs.chmodSync(filePath, 0o600);
          }
        });
        
        console.log(`🔒 Secured backup directory: ${this.backupDir}`);
      }
    } catch (error) {
      console.warn('Could not secure backup directory:', error);
    }
  }

  // Get or create encryption key
  private getOrCreateEncryptionKey(): string {
    const keyPath = path.join(process.cwd(), '.db-key');
    
    if (fs.existsSync(keyPath)) {
      try {
        const key = fs.readFileSync(keyPath, 'utf8').trim();
        if (key.length === 64) {
          return key;
        }
      } catch (error) {
        console.warn('Failed to read existing encryption key, generating new one');
      }
    }
    
    // Generate new encryption key
    const key = crypto.randomBytes(32).toString('hex');
    
    try {
      fs.writeFileSync(keyPath, key, { mode: 0o600 });
      console.log('🔐 Generated new database encryption key');
    } catch (error) {
      console.error('Failed to save encryption key:', error);
      throw new Error('Cannot secure database without encryption key');
    }
    
    return key;
  }

  // Create encrypted backup
  async createEncryptedBackup(): Promise<string> {
    try {
      const backupPath = await createSecureBackup(this.dbPath, this.backupDir);
      
      // Set secure permissions on the backup file
      fs.chmodSync(backupPath, 0o600);
      
      // Clean up old backups (keep only last 10)
      await this.cleanupOldBackups();
      
      return backupPath;
    } catch (error) {
      console.error('Failed to create encrypted backup:', error);
      throw error;
    }
  }

  // Restore from encrypted backup
  async restoreFromEncryptedBackup(backupPath: string): Promise<void> {
    try {
      // Verify backup file exists and is readable
      if (!fs.existsSync(backupPath)) {
        throw new Error(`Backup file not found: ${backupPath}`);
      }

      // Create backup of current database before restore
      const currentBackupPath = await this.createEncryptedBackup();
      console.log(`📋 Created backup of current database: ${currentBackupPath}`);

      // Restore from encrypted backup
      await restoreFromSecureBackup(backupPath, this.dbPath);
      
      // Set secure permissions on restored database
      this.setDatabasePermissions();
      
      console.log(`✅ Successfully restored database from: ${backupPath}`);
    } catch (error) {
      console.error('Failed to restore from encrypted backup:', error);
      throw error;
    }
  }

  // Clean up old backup files
  private async cleanupOldBackups(keepCount: number = 10): Promise<void> {
    try {
      const backupFiles = fs.readdirSync(this.backupDir)
        .filter(file => file.startsWith('secure_backup_') && file.endsWith('.db.enc'))
        .map(file => ({
          name: file,
          path: path.join(this.backupDir, file),
          mtime: fs.statSync(path.join(this.backupDir, file)).mtime
        }))
        .sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

      // Remove old backups beyond the keep count
      if (backupFiles.length > keepCount) {
        const filesToDelete = backupFiles.slice(keepCount);
        
        for (const file of filesToDelete) {
          fs.unlinkSync(file.path);
          console.log(`🗑️ Removed old backup: ${file.name}`);
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup old backups:', error);
    }
  }

  // Verify database integrity
  async verifyDatabaseIntegrity(): Promise<boolean> {
    try {
      const Database = require('better-sqlite3');
      const db = new Database(this.dbPath, { readonly: true });
      
      // Run integrity check
      const result = db.pragma('integrity_check');
      db.close();
      
      const isIntact = result.length === 1 && result[0].integrity_check === 'ok';
      
      if (isIntact) {
        console.log('✅ Database integrity check passed');
      } else {
        console.error('❌ Database integrity check failed:', result);
      }
      
      return isIntact;
    } catch (error) {
      console.error('Failed to verify database integrity:', error);
      return false;
    }
  }

  // Get database statistics
  getDatabaseStats(): {
    size: number;
    permissions: string;
    lastModified: Date;
    backupCount: number;
    lastBackup?: Date;
  } {
    const stats = fs.statSync(this.dbPath);
    
    // Get backup count and last backup date
    let backupCount = 0;
    let lastBackup: Date | undefined;
    
    try {
      const backupFiles = fs.readdirSync(this.backupDir)
        .filter(file => file.startsWith('secure_backup_') && file.endsWith('.db.enc'));
      
      backupCount = backupFiles.length;
      
      if (backupCount > 0) {
        const latestBackup = backupFiles
          .map(file => fs.statSync(path.join(this.backupDir, file)))
          .sort((a, b) => b.mtime.getTime() - a.mtime.getTime())[0];
        
        lastBackup = latestBackup.mtime;
      }
    } catch (error) {
      console.warn('Failed to get backup statistics:', error);
    }

    return {
      size: stats.size,
      permissions: (stats.mode & parseInt('777', 8)).toString(8),
      lastModified: stats.mtime,
      backupCount,
      lastBackup
    };
  }

  // Secure database shutdown
  async secureShutdown(): Promise<void> {
    try {
      // Create final backup before shutdown
      await this.createEncryptedBackup();
      
      // Verify database integrity
      await this.verifyDatabaseIntegrity();
      
      console.log('🔒 Database security manager shutdown complete');
    } catch (error) {
      console.error('Error during secure shutdown:', error);
    }
  }

  // Schedule automatic backups
  scheduleBackups(intervalHours: number = 24): NodeJS.Timeout {
    const intervalMs = intervalHours * 60 * 60 * 1000;
    
    return setInterval(async () => {
      try {
        console.log('🔄 Starting scheduled backup...');
        await this.createEncryptedBackup();
        console.log('✅ Scheduled backup completed');
      } catch (error) {
        console.error('❌ Scheduled backup failed:', error);
      }
    }, intervalMs);
  }
}
