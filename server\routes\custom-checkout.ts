import { Router, Request, Response } from 'express';
import { storage } from '../storage-factory';
import { z } from 'zod';
// import { isAdmin } from '../middleware/auth';
import { validateEmailDomain } from '../../shared/email-validator';
import { storage } from '../storage-factory';
import { nanoid } from 'nanoid';

export const customCheckoutRouter = Router();

// Schema for creating/updating custom checkout pages
const customCheckoutPageSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  slug: z.string().optional(),
  productName: z.string().min(1, 'Product name is required'),
  productDescription: z.string().min(1, 'Product description is required'),
  price: z.number().positive('Price must be positive'),
  imageUrl: z.union([z.string().url(), z.string().length(0)]).optional(),
  paymentMethod: z.enum(['custom-link', 'trial-custom-link', 'embed-code']),
  customPaymentLinkId: z.string().optional(),
  trialCustomPaymentLinkId: z.string().optional(),
  embedCodeId: z.string().optional(),
  confirmationMessage: z.string().optional(),
  headerTitle: z.string().optional(),
  footerText: z.string().optional(),
  contactEmail: z.string().email().optional(),
  headerLogo: z.string().optional(),
  footerLogo: z.string().optional(),
  themeMode: z.enum(['light', 'dark']).default('light'),
  useReferrerMasking: z.boolean().default(false),
  redirectDelay: z.number().min(0).max(10000).default(2000),
  smtpProviderId: z.string().optional(),
  requireUsername: z.boolean().default(false),
  requireAllowedEmail: z.boolean().default(false),
  isTrialCheckout: z.boolean().default(false),
  expiresAt: z.string().optional(),
  active: z.boolean().default(true)
});

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  console.log('Checking admin session:', req.session);
  if (req.session && req.session.isAdmin) {
    console.log('Admin session verified:', req.session.isAdmin);
    next();
  } else {
    console.log('Admin session verification failed');
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Get all custom checkout pages (admin only)
customCheckoutRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    // Get all checkout pages
    const pages = await storage.getCustomCheckoutPages();
    res.json(pages);
  } catch (error) {
    console.error('Error fetching custom checkout pages:', error);
    res.status(500).json({ message: 'Failed to fetch custom checkout pages' });
  }
});

// Get a specific custom checkout page by ID (admin only)
customCheckoutRouter.get('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const page = await storage.getCustomCheckoutPage(id);

    if (!page) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    // Map requireUsername to requireAllowedEmail for frontend compatibility
    const responseData = {
      ...page,
      requireAllowedEmail: page.requireAllowedEmail || page.requireUsername
    };

    console.log('🔍 CORRECT FILE - GET checkout page data being returned:', JSON.stringify(responseData, null, 2));
    res.json(responseData);
  } catch (error) {
    console.error('Error fetching custom checkout page:', error);
    res.status(500).json({ message: 'Failed to fetch custom checkout page' });
  }
});

// Create a new custom checkout page (admin only)
customCheckoutRouter.post('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    // Map requireAllowedEmail to requireUsername for backend compatibility
    const requestData = { ...req.body };
    if (requestData.requireAllowedEmail !== undefined) {
      requestData.requireUsername = requestData.requireAllowedEmail;
    }

    const validatedData = customCheckoutPageSchema.parse(requestData);

    // Generate a slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = `${validatedData.title.toLowerCase().replace(/[^a-z0-9]+/g, '-')}-${nanoid(6)}`;
    }

    // Check if the slug is already in use
    const existingPage = await storage.getCustomCheckoutPageBySlug(validatedData.slug);
    if (existingPage) {
      return res.status(400).json({ message: 'Slug is already in use' });
    }

    // Validate payment method
    const paymentConfig = await storage.getPaymentConfig();
    const paymentProvider = paymentConfig.providers.find(p => p.id === validatedData.paymentMethod);

    if (!paymentProvider) {
      return res.status(400).json({ message: `Payment method ${validatedData.paymentMethod} is not available` });
    }

    // If using custom-link, validate the link ID
    if (validatedData.paymentMethod === 'custom-link' && validatedData.customPaymentLinkId) {
      const customLinkProvider = paymentConfig.providers.find(p => p.id === 'custom-link');
      if (customLinkProvider && customLinkProvider.config) {
        const config = customLinkProvider.config as any;
        if (Array.isArray(config.links)) {
          const linkExists = config.links.some((link: any) => link.id === validatedData.customPaymentLinkId);
          if (!linkExists) {
            return res.status(400).json({ message: 'Selected custom payment link does not exist' });
          }
        }
      }
    }



    // If using trial-custom-link, validate the trial link ID
    if (validatedData.paymentMethod === 'trial-custom-link' && validatedData.trialCustomPaymentLinkId) {
      const trialCustomLinkProvider = paymentConfig.providers.find(p => p.id === 'trial-custom-link');
      if (trialCustomLinkProvider && trialCustomLinkProvider.config) {
        const config = trialCustomLinkProvider.config as any;
        if (Array.isArray(config.links)) {
          const linkExists = config.links.some((link: any) => link.id === validatedData.trialCustomPaymentLinkId);
          if (!linkExists) {
            return res.status(400).json({ message: 'Selected trial custom payment link does not exist' });
          }
        }
      }
    }



    // Create the page
    const now = new Date().toISOString();
    const page = await storage.createCustomCheckoutPage({
      ...validatedData,
      createdAt: now,
      updatedAt: now
    });

    res.status(201).json(page);
  } catch (error) {
    console.error('Error creating custom checkout page:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to create custom checkout page' });
  }
});

// Update a custom checkout page (admin only)
customCheckoutRouter.put('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const page = await storage.getCustomCheckoutPage(id);

    if (!page) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    // Map requireAllowedEmail to requireUsername for backend compatibility
    const requestData = { ...req.body };
    if (requestData.requireAllowedEmail !== undefined) {
      requestData.requireUsername = requestData.requireAllowedEmail;
    }

    console.log('🔍 CORRECT FILE - Raw request data:', JSON.stringify(requestData, null, 2));
    const validatedData = customCheckoutPageSchema.parse(requestData);
    console.log('Validated data after parsing:', JSON.stringify(validatedData, null, 2));

    // Check if slug is being changed and if it's already in use
    if (validatedData.slug && validatedData.slug !== page.slug) {
      const existingPage = await storage.getCustomCheckoutPageBySlug(validatedData.slug);
      if (existingPage && existingPage.id !== id) {
        return res.status(400).json({ message: 'Slug is already in use' });
      }
    }

    // Update the page
    console.log('Updating page with validated data:', JSON.stringify(validatedData, null, 2));
    const updatedPage = await storage.updateCustomCheckoutPage(id, {
      ...validatedData,
      updatedAt: new Date().toISOString()
    });

    console.log('Updated page result:', JSON.stringify(updatedPage, null, 2));
    res.json(updatedPage);
  } catch (error) {
    console.error('Error updating custom checkout page:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update custom checkout page' });
  }
});

// Duplicate a custom checkout page (admin only)
customCheckoutRouter.post('/:id/duplicate', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const originalPage = await storage.getCustomCheckoutPage(id);

    if (!originalPage) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    // Create a copy of the page with a new title and slug
    const duplicatedData = {
      ...originalPage,
      title: `${originalPage.title} (Copy)`,
      slug: `${originalPage.slug}-copy-${nanoid(6)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      views: 0,
      conversions: 0
    };

    // Remove the id field as it will be auto-generated
    delete (duplicatedData as any).id;

    const duplicatedPage = await storage.createCustomCheckoutPage(duplicatedData);

    console.log('Custom checkout page duplicated successfully:', duplicatedPage);
    res.status(201).json(duplicatedPage);
  } catch (error) {
    console.error('Error duplicating custom checkout page:', error);
    res.status(500).json({ message: 'Failed to duplicate custom checkout page' });
  }
});

// Delete a custom checkout page (admin only)
customCheckoutRouter.delete('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const page = await storage.getCustomCheckoutPage(id);

    if (!page) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    await storage.deleteCustomCheckoutPage(id);

    res.json({ message: 'Custom checkout page deleted successfully' });
  } catch (error) {
    console.error('Error deleting custom checkout page:', error);
    res.status(500).json({ message: 'Failed to delete custom checkout page' });
  }
});

// Public route to get a custom checkout page by slug
customCheckoutRouter.get('/public/:slug', async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;
    const page = await storage.getCustomCheckoutPageBySlug(slug);

    if (!page) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    if (!page.active) {
      return res.status(404).json({ message: 'This checkout page is no longer active' });
    }

    // Check if the page has expired
    if (page.expiresAt) {
      const expiryDate = new Date(page.expiresAt);
      if (expiryDate < new Date()) {
        return res.status(404).json({ message: 'This checkout page has expired' });
      }
    }



    // Increment view count
    await storage.incrementCustomCheckoutPageViews(page.id);

    // Return the page without sensitive information
    const { id, title, productName, productDescription, price, imageUrl, paymentMethod, requireUsername, requireAllowedEmail, isTrialCheckout, confirmationMessage, headerTitle, footerText, headerLogo, footerLogo, themeMode, embedCodeId, contactEmail } = page;

    // Get embed code data if this page uses embed codes
    let embedCodeHeadScript = null;
    if (paymentMethod === 'embed-code' && embedCodeId) {
      const embedCode = await storage.getEmbedCode(embedCodeId);
      if (embedCode && embedCode.active) {
        embedCodeHeadScript = embedCode.headScript;
      }
    }

    const publicData = {
      id,
      title,
      productName,
      productDescription,
      price,
      imageUrl,
      paymentMethod,
      requireAllowedEmail: requireAllowedEmail || requireUsername, // Map requireUsername to requireAllowedEmail for frontend compatibility
      isTrialCheckout,
      confirmationMessage,
      headerTitle,
      footerText,
      headerLogo,
      footerLogo,
      themeMode,
      embedCodeHeadScript,
      contactEmail
    };
    console.log('🔍 CORRECT FILE - Public checkout page data being returned:', JSON.stringify(publicData, null, 2));
    res.json(publicData);
  } catch (error) {
    console.error('Error fetching public custom checkout page:', error);
    res.status(500).json({ message: 'Failed to fetch checkout page' });
  }
});

// Process checkout for a custom checkout page
customCheckoutRouter.post('/checkout/:slug', async (req: Request, res: Response) => {
  try {
    console.log('🚨🚨🚨 CHECKOUT ROUTE HIT IN CUSTOM-CHECKOUT.TS - slug:', req.params.slug);
    console.log('🚨🚨🚨 REQUEST BODY:', JSON.stringify(req.body, null, 2));
    const { slug } = req.params;
    const { fullName, email, username, country, appType, macAddress } = req.body;

    // Validate input
    if (!fullName || !email) {
      return res.status(400).json({ message: 'Full name and email are required' });
    }

    // Validate country
    if (!country) {
      return res.status(400).json({ message: 'Country is required' });
    }

    // Validate MAC address if a device type that requires it is selected
    const requiresMac = ['MAG', 'Formuler Z', 'Smart STB', 'STBEMU'].includes(appType || '');
    if (requiresMac && !macAddress) {
      return res.status(400).json({ message: 'MAC address is required for this device type' });
    }

    // Validate MAC address format if provided
    if (macAddress) {
      const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
      if (!macRegex.test(macAddress)) {
        return res.status(400).json({ message: 'Please enter a valid MAC address in the format 00:1A:72:c9:dc:a4' });
      }
    }

    // Validate email domain
    const emailValidation = validateEmailDomain(email);
    if (!emailValidation.isValid) {
      return res.status(400).json({ message: emailValidation.message });
    }

    // Get the custom checkout page
    const page = await storage.getCustomCheckoutPageBySlug(slug);

    if (!page) {
      return res.status(404).json({ message: 'Custom checkout page not found' });
    }

    // Validate email against allowed list if required
    // Trial checkouts NEVER block emails, regular checkouts only block if requireAllowedEmail is true
    if (!page.isTrialCheckout && page.requireAllowedEmail) {
      // Check if email is in the allowed list
      const isAllowed = await storage.isEmailAllowed(email);
      if (!isAllowed) {
        return res.status(400).json({ message: 'The email you entered is not in our database. This checkout page is only for existing subscribers.' });
      }
    }

    if (!page.active) {
      return res.status(404).json({ message: 'This checkout page is no longer active' });
    }

    // Check if the page has expired
    if (page.expiresAt) {
      const expiryDate = new Date(page.expiresAt);
      if (expiryDate < new Date()) {
        return res.status(404).json({ message: 'This checkout page has expired' });
      }
    }

    // Create a product object from the page data
    const product = {
      id: page.id,
      name: page.productName,
      description: page.productDescription,
      price: page.price,
      imageUrl: page.imageUrl || '',
      active: true
    };

    // Create checkout data
    const checkoutData = {
      fullName,
      email,
      username: username || undefined,
      productId: page.id,
      customCheckoutPageId: page.id, // Add the checkout page ID
      country,
      appType: appType || undefined,
      macAddress: macAddress || undefined
    };

    // Get the payment provider
    const paymentConfig = await storage.getPaymentConfig();
    let paymentProvider;
    let actualPaymentMethod = page.paymentMethod;

    console.log('🔧 CHECKOUT DEBUG - Page payment method:', actualPaymentMethod);
    console.log('🔧 CHECKOUT DEBUG - Page customPaymentLinkId:', page.customPaymentLinkId);
    console.log('🔧 CHECKOUT DEBUG - Page trialCustomPaymentLinkId:', page.trialCustomPaymentLinkId);

    // Map trial payment methods to their regular counterparts for processing
    if (page.paymentMethod === 'trial-custom-link') {
      actualPaymentMethod = 'custom-link';
    }

    if (actualPaymentMethod === 'custom-link') {
      // For trial-custom-link, use the trial provider
      const providerId = page.paymentMethod === 'trial-custom-link' ? 'trial-custom-link' : 'custom-link';
      paymentProvider = paymentConfig.providers.find(p => p.id === providerId);

      // If a specific custom payment link is specified, use it
      if (page.customPaymentLinkId && paymentProvider && paymentProvider.config) {
        console.log('🔧 Processing custom payment link:', page.customPaymentLinkId);
        const config = paymentProvider.config as any;
        if (Array.isArray(config.links)) {
          console.log('🔧 Available links:', config.links.map((l: any) => ({ id: l.id, name: l.name, active: l.active })));
          // Find the specified link and make it the only active one for this checkout
          const linkIndex = config.links.findIndex((link: any) => link.id === page.customPaymentLinkId);
          console.log('🔧 Found link at index:', linkIndex);
          if (linkIndex !== -1) {
            // Create a temporary copy of the config for this checkout
            const tempConfig = { ...config };
            tempConfig.links = [...config.links];

            // Make only the specified link active
            tempConfig.links.forEach((link: any, idx: number) => {
              link.active = idx === linkIndex;
            });

            // Set the last used index to use this link next
            tempConfig.lastUsedIndex = linkIndex - 1;

            // Replace the config temporarily
            paymentProvider = {
              ...paymentProvider,
              config: tempConfig
            };
            console.log('🔧 Modified payment provider config:', JSON.stringify(tempConfig, null, 2));
          } else {
            console.log('🔧 Custom payment link not found:', page.customPaymentLinkId);
          }
        }
      }

      // If a specific trial custom payment link is specified, use it
      if (page.trialCustomPaymentLinkId && paymentProvider && paymentProvider.config && page.paymentMethod === 'trial-custom-link') {
        console.log('🔧 Processing trial custom payment link:', page.trialCustomPaymentLinkId);
        const config = paymentProvider.config as any;
        if (Array.isArray(config.links)) {
          console.log('🔧 Available links:', config.links.map((l: any) => ({ id: l.id, name: l.name, active: l.active })));
          // Find the specified link and make it the only active one for this checkout
          const linkIndex = config.links.findIndex((link: any) => link.id === page.trialCustomPaymentLinkId);
          console.log('🔧 Found link at index:', linkIndex);
          if (linkIndex !== -1) {
            // Create a temporary copy of the config for this checkout
            const tempConfig = { ...config };
            tempConfig.links = [...config.links];

            // Make only the specified link active
            tempConfig.links.forEach((link: any, idx: number) => {
              link.active = idx === linkIndex;
            });

            // Set the last used index to use this link next
            tempConfig.lastUsedIndex = linkIndex - 1;

            // Replace the config temporarily
            paymentProvider = {
              ...paymentProvider,
              config: tempConfig
            };
            console.log('🔧 Modified payment provider config:', JSON.stringify(tempConfig, null, 2));
          } else {
            console.log('🔧 Trial custom payment link not found:', page.trialCustomPaymentLinkId);
          }
        }
      }

    } else if (actualPaymentMethod === 'embed-code') {
      // Handle embed code payment method
      if (page.embedCodeId) {
        const embedCode = await storage.getEmbedCode(page.embedCodeId);
        if (!embedCode || !embedCode.active) {
          return res.status(500).json({
            message: 'Selected embed code is not available'
          });
        }

        // Create a mock payment provider for embed codes
        paymentProvider = {
          id: 'embed-code',
          name: 'External Embed Code',
          active: true,
          config: {
            embedCode: embedCode
          }
        };
      } else {
        return res.status(500).json({
          message: 'No embed code selected for this checkout page'
        });
      }
    }

    if (!paymentProvider || !paymentProvider.active) {
      return res.status(500).json({
        message: `Payment method ${page.paymentMethod} is not available`
      });
    }

    let invoiceResult;
    let paymentMethod = paymentProvider.id;

    if (paymentMethod === 'custom-link' || paymentMethod === 'trial-custom-link') {
      // Generate custom payment link
      const { createCustomPaymentLink } = await import('../services/custom-link');
      const isTrial = paymentMethod === 'trial-custom-link';
      invoiceResult = await createCustomPaymentLink(checkoutData, product, isTrial, paymentProvider);
    } else if (paymentMethod === 'embed-code') {
      // Handle embed code payment method
      const embedCode = paymentProvider.config.embedCode;
      invoiceResult = {
        id: `embed-${Date.now()}`,
        url: null,
        isSimulated: false,
        error: null,
        isDraft: false,
        status: 'pending',
        noPayPalAccount: false,
        buttonHtml: embedCode.buttonHtml,
        embedCode: embedCode
      };
    } else {
      return res.status(500).json({
        message: `Payment provider ${paymentProvider.id} is not supported`
      });
    }

    const {
      id: invoiceId,
      url: invoiceUrl,
      isSimulated,
      error,
      isDraft,
      status,
      noPayPalAccount
    } = invoiceResult;

    // Determine the invoice status
    let invoiceStatus = "sent";
    let invoiceNotes;

    if (noPayPalAccount) {
      invoiceStatus = "no_paypal";
      invoiceNotes = `No invoice generated because the customer email is not valid.`;
    } else if (isSimulated) {
      invoiceStatus = "simulated";
      invoiceNotes = `Simulated invoice due to API error: ${error}`;
    } else if (isDraft) {
      invoiceStatus = "draft";
      invoiceNotes = `Invoice created in draft status. Status: ${status}`;
    }

    // Create invoice in our storage
    const invoice = await storage.createInvoice({
      customerName: checkoutData.fullName,
      customerEmail: checkoutData.email,
      productId: product.id,
      amount: product.price,
      status: invoiceStatus,
      paypalInvoiceId: invoiceId,
      paypalInvoiceUrl: invoiceUrl,
      isTrialOrder: page.isTrialCheckout || false,
      hasUpgraded: false,
      createdAt: new Date().toISOString(),
      notes: invoiceNotes,
      customCheckoutPageId: page.id,
      country: checkoutData.country,
      appType: checkoutData.appType,
      macAddress: checkoutData.macAddress
    });

    // Handle email notifications based on checkout type and payment method
    if (paymentMethod === 'paypal' && noPayPalAccount) {
      // Don't send email for PayPal if the customer doesn't have a PayPal account
      console.log(`No email notification sent for invoice ID: ${invoice.id} (customer has no PayPal account)`);
    } else {
      try {
        const { sendDualInvoiceEmail } = await import('../services/email');

        // For embed code payment method, don't send email (payment form is shown on page)
        if (paymentMethod === 'embed-code') {
          console.log(`No email sent for embed code checkout ID: ${invoice.id} - payment form is shown on page`);
        }
        // For regular checkout pages, always send email
        else if (!page.isTrialCheckout) {
          const deliveryResults = await sendDualInvoiceEmail(
            checkoutData,
            product,
            invoiceUrl,
            paymentMethod,
            page.smtpProviderId,
            page.secondarySmtpProviderId
          );
          console.log(`Dual email delivery for invoice ID: ${invoice.id} - Primary: ${deliveryResults.primary ? 'SUCCESS' : 'FAILED'}, Secondary: ${deliveryResults.secondary ? 'SUCCESS' : 'FAILED'}`);
        }
      } catch (emailError) {
        console.warn("Email notification could not be sent:", emailError);
        // We don't want to fail the checkout if just the email fails
      }
    }

    // Send Telegram notification
    try {
      const { telegramBot } = await import('../services/telegram-bot');
      await telegramBot.sendOrderNotification({
        orderId: invoice.id,
        customerName: checkoutData.fullName,
        customerEmail: checkoutData.email,
        amount: product.price.toString(),
        status: invoiceStatus,
        country: checkoutData.country || 'Unknown',
        appType: checkoutData.appType || 'Unknown',
        isTrialOrder: page.isTrialCheckout || false,
        createdAt: invoice.createdAt,
        macAddress: checkoutData.macAddress,
        checkoutPageName: page.title
      });
      console.log(`Telegram notification sent for invoice ID: ${invoice.id}`);
    } catch (telegramError) {
      console.warn("Telegram notification could not be sent:", telegramError);
      // We don't want to fail the checkout if just the Telegram notification fails
    }

    // Increment conversion count
    await storage.incrementCustomCheckoutPageConversions(page.id);

    // Return success response with appropriate data based on checkout type
    const responseData = {
      invoiceId: invoice.id,
      paypalInvoiceId: invoiceId,
      paypalInvoiceUrl: invoiceUrl,
      isSimulated: isSimulated || false,
      isDraft: isDraft || false,
      noPayPalAccount: noPayPalAccount || false,
      status: status || 'SENT',
    };

    // For embed code payment method, include the button HTML in the response
    if (paymentMethod === 'embed-code' && invoiceResult.buttonHtml) {
      responseData['paypalButtonHtml'] = invoiceResult.buttonHtml;
      responseData['message'] = 'Order processed successfully. Please complete your payment using the form below.';
    } else {
      // Set appropriate message based on payment method and checkout type
      responseData['message'] = paymentMethod === 'custom-link'
        ? `Order processed successfully. A payment link has been sent to your email.`
        : noPayPalAccount
          ? `Order processed successfully. No invoice was generated for this email.`
          : isSimulated
            ? `A simulated invoice was created due to an API error: ${error}`
            : isDraft
              ? `Invoice created successfully in draft status.`
              : 'Invoice created and sent successfully';
    }

    res.status(201).json(responseData);
  } catch (error) {
    console.error("Error processing custom checkout:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: "Validation error",
        errors: error.errors
      });
    }

    res.status(500).json({
      message: "Failed to process checkout",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
});
