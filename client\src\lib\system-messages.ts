/**
 * System Messages Configuration
 * This file defines the structure and default values for system messages
 * that can be customized through the admin interface.
 */

export interface SystemMessage {
  id: string;
  category: string;
  name: string;
  description: string;
  content: string;
  isHtml: boolean;
}

export interface SystemMessageCategory {
  id: string;
  name: string;
  description: string;
}

export const SYSTEM_MESSAGE_CATEGORIES: SystemMessageCategory[] = [
  {
    id: 'checkout',
    name: 'Checkout Messages',
    description: 'Messages displayed during the checkout process'
  },
  {
    id: 'validation',
    name: 'Validation Messages',
    description: 'Messages displayed when validating user input'
  },
  {
    id: 'error',
    name: 'Error Messages',
    description: 'Messages displayed when errors occur'
  },
  {
    id: 'notification',
    name: 'Notification Messages',
    description: 'General notification messages'
  },
  {
    id: 'email',
    name: 'Email Messages',
    description: 'Messages used in email templates'
  }
];

// Default system messages
export const DEFAULT_SYSTEM_MESSAGES: SystemMessage[] = [
  // Checkout Messages
  {
    id: 'checkout_subscriber_only',
    category: 'checkout',
    name: 'Subscriber Only Message',
    description: 'Message shown when a checkout page is restricted to existing subscribers',
    content: 'This purchase is only available to existing subscribers. Please enter the email address associated with your subscription. New users should order a test plan first.',
    isHtml: false
  },
  {
    id: 'checkout_processing',
    category: 'checkout',
    name: 'Processing Order Message',
    description: 'Message shown when an order is being processed',
    content: 'We\'re processing your order. Please wait a moment...',
    isHtml: false
  },
  {
    id: 'checkout_payment_link',
    category: 'checkout',
    name: 'Payment Link Message',
    description: 'Message shown below the checkout button about receiving payment link',
    content: 'You\'ll receive a payment link via email to complete your purchase securely.',
    isHtml: false
  },
  {
    id: 'checkout_confirmation',
    category: 'checkout',
    name: 'Default Confirmation Message',
    description: 'Default message shown in the confirmation dialog before purchase',
    content: 'Are you sure you want to proceed with this purchase? By clicking confirm, you agree to our terms and conditions.',
    isHtml: false
  },

  // Validation Messages
  {
    id: 'validation_email_domain',
    category: 'validation',
    name: 'Email Domain Restriction',
    description: 'Message shown when an email domain is not allowed (deprecated - all domains now accepted)',
    content: 'All email domains are accepted',
    isHtml: false
  },
  {
    id: 'validation_email_not_found',
    category: 'validation',
    name: 'Email Not Found',
    description: 'Message shown when an email is not found in the allowed list',
    content: 'The email you entered is not in our database. This checkout page is only for existing subscribers.',
    isHtml: false
  },
  {
    id: 'validation_mac_address_required',
    category: 'validation',
    name: 'MAC Address Required',
    description: 'Message shown when a MAC address is required but not provided',
    content: 'MAC address is required for this device type',
    isHtml: false
  },
  {
    id: 'validation_mac_address_format',
    category: 'validation',
    name: 'MAC Address Format',
    description: 'Message shown when a MAC address is in an invalid format',
    content: 'Please enter a valid MAC address in the format 00:1A:72:c9:dc:a4',
    isHtml: false
  },

  // Error Messages
  {
    id: 'error_checkout_failed',
    category: 'error',
    name: 'Checkout Failed',
    description: 'Message shown when checkout fails',
    content: 'Failed to process checkout. Please try again or contact support.',
    isHtml: false
  },
  {
    id: 'error_email_not_found_detail',
    category: 'error',
    name: 'Email Not Found Detail',
    description: 'Detailed message shown when an email is not found in the allowed list',
    content: '<div class="space-y-2"><p>The email you entered is not in our database.</p><p>This checkout page is only for existing subscribers. If you are not an existing subscriber, please order a test plan first.</p><p>Contact support if you believe this is an error.</p></div>',
    isHtml: true
  },

  // Notification Messages
  {
    id: 'notification_order_success',
    category: 'notification',
    name: 'Order Success',
    description: 'Message shown when an order is successfully placed',
    content: 'Your order has been successfully placed. Thank you for your purchase!',
    isHtml: false
  }
];
