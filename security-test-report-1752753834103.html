
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .suite { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 6px; }
        .suite-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .suite-name { font-weight: bold; font-size: 1.1em; }
        .duration { color: #666; font-size: 0.9em; }
        .description { color: #666; margin-bottom: 10px; }
        .status { padding: 4px 8px; border-radius: 4px; color: white; font-size: 0.9em; }
        .status.passed { background-color: #28a745; }
        .status.failed { background-color: #dc3545; }
        .footer { text-align: center; margin-top: 30px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Security Test Report</h1>
            <p>Generated on 2025-07-17T12:03:54.064Z</p>
        </div>
        
        <div class="summary">
            <div class="metric">
                <div class="metric-value">5</div>
                <div>Total Suites</div>
            </div>
            <div class="metric">
                <div class="metric-value passed">5</div>
                <div>Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value failed">0</div>
                <div>Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value">100.0%</div>
                <div>Success Rate</div>
            </div>
            <div class="metric">
                <div class="metric-value">0.4s</div>
                <div>Total Duration</div>
            </div>
        </div>
        
        <h2>Test Suite Results</h2>
        
            <div class="suite">
                <div class="suite-header">
                    <div class="suite-name">Email Protection System</div>
                    <div>
                        <span class="status passed">
                            PASSED
                        </span>
                        <span class="duration">70ms</span>
                    </div>
                </div>
                <div class="description">Tests field-level encryption, obfuscation, and audit trails</div>
                
            </div>
        
            <div class="suite">
                <div class="suite-header">
                    <div class="suite-name">SQLCipher Integration</div>
                    <div>
                        <span class="status passed">
                            PASSED
                        </span>
                        <span class="duration">59ms</span>
                    </div>
                </div>
                <div class="description">Tests database-level encryption and migration tools</div>
                
            </div>
        
            <div class="suite">
                <div class="suite-header">
                    <div class="suite-name">Key Rotation System</div>
                    <div>
                        <span class="status passed">
                            PASSED
                        </span>
                        <span class="duration">150ms</span>
                    </div>
                </div>
                <div class="description">Tests automated key rotation and data re-encryption</div>
                
            </div>
        
            <div class="suite">
                <div class="suite-header">
                    <div class="suite-name">Database Connection Security</div>
                    <div>
                        <span class="status passed">
                            PASSED
                        </span>
                        <span class="duration">65ms</span>
                    </div>
                </div>
                <div class="description">Tests connection pooling, health monitoring, and timeouts</div>
                
            </div>
        
            <div class="suite">
                <div class="suite-header">
                    <div class="suite-name">Security Headers</div>
                    <div>
                        <span class="status passed">
                            PASSED
                        </span>
                        <span class="duration">75ms</span>
                    </div>
                </div>
                <div class="description">Tests CSP, HSTS, and other web security headers</div>
                
            </div>
        
        
        <div class="footer">
            <p>Detailed results saved to: security-test-report-1752753834103.json</p>
        </div>
    </div>
</body>
</html>