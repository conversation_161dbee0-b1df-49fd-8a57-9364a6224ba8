import { db } from '../db';
import { sql } from 'drizzle-orm';

export async function addEmbedCodeToCheckout() {
  console.log('Adding embed_code_id column to custom_checkout_pages...');

  try {
    // Add embed_code_id column
    await db.execute(sql`
      ALTER TABLE custom_checkout_pages
      ADD COLUMN embed_code_id TEXT
    `);

    console.log('Successfully added embed_code_id column to custom_checkout_pages');
  } catch (error) {
    console.error('Error adding embed_code_id column to custom_checkout_pages:', error);
    throw error;
  }
}
