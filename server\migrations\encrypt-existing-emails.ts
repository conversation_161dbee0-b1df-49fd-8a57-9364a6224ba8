import { db } from '../db';
import { allowedEmails } from '../../shared/schema';
import { emailProtection } from '../config/email-protection';
import { normalizeEmail } from '../../shared/email-utils';
import { eq } from 'drizzle-orm';

/**
 * Migration script to encrypt existing email addresses in the allowedEmails table
 * This should be run once when implementing field-level encryption
 */

interface MigrationResult {
  success: boolean;
  totalEmails: number;
  encryptedEmails: number;
  skippedEmails: number;
  errors: string[];
}

export async function encryptExistingEmails(): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: false,
    totalEmails: 0,
    encryptedEmails: 0,
    skippedEmails: 0,
    errors: []
  };

  console.log('🔐 Starting email encryption migration...');

  try {
    // Get all existing emails
    const existingEmails = await db.select().from(allowedEmails);
    result.totalEmails = existingEmails.length;

    console.log(`📊 Found ${result.totalEmails} emails to process`);

    if (result.totalEmails === 0) {
      console.log('✅ No emails found to encrypt');
      result.success = true;
      return result;
    }

    // Process each email
    for (const emailRecord of existingEmails) {
      try {
        // Check if email is already encrypted (has version prefix)
        if (emailRecord.email.includes(':') && emailRecord.email.startsWith('v')) {
          console.log(`⏭️ Skipping already encrypted email ID ${emailRecord.id}`);
          result.skippedEmails++;
          continue;
        }

        // Normalize and encrypt the email
        const normalizedEmail = normalizeEmail(emailRecord.email);
        const encryptedEmail = emailProtection.encryptEmailForStorage(normalizedEmail);

        // Update the database record
        await db
          .update(allowedEmails)
          .set({ 
            email: encryptedEmail,
            lastUpdated: new Date().toISOString()
          })
          .where(eq(allowedEmails.id, emailRecord.id));

        console.log(`✅ Encrypted email ID ${emailRecord.id}: ${emailProtection.obfuscateEmailForDisplay(normalizedEmail)}`);
        result.encryptedEmails++;

      } catch (error) {
        const errorMsg = `Failed to encrypt email ID ${emailRecord.id}: ${error.message}`;
        console.error(`❌ ${errorMsg}`);
        result.errors.push(errorMsg);
      }
    }

    // Verify encryption worked
    const verificationErrors = await verifyEncryption();
    result.errors.push(...verificationErrors);

    result.success = result.errors.length === 0;

    console.log('\n📊 Migration Summary:');
    console.log(`Total emails: ${result.totalEmails}`);
    console.log(`Encrypted: ${result.encryptedEmails}`);
    console.log(`Skipped: ${result.skippedEmails}`);
    console.log(`Errors: ${result.errors.length}`);

    if (result.success) {
      console.log('🎉 Email encryption migration completed successfully!');
    } else {
      console.log('⚠️ Email encryption migration completed with errors:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }

  } catch (error) {
    const errorMsg = `Migration failed: ${error.message}`;
    console.error(`❌ ${errorMsg}`);
    result.errors.push(errorMsg);
  }

  return result;
}

// Verify that encryption/decryption works correctly
async function verifyEncryption(): Promise<string[]> {
  const errors: string[] = [];

  try {
    console.log('\n🔍 Verifying encryption...');

    // Get a sample of encrypted emails
    const encryptedEmails = await db.select().from(allowedEmails).limit(5);

    for (const emailRecord of encryptedEmails) {
      try {
        // Try to decrypt the email
        const decryptedEmail = emailProtection.decryptEmailFromStorage(emailRecord.email);
        
        // Verify it's a valid email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(decryptedEmail)) {
          errors.push(`Decrypted email ID ${emailRecord.id} is not valid: ${decryptedEmail}`);
        } else {
          console.log(`✅ Verified email ID ${emailRecord.id}: ${emailProtection.obfuscateEmailForDisplay(decryptedEmail)}`);
        }

      } catch (error) {
        errors.push(`Failed to decrypt email ID ${emailRecord.id}: ${error.message}`);
      }
    }

    if (errors.length === 0) {
      console.log('✅ Encryption verification passed');
    } else {
      console.log(`❌ Encryption verification failed with ${errors.length} errors`);
    }

  } catch (error) {
    errors.push(`Verification failed: ${error.message}`);
  }

  return errors;
}

// Rollback function to decrypt emails back to plain text
export async function rollbackEmailEncryption(): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: false,
    totalEmails: 0,
    encryptedEmails: 0,
    skippedEmails: 0,
    errors: []
  };

  console.log('🔄 Starting email encryption rollback...');

  try {
    // Get all existing emails
    const existingEmails = await db.select().from(allowedEmails);
    result.totalEmails = existingEmails.length;

    console.log(`📊 Found ${result.totalEmails} emails to process`);

    // Process each email
    for (const emailRecord of existingEmails) {
      try {
        // Check if email is encrypted (has version prefix)
        if (!emailRecord.email.includes(':') || !emailRecord.email.startsWith('v')) {
          console.log(`⏭️ Skipping non-encrypted email ID ${emailRecord.id}`);
          result.skippedEmails++;
          continue;
        }

        // Decrypt the email
        const decryptedEmail = emailProtection.decryptEmailFromStorage(emailRecord.email);

        // Update the database record with plain text
        await db
          .update(allowedEmails)
          .set({ 
            email: decryptedEmail,
            lastUpdated: new Date().toISOString()
          })
          .where(eq(allowedEmails.id, emailRecord.id));

        console.log(`✅ Decrypted email ID ${emailRecord.id}: ${emailProtection.obfuscateEmailForDisplay(decryptedEmail)}`);
        result.encryptedEmails++;

      } catch (error) {
        const errorMsg = `Failed to decrypt email ID ${emailRecord.id}: ${error.message}`;
        console.error(`❌ ${errorMsg}`);
        result.errors.push(errorMsg);
      }
    }

    result.success = result.errors.length === 0;

    console.log('\n📊 Rollback Summary:');
    console.log(`Total emails: ${result.totalEmails}`);
    console.log(`Decrypted: ${result.encryptedEmails}`);
    console.log(`Skipped: ${result.skippedEmails}`);
    console.log(`Errors: ${result.errors.length}`);

    if (result.success) {
      console.log('🎉 Email encryption rollback completed successfully!');
    } else {
      console.log('⚠️ Email encryption rollback completed with errors:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }

  } catch (error) {
    const errorMsg = `Rollback failed: ${error.message}`;
    console.error(`❌ ${errorMsg}`);
    result.errors.push(errorMsg);
  }

  return result;
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];

  if (command === 'encrypt') {
    encryptExistingEmails()
      .then(result => {
        process.exit(result.success ? 0 : 1);
      })
      .catch(error => {
        console.error('Migration failed:', error);
        process.exit(1);
      });
  } else if (command === 'rollback') {
    rollbackEmailEncryption()
      .then(result => {
        process.exit(result.success ? 0 : 1);
      })
      .catch(error => {
        console.error('Rollback failed:', error);
        process.exit(1);
      });
  } else {
    console.log('Usage:');
    console.log('  npm run migrate:encrypt-emails encrypt   - Encrypt existing emails');
    console.log('  npm run migrate:encrypt-emails rollback  - Rollback email encryption');
    process.exit(1);
  }
}
