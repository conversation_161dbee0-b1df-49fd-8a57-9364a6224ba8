import { Request, Response, NextFunction } from 'express';
import { storage } from '../storage-factory';

/**
 * Middleware to apply privacy and security headers based on configuration
 */
export async function privacyMiddleware(req: Request, res: Response, next: NextFunction) {
  try {
    const dbSettings = await storage.getGeneralSettings();

    if (!dbSettings || !dbSettings.seoPrivacySettings) {
      return next();
    }

    const seoPrivacy = JSON.parse(dbSettings.seoPrivacySettings);
    const privacySettings = seoPrivacy.privacyHeaders || {};

    // Hide server information
    if (privacySettings.hideServerInfo) {
      res.removeHeader('X-Powered-By');
      res.removeHeader('Server');
      res.setHeader('Server', '');
    }

    // Prevent framing (clickjacking protection)
    if (privacySettings.preventFraming) {
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('Content-Security-Policy', "frame-ancestors 'none'");
    }

    // Disable referrer information
    if (privacySettings.disableReferrer) {
      res.setHeader('Referrer-Policy', 'no-referrer');
    }

    // Hide generator information
    if (privacySettings.hideGenerator) {
      res.removeHeader('X-Generator');
      res.removeHeader('X-Powered-By');
    }

    // Additional privacy headers
    if (seoPrivacy.hideFramework) {
      // Remove any headers that might reveal the technology stack
      res.removeHeader('X-Powered-By');
      res.removeHeader('X-Express-Powered-By');
      res.removeHeader('X-Runtime');
      res.removeHeader('X-Version');
      res.removeHeader('X-AspNet-Version');
      res.removeHeader('X-AspNetMvc-Version');
      res.removeHeader('X-Drupal-Cache');
      res.removeHeader('X-Generator');
      res.removeHeader('X-Mod-Pagespeed');
      res.removeHeader('X-Pingback');
      res.removeHeader('X-Powered-CMS');
      res.removeHeader('X-Turbo-Charged-By');
      res.removeHeader('X-Varnish');
      res.removeHeader('Via');
      
      // Set generic server header
      res.setHeader('Server', 'nginx');
    }

    // Security headers for additional protection
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    
    // Prevent search engine indexing if globally disabled
    if (seoPrivacy.globalNoIndex || seoPrivacy.hideFromSearchEngines) {
      res.setHeader('X-Robots-Tag', 'noindex, nofollow, noarchive, nosnippet, noimageindex, notranslate');
    }

  } catch (error) {
    console.error('Error applying privacy middleware:', error);
  }

  next();
}

/**
 * Middleware to add no-index meta tags to HTML responses
 */
export async function noIndexMiddleware(req: Request, res: Response, next: NextFunction) {
  try {
    const dbSettings = await storage.getGeneralSettings();

    if (!dbSettings || !dbSettings.seoPrivacySettings) {
      return next();
    }

    const seoPrivacy = JSON.parse(dbSettings.seoPrivacySettings);

    // Check if this page should be indexed based on rules
    const shouldIndex = shouldPageBeIndexed(req.path, { seoPrivacy });
    
    if (!shouldIndex) {
      // Store the no-index flag in response locals for use in templates
      res.locals.noIndex = true;
      res.setHeader('X-Robots-Tag', 'noindex, nofollow, noarchive, nosnippet, noimageindex, notranslate');
    }
  } catch (error) {
    console.error('Error applying no-index middleware:', error);
  }

  next();
}

/**
 * Determine if a page should be indexed based on configuration rules
 */
function shouldPageBeIndexed(path: string, config: any): boolean {
  const { seoPrivacy } = config;
  
  // If global no-index is enabled, nothing should be indexed
  if (seoPrivacy.globalNoIndex || seoPrivacy.hideFromSearchEngines) {
    return false;
  }

  // Check specific page rules
  if (path === '/' || path === '/home') {
    return seoPrivacy.pageIndexingRules.homepage;
  }
  
  if (path.startsWith('/checkout/')) {
    return seoPrivacy.pageIndexingRules.checkoutPages;
  }
  
  if (path.startsWith('/admin/')) {
    return seoPrivacy.pageIndexingRules.adminPages;
  }
  
  // Default to custom pages rule
  return seoPrivacy.pageIndexingRules.customPages;
}

/**
 * Block known crawlers and bots
 */
export async function blockCrawlersMiddleware(req: Request, res: Response, next: NextFunction) {
  try {
    const dbSettings = await storage.getGeneralSettings();

    if (!dbSettings || !dbSettings.seoPrivacySettings) {
      return next();
    }

    const seoPrivacy = JSON.parse(dbSettings.seoPrivacySettings);

    if (!seoPrivacy.hideFromSearchEngines) {
      return next();
    }

    const userAgent = req.get('User-Agent')?.toLowerCase() || '';
    
    // List of known crawlers and bots to block
    const blockedBots = [
      'googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider', 'yandexbot',
      'facebookexternalhit', 'twitterbot', 'linkedinbot', 'whatsapp', 'telegrambot',
      'ahrefsbot', 'semrushbot', 'mj12bot', 'dotbot', 'blexbot', 'siteauditbot',
      'megaindex', 'screamingfrogseo', 'seositecheckup', 'woorankbot', 'seokicks',
      'seolyticscrawler', 'linkdexbot', 'spbot', 'mojeekbot', 'petalbot', 'ccbot',
      'gptbot', 'chatgpt-user', 'claude-web', 'anthropic-ai', 'perplexitybot',
      'crawler', 'spider', 'bot', 'scraper', 'parser', 'extractor'
    ];

    // Check if the user agent contains any blocked bot identifiers
    const isBot = blockedBots.some(bot => userAgent.includes(bot));
    
    if (isBot) {
      console.log(`Blocked crawler: ${userAgent}`);
      return res.status(403).send('Access denied');
    }

  } catch (error) {
    console.error('Error in crawler blocking middleware:', error);
  }

  next();
}
