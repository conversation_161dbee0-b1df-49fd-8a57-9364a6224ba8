import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getGeneralSettings, updateGeneralSettings } from '@/api/generalSettings';

const SEOPrivacySettings: React.FC = () => {
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);

  const { data: settings, isLoading } = useQuery({
    queryKey: ['generalSettings'],
    queryFn: getGeneralSettings,
  });

  const updateMutation = useMutation({
    mutationFn: updateGeneralSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['generalSettings'] });
      setIsEditing(false);
    },
  });

  const [formData, setFormData] = useState({
    globalNoIndex: true,
    hideFromSearchEngines: true,
    disableSitemaps: true,
    hideFramework: true,
    customRobotsTxt: '',
    pageIndexingRules: {
      homepage: false,
      checkoutPages: false,
      adminPages: false,
      customPages: false,
    },
    privacyHeaders: {
      hideServerInfo: true,
      preventFraming: true,
      disableReferrer: true,
      hideGenerator: true,
    },
  });

  React.useEffect(() => {
    if (settings?.seoPrivacy) {
      setFormData(prev => ({
        ...prev,
        ...settings.seoPrivacy,
        pageIndexingRules: {
          homepage: false,
          checkoutPages: false,
          adminPages: false,
          customPages: false,
          ...settings.seoPrivacy.pageIndexingRules,
        },
        privacyHeaders: {
          hideServerInfo: true,
          preventFraming: true,
          disableReferrer: true,
          hideGenerator: true,
          ...settings.seoPrivacy.privacyHeaders,
        },
      }));
    }
  }, [settings]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const updatedSettings = {
      ...settings,
      seoPrivacy: formData,
    };

    updateMutation.mutate(updatedSettings);
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleNestedChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...(prev[parent as keyof typeof prev] || {}),
        [field]: value,
      },
    }));
  };

  if (isLoading) {
    return <div className="p-6">Loading SEO & Privacy settings...</div>;
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">SEO & Privacy Settings</h2>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            {isEditing ? 'Cancel' : 'Edit Settings'}
          </button>
        </div>

        {/* Warning Box */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Privacy Protection Active</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>These settings help protect your site from search engines, crawlers, and detection tools to avoid fraud buyers.</p>
              </div>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Global Settings */}
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Global Privacy Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.globalNoIndex}
                  onChange={(e) => handleInputChange('globalNoIndex', e.target.checked)}
                  disabled={!isEditing}
                  className="mr-2"
                />
                <span className="text-sm">Global No-Index (Hide entire site from search engines)</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.hideFromSearchEngines}
                  onChange={(e) => handleInputChange('hideFromSearchEngines', e.target.checked)}
                  disabled={!isEditing}
                  className="mr-2"
                />
                <span className="text-sm">Block All Search Engine Crawlers</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.disableSitemaps}
                  onChange={(e) => handleInputChange('disableSitemaps', e.target.checked)}
                  disabled={!isEditing}
                  className="mr-2"
                />
                <span className="text-sm">Disable Sitemaps</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.hideFramework}
                  onChange={(e) => handleInputChange('hideFramework', e.target.checked)}
                  disabled={!isEditing}
                  className="mr-2"
                />
                <span className="text-sm">Hide Technology Stack</span>
              </label>
            </div>
          </div>

          {/* Page Indexing Rules */}
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Page Indexing Rules</h3>
            <p className="text-sm text-gray-600 mb-4">Control which pages can be indexed (only applies when Global No-Index is disabled)</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.pageIndexingRules?.homepage || false}
                  onChange={(e) => handleNestedChange('pageIndexingRules', 'homepage', e.target.checked)}
                  disabled={!isEditing || formData.globalNoIndex}
                  className="mr-2"
                />
                <span className="text-sm">Allow Homepage Indexing</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.pageIndexingRules?.checkoutPages || false}
                  onChange={(e) => handleNestedChange('pageIndexingRules', 'checkoutPages', e.target.checked)}
                  disabled={!isEditing || formData.globalNoIndex}
                  className="mr-2"
                />
                <span className="text-sm">Allow Checkout Pages Indexing</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.pageIndexingRules?.adminPages || false}
                  onChange={(e) => handleNestedChange('pageIndexingRules', 'adminPages', e.target.checked)}
                  disabled={!isEditing || formData.globalNoIndex}
                  className="mr-2"
                />
                <span className="text-sm">Allow Admin Pages Indexing</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.pageIndexingRules?.customPages || false}
                  onChange={(e) => handleNestedChange('pageIndexingRules', 'customPages', e.target.checked)}
                  disabled={!isEditing || formData.globalNoIndex}
                  className="mr-2"
                />
                <span className="text-sm">Allow Custom Pages Indexing</span>
              </label>
            </div>
          </div>

          {/* Privacy Headers */}
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Privacy Headers</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.privacyHeaders?.hideServerInfo || false}
                  onChange={(e) => handleNestedChange('privacyHeaders', 'hideServerInfo', e.target.checked)}
                  disabled={!isEditing}
                  className="mr-2"
                />
                <span className="text-sm">Hide Server Information</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.privacyHeaders?.preventFraming || false}
                  onChange={(e) => handleNestedChange('privacyHeaders', 'preventFraming', e.target.checked)}
                  disabled={!isEditing}
                  className="mr-2"
                />
                <span className="text-sm">Prevent Framing (Anti-Clickjacking)</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.privacyHeaders?.disableReferrer || false}
                  onChange={(e) => handleNestedChange('privacyHeaders', 'disableReferrer', e.target.checked)}
                  disabled={!isEditing}
                  className="mr-2"
                />
                <span className="text-sm">Disable Referrer Information</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.privacyHeaders?.hideGenerator || false}
                  onChange={(e) => handleNestedChange('privacyHeaders', 'hideGenerator', e.target.checked)}
                  disabled={!isEditing}
                  className="mr-2"
                />
                <span className="text-sm">Hide Generator Information</span>
              </label>
            </div>
          </div>

          {/* Custom Robots.txt */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Custom Robots.txt</h3>
            <textarea
              value={formData.customRobotsTxt}
              onChange={(e) => handleInputChange('customRobotsTxt', e.target.value)}
              disabled={!isEditing}
              rows={15}
              className="w-full p-3 border border-gray-300 rounded-md font-mono text-sm"
              placeholder="Enter custom robots.txt content..."
            />
            <p className="text-sm text-gray-600 mt-2">
              This content will be served at /robots.txt. Leave empty to use default restrictive robots.txt.
            </p>
          </div>

          {isEditing && (
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={updateMutation.isPending}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {updateMutation.isPending ? 'Saving...' : 'Save Settings'}
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default SEOPrivacySettings;
