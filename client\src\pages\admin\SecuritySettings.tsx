import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Separator } from '@/components/ui/separator';
import { Shield, AlertTriangle, Smartphone, Globe } from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';

interface SecuritySettings {
  loginAlertsEnabled: boolean;
  ipRestrictionEnabled: boolean;
  telegram2FAEnabled: boolean;
  userTwoFactorEnabled?: boolean;
  username?: string;
}

export default function SecuritySettings() {
  const [settings, setSettings] = useState<SecuritySettings>({
    loginAlertsEnabled: false,
    ipRestrictionEnabled: false,
    telegram2FAEnabled: false,
    userTwoFactorEnabled: false
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/security-settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      } else {
        throw new Error('Failed to fetch settings');
      }
    } catch (error) {
      console.error('Error fetching security settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load security settings',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (updates: Partial<SecuritySettings>) => {
    setSaving(true);
    try {
      const response = await fetch('/api/admin/security-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(prev => ({ ...prev, ...data.settings }));
        toast({
          title: 'Success',
          description: 'Security settings updated successfully'
        });
      } else {
        throw new Error('Failed to update settings');
      }
    } catch (error) {
      console.error('Error updating security settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update security settings',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const toggleUserTwoFactor = async () => {
    try {
      const response = await fetch('/api/admin/toggle-2fa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ enabled: !settings.userTwoFactorEnabled })
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(prev => ({ ...prev, userTwoFactorEnabled: data.twoFactorEnabled }));
        toast({
          title: 'Success',
          description: data.message
        });
      } else {
        throw new Error('Failed to toggle 2FA');
      }
    } catch (error) {
      console.error('Error toggling 2FA:', error);
      toast({
        title: 'Error',
        description: 'Failed to update 2FA settings',
        variant: 'destructive'
      });
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="space-y-6">
          <Card>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">

      {/* System Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>System Security Features</span>
          </CardTitle>
          <CardDescription>
            Configure system-wide security features for all admin users
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Login Alerts */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="space-y-0.5 flex-1">
              <Label className="text-base font-medium">Login Alerts</Label>
              <p className="text-sm text-muted-foreground">
                Get Telegram notifications for all login attempts (success and failed)
              </p>
            </div>
            <Switch
              checked={settings.loginAlertsEnabled}
              onCheckedChange={(checked) =>
                updateSettings({ loginAlertsEnabled: checked })
              }
              disabled={saving}
            />
          </div>

          <Separator />

          {/* IP Restriction */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="space-y-0.5 flex-1">
              <Label className="text-base font-medium flex items-center space-x-2">
                <Globe className="h-4 w-4" />
                <span>IP Restriction</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Require Telegram approval for login attempts from new IP addresses
              </p>
            </div>
            <Switch
              checked={settings.ipRestrictionEnabled}
              onCheckedChange={(checked) =>
                updateSettings({ ipRestrictionEnabled: checked })
              }
              disabled={saving}
            />
          </div>

          <Separator />

          {/* Telegram 2FA */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="space-y-0.5 flex-1">
              <Label className="text-base font-medium flex items-center space-x-2">
                <Smartphone className="h-4 w-4" />
                <span>Telegram Two-Factor Authentication</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Enable Telegram-based 2FA for users who have it enabled
              </p>
            </div>
            <Switch
              checked={settings.telegram2FAEnabled}
              onCheckedChange={(checked) =>
                updateSettings({ telegram2FAEnabled: checked })
              }
              disabled={saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* User 2FA Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Personal Two-Factor Authentication</CardTitle>
          <CardDescription>
            Configure 2FA for your account ({settings.username})
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="space-y-0.5 flex-1">
              <Label className="text-base font-medium">Enable 2FA for Your Account</Label>
              <p className="text-sm text-muted-foreground">
                Require Telegram approval when you login (only works if system 2FA is enabled)
              </p>
            </div>
            <Switch
              checked={settings.userTwoFactorEnabled || false}
              onCheckedChange={toggleUserTwoFactor}
              disabled={saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* Current Settings Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Current Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg bg-slate-50">
              <AlertTriangle className={`h-8 w-8 mx-auto mb-2 ${settings.loginAlertsEnabled ? 'text-green-500' : 'text-gray-400'}`} />
              <p className="font-medium">Login Alerts</p>
              <p className="text-sm text-muted-foreground">
                {settings.loginAlertsEnabled ? 'Enabled' : 'Disabled'}
              </p>
            </div>
            <div className="text-center p-4 border rounded-lg bg-slate-50">
              <Globe className={`h-8 w-8 mx-auto mb-2 ${settings.ipRestrictionEnabled ? 'text-green-500' : 'text-gray-400'}`} />
              <p className="font-medium">IP Restriction</p>
              <p className="text-sm text-muted-foreground">
                {settings.ipRestrictionEnabled ? 'Enabled' : 'Disabled'}
              </p>
            </div>
            <div className="text-center p-4 border rounded-lg bg-slate-50 sm:col-span-2 lg:col-span-1">
              <Smartphone className={`h-8 w-8 mx-auto mb-2 ${settings.telegram2FAEnabled ? 'text-green-500' : 'text-gray-400'}`} />
              <p className="font-medium">Telegram 2FA</p>
              <p className="text-sm text-muted-foreground">
                {settings.telegram2FAEnabled ? 'Enabled' : 'Disabled'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {saving && (
        <div className="fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg">
          Saving settings...
        </div>
      )}
      </div>
    </AdminLayout>
  );
}
