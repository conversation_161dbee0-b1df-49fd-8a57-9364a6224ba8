import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import SQLCipher from '@journeyapps/sqlcipher';
import * as schema from '../shared/schema';
import { createSecureDatabase } from './config/database-security';

// Initialize SQLite database with security enhancements
const sqlite = createSecureDatabase();

// Create Drizzle instance with proper typing
export const db = drizzle(sqlite as Database.Database, { schema });

// Export for use in other modules
export default db;
