/**
 * Email utility functions for consistent email handling across the application
 */

/**
 * Normalizes an email address to lowercase for case-insensitive operations
 * @param email The email address to normalize
 * @returns The normalized email address in lowercase
 */
export function normalizeEmail(email: string): string {
  if (!email || typeof email !== 'string') {
    return '';
  }
  return email.trim().toLowerCase();
}

/**
 * Compares two email addresses in a case-insensitive manner
 * @param email1 First email address
 * @param email2 Second email address
 * @returns True if emails are the same (case-insensitive), false otherwise
 */
export function emailsEqual(email1: string, email2: string): boolean {
  return normalizeEmail(email1) === normalizeEmail(email2);
}

/**
 * Validates if an email address is properly formatted
 * @param email The email address to validate
 * @returns True if email format is valid, false otherwise
 */
export function isValidEmailFormat(email: string): boolean {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
}

/**
 * Normalizes and validates an email address
 * @param email The email address to process
 * @returns Object with normalized email and validation status
 */
export function processEmail(email: string): { 
  normalizedEmail: string; 
  isValid: boolean; 
  error?: string 
} {
  if (!email || typeof email !== 'string') {
    return {
      normalizedEmail: '',
      isValid: false,
      error: 'Email is required'
    };
  }

  const normalizedEmail = normalizeEmail(email);
  
  if (!isValidEmailFormat(normalizedEmail)) {
    return {
      normalizedEmail,
      isValid: false,
      error: 'Invalid email format'
    };
  }

  return {
    normalizedEmail,
    isValid: true
  };
}
