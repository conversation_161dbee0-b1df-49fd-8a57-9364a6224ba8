import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Card, CardContent, CardDescription, CardFooter, 
  CardHeader, CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Table, TableBody, TableCell, TableHead, TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog, DialogContent, DialogDescription, DialogFooter,
  DialogHeader, DialogTitle, DialogTrigger,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel, SelectSeparator } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import AdminLayout from '@/components/admin/AdminLayout';
import { PlusCircle, Pencil, Trash2, Image as ImageIcon } from 'lucide-react';
import type { Product } from '@shared/schema';
import ImageSelector from '@/components/admin/ImageSelector';

export default function ProductsPage() {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [newProduct, setNewProduct] = useState({
    name: '',
    description: '',
    price: '',
    imageUrl: '',
    active: true,
    paymentMethod: 'manual-payment',
    customPaymentLinkId: '',
    trialCustomPaymentLinkId: '',
    embedCodeId: ''
  });
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const { data: products = [], isLoading } = useQuery<Product[]>({
    queryKey: ['/api/admin/products'],
    retry: false
  });

  // Fetch payment configuration
  const { data: paymentConfig } = useQuery({
    queryKey: ['/api/admin/payment-config'],
    retry: false
  });
  
  const createProductMutation = useMutation({
    mutationFn: (data: typeof newProduct) => 
      apiRequest('/api/admin/products', 'POST', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/products'] });
      toast({
        title: "Product created",
        description: "The product has been added successfully."
      });
      setIsAddDialogOpen(false);
      setNewProduct({
        name: '',
        description: '',
        price: '',
        imageUrl: '',
        active: true,
        paymentMethod: 'manual-payment',
        customPaymentLinkId: '',
        trialCustomPaymentLinkId: '',
        embedCodeId: ''
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to create product: ${error.message}`,
        variant: "destructive"
      });
    }
  });
  
  const updateProductMutation = useMutation({
    mutationFn: (data: Product) => 
      apiRequest(`/api/admin/products/${data.id}`, 'PATCH', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/products'] });
      toast({
        title: "Product updated",
        description: "The product has been updated successfully."
      });
      setIsEditDialogOpen(false);
      setSelectedProduct(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update product: ${error.message}`,
        variant: "destructive"
      });
    }
  });
  
  const deleteProductMutation = useMutation({
    mutationFn: (id: number) => 
      apiRequest(`/api/admin/products/${id}`, 'DELETE'),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/products'] });
      toast({
        title: "Product deleted",
        description: "The product has been deleted successfully."
      });
      setIsDeleteDialogOpen(false);
      setSelectedProduct(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to delete product: ${error.message}`,
        variant: "destructive"
      });
    }
  });
  
  const handleAddSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createProductMutation.mutate(newProduct);
  };
  
  const handleEditSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedProduct) {
      updateProductMutation.mutate(selectedProduct);
    }
  };
  
  const handleDeleteConfirm = () => {
    if (selectedProduct) {
      deleteProductMutation.mutate(selectedProduct.id);
    }
  };
  
  const openEditDialog = (product: Product) => {
    setSelectedProduct(product);
    setIsEditDialogOpen(true);
  };
  
  const openDeleteDialog = (product: Product) => {
    setSelectedProduct(product);
    setIsDeleteDialogOpen(true);
  };
  
  return (
    <AdminLayout>
      <Card className="shadow-lg">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold">Products</CardTitle>
            <CardDescription>
              Manage your digital products
            </CardDescription>
          </div>
          <Button onClick={() => setIsAddDialogOpen(true)} className="ml-auto">
            <PlusCircle className="mr-2 h-4 w-4" /> Add Product
          </Button>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Price</TableHead>
                  <TableHead>Payment Method</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {products.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                      No products found. Add your first product to get started.
                    </TableCell>
                  </TableRow>
                ) : (
                  products.map((product: Product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        <div className="font-medium">{product.name}</div>
                      </TableCell>
                      <TableCell>
                        ${parseFloat(product.price.toString()).toFixed(2)}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {product.paymentMethod === 'manual-payment' && 'Manual Payment'}
                          {product.paymentMethod === 'custom-link' && 'Custom Link'}
                          {product.paymentMethod === 'trial-custom-link' && 'Trial Custom Link'}
                          {product.paymentMethod === 'embed-code' && 'Embed Code'}
                          {!product.paymentMethod && 'Manual Payment'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {product.active ? (
                          <Badge>Active</Badge>
                        ) : (
                          <Badge variant="outline">Inactive</Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => openEditDialog(product)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => openDeleteDialog(product)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
      
      {/* Add Product Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Product</DialogTitle>
            <DialogDescription>
              Add a new digital product to your store
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={newProduct.name}
                  onChange={(e) => setNewProduct({ ...newProduct, name: e.target.value })}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="price" className="text-right">
                  Price ($)
                </Label>
                <Input
                  id="price"
                  type="number"
                  min="0.01"
                  step="0.01"
                  value={newProduct.price}
                  onChange={(e) => setNewProduct({ ...newProduct, price: e.target.value })}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label className="text-right mt-2">
                  <span className="mr-1">Image</span>
                  <ImageIcon className="inline-block h-4 w-4" />
                </Label>
                <div className="col-span-3">
                  <ImageSelector
                    initialUrl={newProduct.imageUrl}
                    onImageSelected={(url) => setNewProduct({ ...newProduct, imageUrl: url })}
                    label="Product Image"
                  />
                </div>
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={newProduct.description}
                  onChange={(e) => setNewProduct({ ...newProduct, description: e.target.value })}
                  className="col-span-3"
                  rows={4}
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="active" className="text-right">
                  Active
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="active"
                    checked={newProduct.active}
                    onCheckedChange={(checked) => setNewProduct({ ...newProduct, active: checked })}
                  />
                  <Label htmlFor="active">
                    {newProduct.active ? 'Product is visible in store' : 'Product is hidden'}
                  </Label>
                </div>
              </div>

              {/* Payment Method Selection */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="payment-method" className="text-right">
                  Payment Method
                </Label>
                <div className="col-span-3">
                  <Select
                    value={newProduct.paymentMethod}
                    onValueChange={(value) => setNewProduct({ ...newProduct, paymentMethod: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Payment Methods</SelectLabel>
                        <SelectItem value="manual-payment">Manual Payment (Email Notification)</SelectItem>
                        <SelectItem value="custom-link">Custom Payment Link</SelectItem>
                        <SelectItem value="trial-custom-link">Trial Custom Payment Link</SelectItem>
                        <SelectItem value="embed-code">External Embed Code</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Payment Configuration Fields */}
              {newProduct.paymentMethod === 'custom-link' && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="custom-payment-link" className="text-right">
                    Custom Payment Link
                  </Label>
                  <div className="col-span-3">
                    <Select
                      value={newProduct.customPaymentLinkId}
                      onValueChange={(value) => setNewProduct({ ...newProduct, customPaymentLinkId: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select custom payment link" />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentConfig?.customPaymentLinks?.map((link: any) => (
                          <SelectItem key={link.id} value={link.id.toString()}>
                            {link.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {newProduct.paymentMethod === 'trial-custom-link' && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="trial-custom-payment-link" className="text-right">
                    Trial Payment Link
                  </Label>
                  <div className="col-span-3">
                    <Select
                      value={newProduct.trialCustomPaymentLinkId}
                      onValueChange={(value) => setNewProduct({ ...newProduct, trialCustomPaymentLinkId: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select trial payment link" />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentConfig?.trialCustomPaymentLinks?.map((link: any) => (
                          <SelectItem key={link.id} value={link.id.toString()}>
                            {link.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}

              {newProduct.paymentMethod === 'embed-code' && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="embed-code" className="text-right">
                    Embed Code
                  </Label>
                  <div className="col-span-3">
                    <Select
                      value={newProduct.embedCodeId}
                      onValueChange={(value) => setNewProduct({ ...newProduct, embedCodeId: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select embed code" />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentConfig?.embedCodes?.map((code: any) => (
                          <SelectItem key={code.id} value={code.id.toString()}>
                            {code.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={createProductMutation.isPending}
              >
                {createProductMutation.isPending ? 'Creating...' : 'Create Product'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Edit Product Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Product</DialogTitle>
            <DialogDescription>
              Update the product details
            </DialogDescription>
          </DialogHeader>
          {selectedProduct && (
            <form onSubmit={handleEditSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="edit-name"
                    value={selectedProduct.name}
                    onChange={(e) => setSelectedProduct({ ...selectedProduct, name: e.target.value })}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-price" className="text-right">
                    Price ($)
                  </Label>
                  <Input
                    id="edit-price"
                    type="number"
                    min="0.01"
                    step="0.01"
                    value={selectedProduct.price}
                    onChange={(e) => setSelectedProduct({ ...selectedProduct, price: e.target.value })}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label className="text-right mt-2">
                    <span className="mr-1">Image</span>
                    <ImageIcon className="inline-block h-4 w-4" />
                  </Label>
                  <div className="col-span-3">
                    <ImageSelector
                      initialUrl={selectedProduct.imageUrl}
                      onImageSelected={(url) => setSelectedProduct({ ...selectedProduct, imageUrl: url })}
                      label="Product Image"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-4 items-start gap-4">
                  <Label htmlFor="edit-description" className="text-right">
                    Description
                  </Label>
                  <Textarea
                    id="edit-description"
                    value={selectedProduct.description}
                    onChange={(e) => setSelectedProduct({ ...selectedProduct, description: e.target.value })}
                    className="col-span-3"
                    rows={4}
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-active" className="text-right">
                    Active
                  </Label>
                  <div className="col-span-3 flex items-center space-x-2">
                    <Switch
                      id="edit-active"
                      checked={selectedProduct.active}
                      onCheckedChange={(checked) => setSelectedProduct({ ...selectedProduct, active: checked })}
                    />
                    <Label htmlFor="edit-active">
                      {selectedProduct.active ? 'Product is visible in store' : 'Product is hidden'}
                    </Label>
                  </div>
                </div>

                {/* Payment Method Selection */}
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-payment-method" className="text-right">
                    Payment Method
                  </Label>
                  <div className="col-span-3">
                    <Select
                      value={selectedProduct.paymentMethod || 'manual-payment'}
                      onValueChange={(value) => setSelectedProduct({ ...selectedProduct, paymentMethod: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectGroup>
                          <SelectLabel>Payment Methods</SelectLabel>
                          <SelectItem value="manual-payment">Manual Payment (Email Notification)</SelectItem>
                          <SelectItem value="custom-link">Custom Payment Link</SelectItem>
                          <SelectItem value="trial-custom-link">Trial Custom Payment Link</SelectItem>
                          <SelectItem value="embed-code">External Embed Code</SelectItem>
                        </SelectGroup>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Payment Configuration Fields */}
                {(selectedProduct.paymentMethod || 'manual-payment') === 'custom-link' && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-custom-payment-link" className="text-right">
                      Custom Payment Link
                    </Label>
                    <div className="col-span-3">
                      <Select
                        value={selectedProduct.customPaymentLinkId || ''}
                        onValueChange={(value) => setSelectedProduct({ ...selectedProduct, customPaymentLinkId: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select custom payment link" />
                        </SelectTrigger>
                        <SelectContent>
                          {paymentConfig?.customPaymentLinks?.map((link: any) => (
                            <SelectItem key={link.id} value={link.id.toString()}>
                              {link.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {(selectedProduct.paymentMethod || 'manual-payment') === 'trial-custom-link' && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-trial-custom-payment-link" className="text-right">
                      Trial Payment Link
                    </Label>
                    <div className="col-span-3">
                      <Select
                        value={selectedProduct.trialCustomPaymentLinkId || ''}
                        onValueChange={(value) => setSelectedProduct({ ...selectedProduct, trialCustomPaymentLinkId: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select trial payment link" />
                        </SelectTrigger>
                        <SelectContent>
                          {paymentConfig?.trialCustomPaymentLinks?.map((link: any) => (
                            <SelectItem key={link.id} value={link.id.toString()}>
                              {link.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {(selectedProduct.paymentMethod || 'manual-payment') === 'embed-code' && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="edit-embed-code" className="text-right">
                      Embed Code
                    </Label>
                    <div className="col-span-3">
                      <Select
                        value={selectedProduct.embedCodeId || ''}
                        onValueChange={(value) => setSelectedProduct({ ...selectedProduct, embedCodeId: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select embed code" />
                        </SelectTrigger>
                        <SelectContent>
                          {paymentConfig?.embedCodes?.map((code: any) => (
                            <SelectItem key={code.id} value={code.id.toString()}>
                              {code.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </div>
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={updateProductMutation.isPending}
                >
                  {updateProductMutation.isPending ? 'Saving...' : 'Save Changes'}
                </Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this product? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              type="button"
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleteProductMutation.isPending}
            >
              {deleteProductMutation.isPending ? 'Deleting...' : 'Delete Product'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
}