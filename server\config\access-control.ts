import { Request, Response, NextFunction } from 'express';

// Define user roles and permissions
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  READONLY = 'readonly'
}

export enum Permission {
  READ_USERS = 'read:users',
  WRITE_USERS = 'write:users',
  DELETE_USERS = 'delete:users',
  READ_PRODUCTS = 'read:products',
  WRITE_PRODUCTS = 'write:products',
  DELETE_PRODUCTS = 'delete:products',
  READ_INVOICES = 'read:invoices',
  WRITE_INVOICES = 'write:invoices',
  DELETE_INVOICES = 'delete:invoices',
  READ_SETTINGS = 'read:settings',
  WRITE_SETTINGS = 'write:settings',
  BACKUP_DATABASE = 'backup:database',
  RESTORE_DATABASE = 'restore:database',
  VIEW_LOGS = 'view:logs',
  MANAGE_SECURITY = 'manage:security'
}

// Role-based permissions mapping
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    Permission.READ_USERS,
    Permission.WRITE_USERS,
    Permission.DELETE_USERS,
    Permission.READ_PRODUCTS,
    Permission.WRITE_PRODUCTS,
    Permission.DELETE_PRODUCTS,
    Permission.READ_INVOICES,
    Permission.WRITE_INVOICES,
    Permission.DELETE_INVOICES,
    Permission.READ_SETTINGS,
    Permission.WRITE_SETTINGS,
    Permission.BACKUP_DATABASE,
    Permission.RESTORE_DATABASE,
    Permission.VIEW_LOGS,
    Permission.MANAGE_SECURITY
  ],
  [UserRole.USER]: [
    Permission.READ_PRODUCTS,
    Permission.READ_INVOICES,
    Permission.WRITE_INVOICES,
    Permission.READ_SETTINGS
  ],
  [UserRole.READONLY]: [
    Permission.READ_PRODUCTS,
    Permission.READ_INVOICES,
    Permission.READ_SETTINGS
  ]
};

// Database operation types
export enum DatabaseOperation {
  SELECT = 'SELECT',
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE'
}

// Table access control
interface TableAccess {
  table: string;
  operations: DatabaseOperation[];
  conditions?: string[];
}

const ROLE_TABLE_ACCESS: Record<UserRole, TableAccess[]> = {
  [UserRole.ADMIN]: [
    { table: '*', operations: [DatabaseOperation.SELECT, DatabaseOperation.INSERT, DatabaseOperation.UPDATE, DatabaseOperation.DELETE] }
  ],
  [UserRole.USER]: [
    { table: 'products', operations: [DatabaseOperation.SELECT] },
    { table: 'invoices', operations: [DatabaseOperation.SELECT, DatabaseOperation.INSERT, DatabaseOperation.UPDATE] },
    { table: 'custom_checkout_pages', operations: [DatabaseOperation.SELECT] },
    { table: 'general_settings', operations: [DatabaseOperation.SELECT] }
  ],
  [UserRole.READONLY]: [
    { table: 'products', operations: [DatabaseOperation.SELECT] },
    { table: 'invoices', operations: [DatabaseOperation.SELECT] },
    { table: 'custom_checkout_pages', operations: [DatabaseOperation.SELECT] },
    { table: 'general_settings', operations: [DatabaseOperation.SELECT] }
  ]
};

// Security context for database operations
export interface SecurityContext {
  userId?: number;
  username?: string;
  role: UserRole;
  permissions: Permission[];
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

// Create security context from request
export function createSecurityContext(req: Request): SecurityContext {
  const role = req.session?.role || UserRole.READONLY;
  const permissions = ROLE_PERMISSIONS[role] || [];
  
  return {
    userId: req.session?.userId,
    username: req.session?.username,
    role,
    permissions,
    sessionId: req.session?.id,
    ipAddress: req.ip,
    userAgent: req.get('User-Agent')
  };
}

// Check if user has specific permission
export function hasPermission(context: SecurityContext, permission: Permission): boolean {
  return context.permissions.includes(permission);
}

// Check if user can perform database operation on table
export function canAccessTable(
  context: SecurityContext, 
  table: string, 
  operation: DatabaseOperation
): boolean {
  const tableAccess = ROLE_TABLE_ACCESS[context.role] || [];
  
  // Check for wildcard access
  const wildcardAccess = tableAccess.find(access => access.table === '*');
  if (wildcardAccess && wildcardAccess.operations.includes(operation)) {
    return true;
  }
  
  // Check for specific table access
  const specificAccess = tableAccess.find(access => access.table === table);
  return specificAccess ? specificAccess.operations.includes(operation) : false;
}

// Middleware to require specific permission
export function requirePermission(permission: Permission) {
  return (req: Request, res: Response, next: NextFunction) => {
    const context = createSecurityContext(req);
    
    if (!hasPermission(context, permission)) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        required: permission,
        userRole: context.role
      });
    }
    
    // Add security context to request
    (req as any).securityContext = context;
    next();
  };
}

// Middleware to require specific role
export function requireRole(role: UserRole) {
  return (req: Request, res: Response, next: NextFunction) => {
    const context = createSecurityContext(req);
    
    if (context.role !== role && context.role !== UserRole.ADMIN) {
      return res.status(403).json({
        error: 'Insufficient role',
        required: role,
        userRole: context.role
      });
    }
    
    (req as any).securityContext = context;
    next();
  };
}

// Database query interceptor for access control
export function validateDatabaseAccess(
  context: SecurityContext,
  query: string,
  table?: string
): { allowed: boolean; reason?: string } {
  // Extract operation type from query
  const operation = extractOperationType(query);
  if (!operation) {
    return { allowed: false, reason: 'Unknown database operation' };
  }
  
  // Extract table name if not provided
  const targetTable = table || extractTableName(query);
  if (!targetTable) {
    return { allowed: false, reason: 'Cannot determine target table' };
  }
  
  // Check access permissions
  if (!canAccessTable(context, targetTable, operation)) {
    return { 
      allowed: false, 
      reason: `No ${operation} permission for table ${targetTable}` 
    };
  }
  
  return { allowed: true };
}

// Extract operation type from SQL query
function extractOperationType(query: string): DatabaseOperation | null {
  const normalizedQuery = query.trim().toUpperCase();
  
  if (normalizedQuery.startsWith('SELECT')) return DatabaseOperation.SELECT;
  if (normalizedQuery.startsWith('INSERT')) return DatabaseOperation.INSERT;
  if (normalizedQuery.startsWith('UPDATE')) return DatabaseOperation.UPDATE;
  if (normalizedQuery.startsWith('DELETE')) return DatabaseOperation.DELETE;
  
  return null;
}

// Extract table name from SQL query
function extractTableName(query: string): string | null {
  const normalizedQuery = query.trim().toUpperCase();
  
  // Simple regex patterns for common SQL operations
  const patterns = [
    /SELECT.*FROM\s+(\w+)/,
    /INSERT\s+INTO\s+(\w+)/,
    /UPDATE\s+(\w+)\s+SET/,
    /DELETE\s+FROM\s+(\w+)/
  ];
  
  for (const pattern of patterns) {
    const match = normalizedQuery.match(pattern);
    if (match && match[1]) {
      return match[1].toLowerCase();
    }
  }
  
  return null;
}

// Log security events
export function logSecurityEvent(
  context: SecurityContext,
  event: string,
  details?: any
): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    userId: context.userId,
    username: context.username,
    role: context.role,
    sessionId: context.sessionId,
    ipAddress: context.ipAddress,
    userAgent: context.userAgent,
    details
  };
  
  console.log('[SECURITY]', JSON.stringify(logEntry));
  
  // In production, you might want to send this to a security monitoring service
  // or store in a separate security log database
}
