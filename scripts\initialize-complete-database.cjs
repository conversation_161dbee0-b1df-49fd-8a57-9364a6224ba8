const Database = require('better-sqlite3');
const bcrypt = require('bcrypt');
const path = require('path');

const dbPath = path.join(__dirname, '..', 'app', 'data', 'data.db');
const db = new Database(dbPath);

console.log('🚀 COMPLETE DATABASE INITIALIZATION');
console.log('='.repeat(60));

async function initializeDatabase() {
  try {
    // 1. Create admin user if not exists
    console.log('\n👤 INITIALIZING ADMIN USER...');
    
    const existingUsers = db.prepare('SELECT COUNT(*) as count FROM users').get();
    console.log(`Current users in database: ${existingUsers.count}`);
    
    if (existingUsers.count === 0) {
      console.log('📝 Creating default admin user...');
      
      // Hash the default password
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      const insertUser = db.prepare(`
        INSERT INTO users (username, password, role, two_factor_enabled, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `);
      
      const now = new Date().toISOString();
      insertUser.run('admin', hashedPassword, 'admin', 0, now, now);
      
      console.log('✅ Created admin user: admin/admin123');
      console.log('⚠️ IMPORTANT: Change the default password after first login!');
    } else {
      console.log('✅ Admin user already exists');
    }

    // 2. Initialize general settings if not exists
    console.log('\n⚙️ INITIALIZING GENERAL SETTINGS...');
    
    const existingSettings = db.prepare('SELECT COUNT(*) as count FROM general_settings').get();
    
    if (existingSettings.count === 0) {
      console.log('📝 Creating default general settings...');
      
      const insertSettings = db.prepare(`
        INSERT INTO general_settings (
          site_name, site_description, logo_url, favicon_url, 
          primary_color, secondary_color, footer_text, 
          enable_checkout, enable_custom_checkout, enable_test_mode,
          default_test_customer_enabled, default_test_customer_name, default_test_customer_email,
          email_domain_restriction_enabled, email_domain_restriction_domains,
          seo_privacy_settings, telegram_bot_settings, url_settings,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      const now = new Date().toISOString();
      const seoPrivacySettings = JSON.stringify({
        globalNoIndex: true,
        hideFromSearchEngines: true,
        disableSitemaps: true,
        hideFramework: true,
        customRobotsTxt: "User-agent: *\\nDisallow: /",
        pageIndexingRules: {
          homepage: false,
          checkoutPages: false,
          adminPages: false,
          customPages: false
        },
        privacyHeaders: {
          hideServerInfo: true,
          preventFraming: true,
          disableReferrer: true,
          hideGenerator: true
        }
      });
      
      const telegramBotSettings = JSON.stringify({
        enabled: false,
        botToken: "",
        chatId: "",
        notifications: {
          newOrders: true,
          systemAlerts: true,
          dailyReports: false
        }
      });
      
      const urlSettings = JSON.stringify({
        customDomain: "",
        useCustomDomain: false,
        redirectToCustom: false,
        sslEnabled: true
      });
      
      insertSettings.run(
        'PayPal Invoice Generator',
        'Professional invoice generation with PayPal integration',
        '',
        '',
        '#6366f1',
        '#4f46e5',
        'Powered by PayPal Invoice Generator',
        1, 1, 0, // enable_checkout, enable_custom_checkout, enable_test_mode
        1, 'Test Customer', '<EMAIL>', // test customer settings
        0, 'gmail.com, hotmail.com, yahoo.com', // email domain restriction
        seoPrivacySettings,
        telegramBotSettings,
        urlSettings,
        now, now
      );
      
      console.log('✅ Created default general settings');
    } else {
      console.log('✅ General settings already exist');
    }

    // 3. Initialize sample products if not exists
    console.log('\n📦 INITIALIZING SAMPLE PRODUCTS...');
    
    const existingProducts = db.prepare('SELECT COUNT(*) as count FROM products').get();
    
    if (existingProducts.count === 0) {
      console.log('📝 Creating sample products...');
      
      const insertProduct = db.prepare(`
        INSERT INTO products (name, description, price, image_url, active, payment_method)
        VALUES (?, ?, ?, ?, ?, ?)
      `);
      
      const sampleProducts = [
        {
          name: 'Premium Template Package',
          description: 'Complete collection of premium app templates with source code',
          price: '99.00',
          image_url: '',
          active: 1,
          payment_method: 'paypal'
        },
        {
          name: 'UI Component Library',
          description: 'Comprehensive UI component library for modern applications',
          price: '49.00',
          image_url: '',
          active: 1,
          payment_method: 'paypal'
        },
        {
          name: 'Design System Pro',
          description: 'Professional design system with guidelines and assets',
          price: '149.00',
          image_url: '',
          active: 1,
          payment_method: 'paypal'
        }
      ];
      
      sampleProducts.forEach(product => {
        insertProduct.run(
          product.name,
          product.description,
          product.price,
          product.image_url,
          product.active,
          product.payment_method
        );
      });
      
      console.log(`✅ Created ${sampleProducts.length} sample products`);
    } else {
      console.log('✅ Products already exist');
    }

    // 4. Verify homepage configuration exists (already created by previous script)
    console.log('\n🏠 VERIFYING HOMEPAGE CONFIGURATION...');
    
    const homepageConfigs = db.prepare('SELECT COUNT(*) as count FROM homepage_config').get();
    console.log(`Homepage configurations: ${homepageConfigs.count > 0 ? '✅ EXISTS' : '❌ MISSING'}`);

    // 5. Initialize email templates if not exists
    console.log('\n📧 INITIALIZING EMAIL TEMPLATES...');
    
    const existingTemplates = db.prepare('SELECT COUNT(*) as count FROM email_templates').get();
    
    if (existingTemplates.count === 0) {
      console.log('📝 Creating default email templates...');
      
      const insertTemplate = db.prepare(`
        INSERT INTO email_templates (
          template_id, name, description, subject, html_content, text_content, 
          category, is_default, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      
      const now = new Date().toISOString();
      
      const templates = [
        {
          template_id: 'invoice_created',
          name: 'Invoice Created',
          description: 'Sent when a new invoice is created',
          subject: 'New Invoice Created - {{invoice_number}}',
          html_content: '<h1>Invoice Created</h1><p>Your invoice {{invoice_number}} has been created.</p>',
          text_content: 'Invoice Created\\n\\nYour invoice {{invoice_number}} has been created.',
          category: 'invoice',
          is_default: 1
        },
        {
          template_id: 'payment_received',
          name: 'Payment Received',
          description: 'Sent when payment is received',
          subject: 'Payment Received - {{invoice_number}}',
          html_content: '<h1>Payment Received</h1><p>Payment for invoice {{invoice_number}} has been received.</p>',
          text_content: 'Payment Received\\n\\nPayment for invoice {{invoice_number}} has been received.',
          category: 'payment',
          is_default: 1
        }
      ];
      
      templates.forEach(template => {
        insertTemplate.run(
          template.template_id,
          template.name,
          template.description,
          template.subject,
          template.html_content,
          template.text_content,
          template.category,
          template.is_default,
          now, now
        );
      });
      
      console.log(`✅ Created ${templates.length} default email templates`);
    } else {
      console.log('✅ Email templates already exist');
    }

    console.log('\n' + '='.repeat(60));
    console.log('🎉 DATABASE INITIALIZATION COMPLETED SUCCESSFULLY!');
    console.log('='.repeat(60));
    
    // Final verification
    const finalStats = {
      users: db.prepare('SELECT COUNT(*) as count FROM users').get().count,
      products: db.prepare('SELECT COUNT(*) as count FROM products').get().count,
      settings: db.prepare('SELECT COUNT(*) as count FROM general_settings').get().count,
      homepage: db.prepare('SELECT COUNT(*) as count FROM homepage_config').get().count,
      templates: db.prepare('SELECT COUNT(*) as count FROM email_templates').get().count
    };
    
    console.log('📊 FINAL DATABASE STATISTICS:');
    console.log(`   Users: ${finalStats.users}`);
    console.log(`   Products: ${finalStats.products}`);
    console.log(`   Settings: ${finalStats.settings}`);
    console.log(`   Homepage Configs: ${finalStats.homepage}`);
    console.log(`   Email Templates: ${finalStats.templates}`);
    
    const totalRecords = Object.values(finalStats).reduce((sum, count) => sum + count, 0);
    console.log(`   Total Records: ${totalRecords}`);
    
    if (totalRecords > 0) {
      console.log('\n✅ DATABASE IS READY FOR USE!');
      console.log('🔐 Default admin credentials: admin / admin123');
      console.log('⚠️  IMPORTANT: Change default password after first login!');
    } else {
      console.log('\n❌ DATABASE INITIALIZATION FAILED - NO DATA CREATED');
    }

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    throw error;
  }
}

// Run initialization
initializeDatabase()
  .then(() => {
    console.log('\n🏁 Initialization script completed');
  })
  .catch((error) => {
    console.error('\n💥 Initialization script failed:', error);
    process.exit(1);
  })
  .finally(() => {
    db.close();
  });
