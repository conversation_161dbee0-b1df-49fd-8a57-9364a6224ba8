import Database from 'better-sqlite3';
import SQLCipher from '@journeyapps/sqlcipher';
import { EventEmitter } from 'events';
import { securityMonitor, SecurityEventType, SecuritySeverity } from './security-monitoring';

// Connection pool configuration
interface ConnectionPoolConfig {
  maxConnections: number;
  minConnections: number;
  connectionTimeout: number;
  idleTimeout: number;
  healthCheckInterval: number;
  retryAttempts: number;
  retryDelay: number;
  enableHealthMonitoring: boolean;
  enableMetrics: boolean;
}

// Connection wrapper interface
interface DatabaseConnection {
  id: string;
  database: Database.Database | SQLCipher.Database;
  createdAt: Date;
  lastUsed: Date;
  isActive: boolean;
  isHealthy: boolean;
  queryCount: number;
  errorCount: number;
}

// Connection pool metrics
interface ConnectionPoolMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  unhealthyConnections: number;
  totalQueries: number;
  totalErrors: number;
  averageQueryTime: number;
  connectionCreations: number;
  connectionDestructions: number;
  healthCheckFailures: number;
}

// Default configuration
const DEFAULT_CONFIG: ConnectionPoolConfig = {
  maxConnections: parseInt(process.env.MAX_DB_CONNECTIONS || '10'),
  minConnections: parseInt(process.env.MIN_DB_CONNECTIONS || '2'),
  connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '30000'), // 30 seconds
  idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '300000'), // 5 minutes
  healthCheckInterval: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL || '60000'), // 1 minute
  retryAttempts: parseInt(process.env.DB_RETRY_ATTEMPTS || '3'),
  retryDelay: parseInt(process.env.DB_RETRY_DELAY || '1000'), // 1 second
  enableHealthMonitoring: process.env.ENABLE_DB_HEALTH_MONITORING !== 'false',
  enableMetrics: process.env.ENABLE_DB_METRICS !== 'false'
};

export class DatabaseConnectionManager extends EventEmitter {
  private config: ConnectionPoolConfig;
  private connections: Map<string, DatabaseConnection> = new Map();
  private availableConnections: string[] = [];
  private waitingQueue: Array<{
    resolve: (connection: DatabaseConnection) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }> = [];
  private healthCheckTimer: NodeJS.Timeout | null = null;
  private metrics: ConnectionPoolMetrics;
  private dbPath: string;
  private encryptionKey: string;
  private useSQLCipher: boolean;

  constructor(
    dbPath: string,
    encryptionKey: string,
    useSQLCipher: boolean = true,
    config: Partial<ConnectionPoolConfig> = {}
  ) {
    super();
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.dbPath = dbPath;
    this.encryptionKey = encryptionKey;
    this.useSQLCipher = useSQLCipher;
    
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      unhealthyConnections: 0,
      totalQueries: 0,
      totalErrors: 0,
      averageQueryTime: 0,
      connectionCreations: 0,
      connectionDestructions: 0,
      healthCheckFailures: 0
    };

    this.initialize();
  }

  // Initialize connection pool
  private async initialize(): Promise<void> {
    try {
      console.log('🔗 Initializing database connection pool...');
      
      // Create minimum number of connections
      for (let i = 0; i < this.config.minConnections; i++) {
        await this.createConnection();
      }

      // Start health monitoring
      if (this.config.enableHealthMonitoring) {
        this.startHealthMonitoring();
      }

      console.log(`✅ Connection pool initialized with ${this.connections.size} connections`);
      
      // Log initialization event
      securityMonitor.logSecurityEvent(
        SecurityEventType.LOGIN_SUCCESS, // Using this as closest match for connection pool init
        {
          userId: undefined,
          username: 'system',
          role: 'system' as any,
          permissions: [],
          ipAddress: 'localhost'
        },
        {
          action: 'CONNECTION_POOL_INITIALIZED',
          totalConnections: this.connections.size,
          maxConnections: this.config.maxConnections,
          useSQLCipher: this.useSQLCipher
        },
        SecuritySeverity.LOW
      );

    } catch (error) {
      console.error('❌ Failed to initialize connection pool:', error);
      throw error;
    }
  }

  // Create a new database connection
  private async createConnection(): Promise<DatabaseConnection> {
    const connectionId = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      let database: Database.Database | SQLCipher.Database;

      if (this.useSQLCipher) {
        database = new SQLCipher(this.dbPath);
        
        // Set encryption key and secure pragmas
        (database as SQLCipher.Database).pragma(`key = '${this.encryptionKey}'`);
        (database as SQLCipher.Database).pragma('cipher_page_size = 4096');
        (database as SQLCipher.Database).pragma('kdf_iter = 256000');
        (database as SQLCipher.Database).pragma('cipher_hmac_algorithm = HMAC_SHA512');
        (database as SQLCipher.Database).pragma('cipher_kdf_algorithm = PBKDF2_HMAC_SHA512');
        
        // Verify encryption is working
        await this.verifyEncryption(database as SQLCipher.Database);
      } else {
        database = new Database(this.dbPath);
      }

      // Set common pragmas
      database.pragma('journal_mode = WAL');
      database.pragma('foreign_keys = ON');
      database.pragma('secure_delete = ON');
      database.pragma('auto_vacuum = FULL');

      const connection: DatabaseConnection = {
        id: connectionId,
        database,
        createdAt: new Date(),
        lastUsed: new Date(),
        isActive: false,
        isHealthy: true,
        queryCount: 0,
        errorCount: 0
      };

      this.connections.set(connectionId, connection);
      this.availableConnections.push(connectionId);
      this.metrics.connectionCreations++;
      this.updateMetrics();

      console.log(`🔗 Created database connection: ${connectionId}`);
      return connection;

    } catch (error) {
      console.error(`❌ Failed to create connection ${connectionId}:`, error);
      
      // Log connection creation failure
      securityMonitor.logSecurityEvent(
        SecurityEventType.DATABASE_ERROR,
        {
          userId: undefined,
          username: 'system',
          role: 'system' as any,
          permissions: [],
          ipAddress: 'localhost'
        },
        {
          action: 'CONNECTION_CREATION_FAILED',
          connectionId,
          error: error.message,
          useSQLCipher: this.useSQLCipher
        },
        SecuritySeverity.HIGH
      );

      throw error;
    }
  }

  // Verify SQLCipher encryption is working
  private async verifyEncryption(database: SQLCipher.Database): Promise<void> {
    try {
      // Test that we can read from the database with encryption
      const result = database.pragma('cipher_version');
      if (!result || result.length === 0) {
        throw new Error('SQLCipher encryption verification failed');
      }
      
      // Test basic query to ensure database is accessible
      database.prepare('SELECT 1 as test').get();
      
    } catch (error) {
      throw new Error(`SQLCipher encryption verification failed: ${error.message}`);
    }
  }

  // Get a connection from the pool
  async getConnection(): Promise<DatabaseConnection> {
    return new Promise((resolve, reject) => {
      // Check if there's an available connection
      if (this.availableConnections.length > 0) {
        const connectionId = this.availableConnections.shift()!;
        const connection = this.connections.get(connectionId);
        
        if (connection && connection.isHealthy) {
          connection.isActive = true;
          connection.lastUsed = new Date();
          this.updateMetrics();
          resolve(connection);
          return;
        }
      }

      // Check if we can create a new connection
      if (this.connections.size < this.config.maxConnections) {
        this.createConnection()
          .then(connection => {
            connection.isActive = true;
            this.updateMetrics();
            resolve(connection);
          })
          .catch(reject);
        return;
      }

      // Add to waiting queue with timeout
      const timeout = setTimeout(() => {
        const index = this.waitingQueue.findIndex(item => item.resolve === resolve);
        if (index !== -1) {
          this.waitingQueue.splice(index, 1);
          reject(new Error('Connection timeout: No available connections'));
        }
      }, this.config.connectionTimeout);

      this.waitingQueue.push({ resolve, reject, timeout });
    });
  }

  // Release a connection back to the pool
  releaseConnection(connection: DatabaseConnection): void {
    if (!this.connections.has(connection.id)) {
      console.warn(`⚠️ Attempted to release unknown connection: ${connection.id}`);
      return;
    }

    connection.isActive = false;
    connection.lastUsed = new Date();

    // Check if there are waiting requests
    if (this.waitingQueue.length > 0) {
      const waiting = this.waitingQueue.shift()!;
      clearTimeout(waiting.timeout);
      connection.isActive = true;
      waiting.resolve(connection);
    } else {
      this.availableConnections.push(connection.id);
    }

    this.updateMetrics();
  }

  // Execute query with automatic connection management
  async executeQuery<T>(
    queryFn: (db: Database.Database | SQLCipher.Database) => T,
    retryCount: number = 0
  ): Promise<T> {
    let connection: DatabaseConnection | null = null;
    
    try {
      connection = await this.getConnection();
      const startTime = Date.now();
      
      const result = queryFn(connection.database);
      
      const executionTime = Date.now() - startTime;
      connection.queryCount++;
      this.metrics.totalQueries++;
      
      // Update average query time
      this.metrics.averageQueryTime = 
        (this.metrics.averageQueryTime * (this.metrics.totalQueries - 1) + executionTime) / 
        this.metrics.totalQueries;

      return result;

    } catch (error) {
      if (connection) {
        connection.errorCount++;
        this.metrics.totalErrors++;
      }

      // Retry logic
      if (retryCount < this.config.retryAttempts) {
        console.warn(`⚠️ Query failed, retrying (${retryCount + 1}/${this.config.retryAttempts}):`, error.message);
        
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        return this.executeQuery(queryFn, retryCount + 1);
      }

      console.error('❌ Query failed after all retries:', error);
      throw error;

    } finally {
      if (connection) {
        this.releaseConnection(connection);
      }
    }
  }

  // Start health monitoring
  private startHealthMonitoring(): void {
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);

    console.log(`💓 Started health monitoring (interval: ${this.config.healthCheckInterval}ms)`);
  }

  // Perform health check on all connections
  private async performHealthCheck(): Promise<void> {
    const healthCheckPromises = Array.from(this.connections.values()).map(async (connection) => {
      try {
        // Simple health check query
        connection.database.prepare('SELECT 1 as health_check').get();
        
        if (!connection.isHealthy) {
          connection.isHealthy = true;
          console.log(`✅ Connection ${connection.id} recovered`);
        }

      } catch (error) {
        if (connection.isHealthy) {
          connection.isHealthy = false;
          this.metrics.healthCheckFailures++;
          
          console.error(`❌ Connection ${connection.id} health check failed:`, error.message);
          
          // Log health check failure
          securityMonitor.logSecurityEvent(
            SecurityEventType.DATABASE_ERROR,
            {
              userId: undefined,
              username: 'system',
              role: 'system' as any,
              permissions: [],
              ipAddress: 'localhost'
            },
            {
              action: 'CONNECTION_HEALTH_CHECK_FAILED',
              connectionId: connection.id,
              error: error.message
            },
            SecuritySeverity.MEDIUM
          );
        }
      }
    });

    await Promise.all(healthCheckPromises);
    this.updateMetrics();
    
    // Clean up idle connections
    this.cleanupIdleConnections();
  }

  // Clean up idle connections
  private cleanupIdleConnections(): void {
    const now = Date.now();
    const connectionsToRemove: string[] = [];

    for (const [connectionId, connection] of this.connections) {
      const idleTime = now - connection.lastUsed.getTime();
      
      if (!connection.isActive && 
          idleTime > this.config.idleTimeout && 
          this.connections.size > this.config.minConnections) {
        connectionsToRemove.push(connectionId);
      }
    }

    for (const connectionId of connectionsToRemove) {
      this.destroyConnection(connectionId);
    }
  }

  // Destroy a connection
  private destroyConnection(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return;
    }

    try {
      connection.database.close();
      this.connections.delete(connectionId);
      
      // Remove from available connections
      const index = this.availableConnections.indexOf(connectionId);
      if (index !== -1) {
        this.availableConnections.splice(index, 1);
      }

      this.metrics.connectionDestructions++;
      this.updateMetrics();

      console.log(`🗑️ Destroyed connection: ${connectionId}`);

    } catch (error) {
      console.error(`❌ Error destroying connection ${connectionId}:`, error);
    }
  }

  // Update metrics
  private updateMetrics(): void {
    this.metrics.totalConnections = this.connections.size;
    this.metrics.activeConnections = Array.from(this.connections.values())
      .filter(conn => conn.isActive).length;
    this.metrics.idleConnections = this.availableConnections.length;
    this.metrics.unhealthyConnections = Array.from(this.connections.values())
      .filter(conn => !conn.isHealthy).length;
  }

  // Get current metrics
  getMetrics(): ConnectionPoolMetrics {
    this.updateMetrics();
    return { ...this.metrics };
  }

  // Get connection pool status
  getStatus(): {
    totalConnections: number;
    availableConnections: number;
    activeConnections: number;
    waitingRequests: number;
    healthyConnections: number;
    useSQLCipher: boolean;
  } {
    this.updateMetrics();
    
    return {
      totalConnections: this.connections.size,
      availableConnections: this.availableConnections.length,
      activeConnections: this.metrics.activeConnections,
      waitingRequests: this.waitingQueue.length,
      healthyConnections: this.connections.size - this.metrics.unhealthyConnections,
      useSQLCipher: this.useSQLCipher
    };
  }

  // Shutdown connection pool
  async shutdown(): Promise<void> {
    console.log('🔗 Shutting down connection pool...');

    // Stop health monitoring
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }

    // Reject all waiting requests
    for (const waiting of this.waitingQueue) {
      clearTimeout(waiting.timeout);
      waiting.reject(new Error('Connection pool is shutting down'));
    }
    this.waitingQueue.length = 0;

    // Close all connections
    for (const connectionId of this.connections.keys()) {
      this.destroyConnection(connectionId);
    }

    console.log('✅ Connection pool shutdown complete');
  }
}

// Global connection manager instance
let connectionManager: DatabaseConnectionManager | null = null;

// Initialize connection manager
export function initializeConnectionManager(
  dbPath: string,
  encryptionKey: string,
  useSQLCipher: boolean = true
): DatabaseConnectionManager {
  if (connectionManager) {
    return connectionManager;
  }

  connectionManager = new DatabaseConnectionManager(dbPath, encryptionKey, useSQLCipher);
  return connectionManager;
}

// Get connection manager instance
export function getConnectionManager(): DatabaseConnectionManager {
  if (!connectionManager) {
    throw new Error('Connection manager not initialized');
  }
  return connectionManager;
}
