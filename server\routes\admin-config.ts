import { Router, type Request, type Response } from 'express';
import { storage } from '../storage-factory';
import { isAdmin } from '../middleware/auth';

const adminConfigRouter = Router();

// SMTP Provider endpoints
adminConfigRouter.get('/email-config', isAdmin, async (req: Request, res: Response) => {
  try {
    const config = await storage.getEmailConfig();
    res.json(config);
  } catch (error) {
    console.error('Failed to get email config:', error);
    res.status(500).json({ message: 'Failed to get email configuration' });
  }
});

adminConfigRouter.post('/email-config', isAdmin, async (req: Request, res: Response) => {
  try {
    // Handle both flat structure and nested config structure
    let { id, providerId, name, host, port, secure, authUser, authPass, fromEmail, fromName, adminEmail, active, isDefault, isBackup, config } = req.body;

    // If config object is provided, extract values from it
    if (config) {
      host = config.host;
      port = config.port;
      secure = config.secure;
      authUser = config.username; // Frontend sends 'username', database expects 'authUser'
      authPass = config.password; // Frontend sends 'password', database expects 'authPass'
      fromEmail = config.fromEmail;
      fromName = config.fromName;
      adminEmail = config.adminEmail;
    }

    // Use providerId if id is not provided (frontend compatibility)
    const smtpId = id || providerId || `smtp-${Date.now()}`;

    // If this provider is being set as default, unset default flag on all other providers
    if (isDefault) {
      const providers = await storage.getSmtpProviders();
      for (const provider of providers) {
        if (provider.id !== smtpId && provider.isDefault) {
          await storage.updateSmtpProvider(provider.id, {
            isDefault: false,
            updatedAt: new Date().toISOString()
          });
        }
      }
    }

    const now = new Date().toISOString();
    const providerData = {
      id: smtpId,
      name,
      host,
      port,
      secure: secure || false,
      authUser,
      authPass,
      fromEmail,
      fromName,
      adminEmail,
      active: active !== undefined ? active : true,
      isDefault: isDefault || false,
      isBackup: isBackup || false,
      createdAt: now,
      updatedAt: now
    };

    // Check if provider exists
    const providers = await storage.getSmtpProviders();
    const existingProvider = providers.find(p => p.id === providerData.id);

    if (existingProvider) {
      await storage.updateSmtpProvider(providerData.id, providerData);
    } else {
      await storage.createSmtpProvider(providerData);
    }

    const updatedConfig = await storage.getEmailConfig();
    res.json({
      message: 'SMTP provider saved successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to save SMTP provider:', error);
    res.status(500).json({ message: 'Failed to save SMTP provider' });
  }
});

adminConfigRouter.delete('/email-config/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    await storage.deleteSmtpProvider(id);
    
    const updatedConfig = await storage.getEmailConfig();
    res.json({
      message: 'SMTP provider deleted successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to delete SMTP provider:', error);
    res.status(500).json({ message: 'Failed to delete SMTP provider' });
  }
});

// Payment Configuration endpoints
adminConfigRouter.get('/payment-config', isAdmin, async (req: Request, res: Response) => {
  try {
    const config = await storage.getPaymentConfig();
    res.json(config);
  } catch (error) {
    console.error('Failed to get payment config:', error);
    res.status(500).json({ message: 'Failed to get payment configuration' });
  }
});

// Custom Payment Links endpoints
adminConfigRouter.post('/payment-config/custom-link/add', isAdmin, async (req: Request, res: Response) => {
  try {
    const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

    const now = new Date().toISOString();
    const linkData = {
      id: `link-${Date.now()}`,
      name: name || 'New Payment Link',
      paymentLink: paymentLink || '',
      buttonText: buttonText || 'Pay Now',
      successRedirectUrl: successRedirectUrl || '',
      active: active !== undefined ? active : true,
      isTrialLink: false,
      createdAt: now,
      updatedAt: now
    };

    await storage.createCustomPaymentLink(linkData);

    const updatedConfig = await storage.getPaymentConfig();
    res.json({
      message: 'Custom payment link added successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to add custom payment link:', error);
    res.status(500).json({ message: 'Failed to add custom payment link' });
  }
});

adminConfigRouter.post('/payment-config/trial-custom-link/add', isAdmin, async (req: Request, res: Response) => {
  try {
    const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

    const now = new Date().toISOString();
    const linkData = {
      id: `trial-link-${Date.now()}`,
      name: name || 'New Trial Payment Link',
      paymentLink: paymentLink || '',
      buttonText: buttonText || 'Start Trial',
      successRedirectUrl: successRedirectUrl || '',
      active: active !== undefined ? active : true,
      isTrialLink: true,
      createdAt: now,
      updatedAt: now
    };

    await storage.createCustomPaymentLink(linkData);

    const updatedConfig = await storage.getPaymentConfig();
    res.json({
      message: 'Trial custom payment link added successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to add trial custom payment link:', error);
    res.status(500).json({ message: 'Failed to add trial custom payment link' });
  }
});

adminConfigRouter.put('/payment-config/custom-link/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

    const updateData = {
      name,
      paymentLink, // Use camelCase to match database method expectations
      buttonText,
      successRedirectUrl,
      active: active !== undefined ? active : true, // Ensure always activated
      updatedAt: new Date().toISOString()
    };

    await storage.updateCustomPaymentLink(id, updateData);

    const updatedConfig = await storage.getPaymentConfig();
    res.json({
      message: 'Custom payment link updated successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to update custom payment link:', error);
    res.status(500).json({ message: 'Failed to update custom payment link' });
  }
});

// POST route for custom payment link updates (frontend compatibility)
adminConfigRouter.post('/payment-config/custom-link/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

    const updateData = {
      name,
      paymentLink, // Use camelCase to match database method expectations
      buttonText,
      successRedirectUrl,
      active: active !== undefined ? active : true, // Ensure always activated
      updatedAt: new Date().toISOString()
    };

    await storage.updateCustomPaymentLink(id, updateData);

    const updatedConfig = await storage.getPaymentConfig();
    res.json({
      message: 'Custom payment link updated successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to update custom payment link:', error);
    res.status(500).json({ message: 'Failed to update custom payment link' });
  }
});

adminConfigRouter.put('/payment-config/trial-custom-link/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

    const updateData = {
      name,
      paymentLink, // Use camelCase to match database method expectations
      buttonText,
      successRedirectUrl,
      active: active !== undefined ? active : true, // Ensure always activated
      updatedAt: new Date().toISOString()
    };

    await storage.updateCustomPaymentLink(id, updateData);

    const updatedConfig = await storage.getPaymentConfig();
    res.json({
      message: 'Trial custom payment link updated successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to update trial custom payment link:', error);
    res.status(500).json({ message: 'Failed to update trial custom payment link' });
  }
});

// POST route for trial custom payment link updates (frontend compatibility)
adminConfigRouter.post('/payment-config/trial-custom-link/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { name, paymentLink, buttonText, successRedirectUrl, active } = req.body;

    const updateData = {
      name,
      paymentLink, // Use camelCase to match database method expectations
      buttonText,
      successRedirectUrl,
      active: active !== undefined ? active : true, // Ensure always activated
      updatedAt: new Date().toISOString()
    };

    await storage.updateCustomPaymentLink(id, updateData);

    const updatedConfig = await storage.getPaymentConfig();
    res.json({
      message: 'Trial custom payment link updated successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to update trial custom payment link:', error);
    res.status(500).json({ message: 'Failed to update trial custom payment link' });
  }
});

adminConfigRouter.delete('/payment-config/custom-link/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    await storage.deleteCustomPaymentLink(id);

    const updatedConfig = await storage.getPaymentConfig();
    res.json({
      message: 'Custom payment link deleted successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to delete custom payment link:', error);
    res.status(500).json({ message: 'Failed to delete custom payment link' });
  }
});

adminConfigRouter.delete('/payment-config/trial-custom-link/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    await storage.deleteCustomPaymentLink(id);

    const updatedConfig = await storage.getPaymentConfig();
    res.json({
      message: 'Trial custom payment link deleted successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to delete trial custom payment link:', error);
    res.status(500).json({ message: 'Failed to delete trial custom payment link' });
  }
});

// Provider Configuration Routes (for updating provider settings like rotation method and active status)

// Update Custom Link provider configuration
adminConfigRouter.post('/payment-config/custom-link', isAdmin, async (req: Request, res: Response) => {
  try {
    const { active, rotationMethod } = req.body;

    // Update the custom link provider configuration
    await storage.updatePaymentProviderConfig('custom-link', {
      active: active !== undefined ? active : true,
      rotationMethod: rotationMethod || 'round-robin'
    });

    // If this provider is being activated, deactivate other providers
    if (active) {
      await storage.deactivateOtherPaymentProviders('custom-link');
    }

    const updatedConfig = await storage.getPaymentConfig();
    res.json({
      message: active
        ? 'Custom Payment Links configuration updated and activated.'
        : 'Custom Payment Links configuration updated successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to update custom link provider configuration:', error);
    res.status(500).json({ message: 'Failed to update custom link provider configuration' });
  }
});

// Update Trial Custom Link provider configuration
adminConfigRouter.post('/payment-config/trial-custom-link', isAdmin, async (req: Request, res: Response) => {
  try {
    const { active, rotationMethod } = req.body;

    // Update the trial custom link provider configuration
    await storage.updatePaymentProviderConfig('trial-custom-link', {
      active: active !== undefined ? active : true,
      rotationMethod: rotationMethod || 'round-robin'
    });

    const updatedConfig = await storage.getPaymentConfig();
    res.json({
      message: 'Trial Custom Payment Links configuration updated successfully',
      config: updatedConfig
    });
  } catch (error) {
    console.error('Failed to update trial custom link provider configuration:', error);
    res.status(500).json({ message: 'Failed to update trial custom link provider configuration' });
  }
});

export { adminConfigRouter };
