# ===================================================================
# PayPal Invoice Generator - Comprehensive Security .gitignore
# ===================================================================
# This file prevents sensitive data from being committed to version control
# Last updated: 2025-07-17
# ===================================================================

# ===================================================================
# DATABASE PROTECTION (CRITICAL)
# ===================================================================
# SQLite database files - NEVER commit these
*.db
*.db-shm
*.db-wal
*.sqlite
*.sqlite3

# Secure database directory (contains live data)
app/data/
app/data/**

# Legacy database locations (should not exist but prevent commits)
server/data.db
server/database.db
data.db
database.db

# Database backups (contain sensitive customer data)
app/data/backups/
backups/
*.backup
*.bak

# ===================================================================
# SECURITY & ENCRYPTION FILES (CRITICAL)
# ===================================================================
# Database encryption keys
.db-key
.master-key
.key-store.json
*.key

# Security configuration files
app/config/security-config.json
security-config.json

# SSL/TLS certificates and keys
*.pem
*.crt
*.key
*.p12
*.pfx

# ===================================================================
# ENVIRONMENT & CONFIGURATION (CRITICAL)
# ===================================================================
# Environment files (contain API keys, secrets)
.env
.env.*
.env.local
.env.development
.env.test
.env.production

# Configuration files with sensitive data
config.json
secrets.json
credentials.json

# ===================================================================
# APPLICATION DATA & LOGS (SENSITIVE)
# ===================================================================
# Secure application directories
app/logs/
app/storage/uploads/
app/config/

# Legacy directories (should be moved to secure locations)
logs/
uploads/
storage/

# Log files (may contain sensitive information)
*.log
*.log.*
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===================================================================
# BUILD & DEVELOPMENT FILES
# ===================================================================
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm/
.yarn/

# Build outputs
dist/
build/
public-secure/
.cache/
.parcel-cache/

# TypeScript
*.tsbuildinfo
.tscache/

# ===================================================================
# DEVELOPMENT & EDITOR FILES
# ===================================================================
# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ===================================================================
# TEMPORARY & CACHE FILES
# ===================================================================
# Temporary files
tmp/
temp/
*.tmp
*.temp

# Cache directories
.cache/
.npm/
.eslintcache
.stylelintcache

# ===================================================================
# TESTING & COVERAGE
# ===================================================================
# Test coverage
coverage/
.nyc_output/
*.lcov

# Test databases
test.db
test.sqlite
*.test.db

# ===================================================================
# DEPLOYMENT & PRODUCTION
# ===================================================================
# Production environment files
.env.production
production.json

# Deployment scripts with sensitive data
deploy.sh
deploy.json

# ===================================================================
# PACKAGE MANAGER FILES
# ===================================================================
# Lock files (optional - some teams prefer to commit these)
# package-lock.json
# yarn.lock

# Package manager cache
.pnpm-store/
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz

# ===================================================================
# SECURITY TESTING & AUDIT FILES
# ===================================================================
# Security test reports
security-test-report-*.html
security-test-report-*.json
audit-report-*.json

# Penetration testing files
*.pen
*.audit

# ===================================================================
# BACKUP & ARCHIVE FILES
# ===================================================================
# Compressed archives that might contain sensitive data
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# Database dumps
*.sql
*.dump

# ===================================================================
# CUSTOM APPLICATION FILES
# ===================================================================
# PayPal Invoice Generator specific files
invoice-*.pdf
receipt-*.pdf
customer-data-*.json

# Email templates with sensitive data
email-templates-backup/

# ===================================================================
# RUNTIME & PROCESS FILES
# ===================================================================
# Process IDs
*.pid
*.seed
*.pid.lock

# Runtime data
pids/
lib-cov/

# ===================================================================
# DOCUMENTATION BUILDS
# ===================================================================
# Generated documentation
docs/build/
docs/dist/

# ===================================================================
# MISCELLANEOUS
# ===================================================================
# Any file ending with .secret
*.secret

# Any file ending with .private
*.private

# Local configuration overrides
local.json
local.config.js

# ===================================================================
# END OF .gitignore
# ===================================================================
