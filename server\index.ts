import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, log } from "./vite";
import * as dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

import { storage } from './storage-factory';
import { updateAllowedDomains } from '../shared/email-validator';
import cors from 'cors';
// Configuration storage is now handled by database storage
// import { initializeDefaultCheckoutPages } from './init-checkout-pages'; // DISABLED
import { privacyMiddleware, noIndexMiddleware, blockCrawlersMiddleware } from './middleware/privacy';
import { robotsRouter } from './routes/robots';
import { urlUtilsMiddleware } from './utils/url-utils';

// Import security features
import { securityHeadersMiddleware } from './middleware/security-headers';
import { initializeConnectionManager } from './config/connection-manager';
import { initializeHealthMonitor } from './config/database-health-monitor';
import { keyRotationManager } from './config/key-rotation';
import { getEncryptionKey } from './config/database-security';

// Load environment variables from .env file
dotenv.config();

const app = express();

// Trust proxy when behind a reverse proxy (like Nginx in CloudPanel)
app.set('trust proxy', 1);

// Initialize security features
console.log('🔒 Initializing security features...');

// Apply security headers middleware first
app.use(securityHeadersMiddleware);

app.use(cors({
  credentials: true,
  origin: true // Allow all origins in development, should be restricted in production
}));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Apply privacy and security middleware
app.use(privacyMiddleware);
app.use(blockCrawlersMiddleware);
app.use(noIndexMiddleware);

// Serve robots.txt
app.use('/robots.txt', robotsRouter);

// CSP violation reporting endpoint
app.post('/api/csp-violation-report', express.json({ type: 'application/csp-report' }), (req, res) => {
  try {
    console.warn('🚨 CSP Violation Report:', JSON.stringify(req.body, null, 2));
    res.status(204).end();
  } catch (error) {
    console.error('❌ CSP violation reporter error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});



// Debug middleware to log all incoming requests
app.use((req, res, next) => {
  console.log(`[DEBUG] ${req.method} ${req.path}`, req.body);
  next();
});

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  console.log("Current environment:", app.get("env"));
  console.log("NODE_ENV:", process.env.NODE_ENV);

  // Use development mode when NODE_ENV is development
  const isDevMode = app.get("env") === "development";

  if (isDevMode) {
    console.log("Setting up Vite in development mode");
    await setupVite(app, server);
  } else {
    console.log("Setting up static file serving in production mode");

    // Serve static files from the secure public directory
    const distPath = path.resolve(process.cwd(), "public-secure");
    console.log(`Serving static files from: ${distPath}`);

    if (!fs.existsSync(distPath)) {
      console.error(`Error: Build directory not found: ${distPath}`);
      console.error("Make sure to run 'npm run build:prod' before starting the server in production mode");
      process.exit(1);
    }

    // Serve static files
    app.use(express.static(distPath));

    // Serve assets from the assets directory
    const assetsPath = path.join(distPath, 'assets');
    if (fs.existsSync(assetsPath)) {
      console.log(`Serving assets from: ${assetsPath}`);
      app.use('/assets', express.static(assetsPath));
    }

    // fall through to index.html if the file doesn't exist
    app.use("*", (req, res) => {
      // Skip API routes
      if (req.originalUrl.startsWith('/api')) {
        return res.status(404).json({ message: "API endpoint not found" });
      }

      console.log(`Serving index.html for: ${req.originalUrl}`);

      // For all other routes, serve the index.html file
      res.sendFile(path.resolve(distPath, "index.html"));
    });
  }

  // Serve the app on port 3001
  // this serves both the API and the client.
  const port = process.env.PORT || 3001;
  server.listen(port, () => {
    log(`serving on port ${port}`);

    // Initialize security systems
    (async () => {
      try {
        console.log('🔒 Initializing security systems...');

        // Initialize database connection manager
        const encryptionKey = getEncryptionKey();
        const useSQLCipher = process.env.USE_SQLCIPHER !== 'false';
        const connectionManager = initializeConnectionManager(
          process.env.DATABASE_PATH || 'data.db',
          encryptionKey,
          useSQLCipher
        );
        console.log('✅ Database connection manager initialized');

        // Initialize health monitoring
        const healthMonitor = initializeHealthMonitor();
        console.log('✅ Database health monitoring initialized');

        // Check key rotation status
        const rotationStatus = keyRotationManager.getRotationStatus();
        console.log(`🔑 Key rotation status: ${rotationStatus.currentVersion}, next rotation: ${rotationStatus.nextRotation.toISOString()}`);

        console.log('🔒 Security systems initialization completed');
      } catch (error) {
        console.error('❌ Failed to initialize security systems:', error);
      }
    })();

    // Initialize allowed email domains
    storage.getGeneralSettings().then(settings => {
      if (settings && settings.emailDomainRestrictionEnabled) {
        const allowedDomains = settings.emailDomainRestrictionDomains ?
          settings.emailDomainRestrictionDomains.split(',').map(d => d.trim()).filter(d => d.length > 0) :
          [];
        updateAllowedDomains(allowedDomains);
        console.log('Initialized allowed email domains:', allowedDomains);
      }
    }).catch(error => {
      console.error('Error initializing allowed email domains:', error);
    });

    // Initialize default checkout pages - DISABLED to prevent automatic recreation
    // initializeDefaultCheckoutPages();

    // Initialize default email templates
    (async () => {
      try {
        const { initializeDefaultTemplates } = await import('./routes/email-templates');
        await initializeDefaultTemplates();
        console.log('✅ Email templates initialization completed');
      } catch (error) {
        console.error('❌ Failed to initialize email templates:', error);
      }
    })();

    // Configuration migration completed - all data is now stored in database

    // Initialize system monitoring
    (async () => {
      try {
        const { systemMonitor } = await import('./services/system-monitor');
        await systemMonitor.startMonitoring(5); // Start with 5-minute intervals
        console.log('🔍 System monitoring initialized');
      } catch (error) {
        console.error('Failed to initialize system monitoring:', error);
      }
    })();
  });
})();
