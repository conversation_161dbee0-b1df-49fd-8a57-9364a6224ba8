import React from 'react';
import { AlertTriangle, Mail } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface EmailNotFoundMessageProps {
  userEmail?: string;
  contactEmail?: string;
}

export function EmailNotFoundMessage({ userEmail, contactEmail }: EmailNotFoundMessageProps) {
  const defaultContactEmail = "<EMAIL>";
  const displayContactEmail = contactEmail || defaultContactEmail;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
          <AlertTriangle className="h-6 w-6 text-orange-600" />
        </div>
        <CardTitle className="text-xl font-semibold text-gray-900">
          Email Not Found
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-gray-600 space-y-3">
          <p>
            The email <strong>{userEmail}</strong> is not in our list (existing subscribers).
          </p>
          
          <p>
            If you are an existing subscriber please contact us on the email below with one of the following:
          </p>
          
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li><strong>M3U Link</strong></li>
            <li><strong>Username</strong></li>
            <li><strong>MAC Address</strong></li>
          </ul>
          
          <p>
            And we will contact you soon after verification.
          </p>
          
          <p className="text-xs text-gray-500">
            <strong>Note:</strong> If you want to buy a second or third subscription, just use the same email from your first subscription since you already tested our service.
          </p>
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center space-x-2">
            <Mail className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">Contact Email:</span>
          </div>
          <a 
            href={`mailto:${displayContactEmail}`}
            className="text-blue-600 hover:text-blue-800 font-medium text-sm break-all"
          >
            {displayContactEmail}
          </a>
        </div>
      </CardContent>
    </Card>
  );
}
