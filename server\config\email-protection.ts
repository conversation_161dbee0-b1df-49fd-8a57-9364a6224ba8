import * as crypto from 'crypto';
import { encryptEmail, decryptEmail, createEmailSearchHash } from './database-security';
import { securityMonitor, SecurityEventType, SecuritySeverity } from './security-monitoring';
import { SecurityContext } from './access-control';

// Email protection configuration
interface EmailProtectionConfig {
  enableFieldEncryption: boolean;
  enableEmailObfuscation: boolean;
  enableAuditTrail: boolean;
  obfuscationPattern: 'partial' | 'domain-only' | 'hash';
  auditRetentionDays: number;
}

const DEFAULT_CONFIG: EmailProtectionConfig = {
  enableFieldEncryption: process.env.ENABLE_EMAIL_ENCRYPTION !== 'false',
  enableEmailObfuscation: process.env.ENABLE_EMAIL_OBFUSCATION !== 'false',
  enableAuditTrail: process.env.ENABLE_EMAIL_AUDIT !== 'false',
  obfuscationPattern: (process.env.EMAIL_OBFUSCATION_PATTERN as any) || 'partial',
  auditRetentionDays: parseInt(process.env.EMAIL_AUDIT_RETENTION_DAYS || '90')
};

// Email audit trail interface
interface EmailAuditEntry {
  id: string;
  timestamp: Date;
  action: 'CREATE' | 'READ' | 'UPDATE' | 'DELETE' | 'SEARCH' | 'EXPORT';
  emailHash: string;
  userId?: number;
  username?: string;
  ipAddress?: string;
  userAgent?: string;
  details?: any;
}

// In-memory audit trail (in production, this should be stored in a separate secure database)
const emailAuditTrail: EmailAuditEntry[] = [];

// Email protection class
export class EmailProtection {
  private config: EmailProtectionConfig;

  constructor(config: Partial<EmailProtectionConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // Encrypt email for storage
  encryptEmailForStorage(email: string, context?: SecurityContext): string {
    if (!this.config.enableFieldEncryption) {
      return email;
    }

    try {
      const encrypted = encryptEmail(email);
      
      // Log encryption event
      this.logEmailAccess('CREATE', email, context, { 
        action: 'encrypt_for_storage',
        encrypted: true 
      });
      
      return encrypted;
    } catch (error) {
      console.error('Failed to encrypt email:', error);
      throw new Error('Email encryption failed');
    }
  }

  // Decrypt email from storage
  decryptEmailFromStorage(encryptedEmail: string, context?: SecurityContext): string {
    if (!this.config.enableFieldEncryption) {
      return encryptedEmail;
    }

    // Check if email is actually encrypted (has version prefix)
    if (!encryptedEmail.includes(':') || !encryptedEmail.startsWith('v')) {
      return encryptedEmail; // Not encrypted, return as-is
    }

    try {
      const decrypted = decryptEmail(encryptedEmail);
      
      // Log decryption event
      this.logEmailAccess('READ', decrypted, context, { 
        action: 'decrypt_from_storage',
        decrypted: true 
      });
      
      return decrypted;
    } catch (error) {
      console.error('Failed to decrypt email:', error);
      throw new Error('Email decryption failed');
    }
  }

  // Obfuscate email for display
  obfuscateEmailForDisplay(email: string, context?: SecurityContext): string {
    if (!this.config.enableEmailObfuscation) {
      return email;
    }

    try {
      let obfuscated: string;
      
      switch (this.config.obfuscationPattern) {
        case 'partial':
          obfuscated = this.partialObfuscation(email);
          break;
        case 'domain-only':
          obfuscated = this.domainOnlyObfuscation(email);
          break;
        case 'hash':
          obfuscated = this.hashObfuscation(email);
          break;
        default:
          obfuscated = this.partialObfuscation(email);
      }

      // Log obfuscation event
      this.logEmailAccess('READ', email, context, { 
        action: 'obfuscate_for_display',
        pattern: this.config.obfuscationPattern,
        obfuscated: obfuscated 
      });

      return obfuscated;
    } catch (error) {
      console.error('Failed to obfuscate email:', error);
      return '***@***.***'; // Fallback obfuscation
    }
  }

  // Partial obfuscation: show first 2 chars and domain
  private partialObfuscation(email: string): string {
    const [localPart, domain] = email.split('@');
    if (!localPart || !domain) {
      return '***@***.***';
    }

    const obfuscatedLocal = localPart.length > 2 
      ? localPart.substring(0, 2) + '*'.repeat(Math.max(1, localPart.length - 2))
      : '*'.repeat(localPart.length);

    return `${obfuscatedLocal}@${domain}`;
  }

  // Domain-only obfuscation: show only domain
  private domainOnlyObfuscation(email: string): string {
    const [, domain] = email.split('@');
    return domain ? `***@${domain}` : '***@***.***';
  }

  // Hash obfuscation: show hash of email
  private hashObfuscation(email: string): string {
    const hash = crypto.createHash('sha256').update(email).digest('hex');
    return `hash:${hash.substring(0, 8)}...`;
  }

  // Create searchable hash for encrypted emails
  createSearchHash(email: string, context?: SecurityContext): string {
    try {
      const hash = createEmailSearchHash(email);
      
      // Log search hash creation
      this.logEmailAccess('SEARCH', email, context, { 
        action: 'create_search_hash',
        hashCreated: true 
      });
      
      return hash;
    } catch (error) {
      console.error('Failed to create email search hash:', error);
      throw new Error('Email search hash creation failed');
    }
  }

  // Search encrypted emails by hash
  async searchEncryptedEmails(searchTerm: string, context?: SecurityContext): Promise<string> {
    try {
      const searchHash = this.createSearchHash(searchTerm.toLowerCase().trim(), context);
      
      // Log search attempt
      this.logEmailAccess('SEARCH', searchTerm, context, { 
        action: 'search_encrypted_emails',
        searchHash: searchHash.substring(0, 8) + '...' 
      });
      
      return searchHash;
    } catch (error) {
      console.error('Failed to search encrypted emails:', error);
      throw new Error('Email search failed');
    }
  }

  // Mask email in logs and monitoring
  maskEmailForLogging(email: string): string {
    if (!email || typeof email !== 'string') {
      return '[INVALID_EMAIL]';
    }

    const [localPart, domain] = email.split('@');
    if (!localPart || !domain) {
      return '[MALFORMED_EMAIL]';
    }

    // Show only first character and domain for logging
    const maskedLocal = localPart.charAt(0) + '*'.repeat(Math.max(1, localPart.length - 1));
    return `${maskedLocal}@${domain}`;
  }

  // Log email access for audit trail
  private logEmailAccess(
    action: EmailAuditEntry['action'],
    email: string,
    context?: SecurityContext,
    details?: any
  ): void {
    if (!this.config.enableAuditTrail) {
      return;
    }

    try {
      const auditEntry: EmailAuditEntry = {
        id: crypto.randomUUID(),
        timestamp: new Date(),
        action,
        emailHash: crypto.createHash('sha256').update(email.toLowerCase()).digest('hex'),
        userId: context?.userId,
        username: context?.username,
        ipAddress: context?.ipAddress,
        userAgent: context?.userAgent,
        details
      };

      // Add to in-memory audit trail
      emailAuditTrail.push(auditEntry);

      // Log to security monitor
      securityMonitor.logSecurityEvent(
        SecurityEventType.DATA_EXPORT, // Using DATA_EXPORT for email access
        context || {
          userId: undefined,
          username: 'system',
          role: 'system' as any,
          permissions: [],
          ipAddress: 'localhost'
        },
        {
          emailAction: action,
          emailHash: auditEntry.emailHash.substring(0, 8) + '...',
          details
        },
        action === 'DELETE' ? SecuritySeverity.HIGH : SecuritySeverity.MEDIUM
      );

      // Clean up old audit entries
      this.cleanupAuditTrail();
    } catch (error) {
      console.error('Failed to log email access:', error);
    }
  }

  // Clean up old audit trail entries
  private cleanupAuditTrail(): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.auditRetentionDays);

    const initialLength = emailAuditTrail.length;
    
    // Remove old entries
    for (let i = emailAuditTrail.length - 1; i >= 0; i--) {
      if (emailAuditTrail[i].timestamp < cutoffDate) {
        emailAuditTrail.splice(i, 1);
      }
    }

    if (emailAuditTrail.length < initialLength) {
      console.log(`🧹 Cleaned up ${initialLength - emailAuditTrail.length} old email audit entries`);
    }
  }

  // Get email audit trail for a specific email
  getEmailAuditTrail(email: string, limit: number = 100): EmailAuditEntry[] {
    const emailHash = crypto.createHash('sha256').update(email.toLowerCase()).digest('hex');
    
    return emailAuditTrail
      .filter(entry => entry.emailHash === emailHash)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  // Get all audit trail entries (admin only)
  getAllAuditTrail(limit: number = 1000): EmailAuditEntry[] {
    return emailAuditTrail
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  // Export audit trail for compliance
  exportAuditTrail(startDate?: Date, endDate?: Date): EmailAuditEntry[] {
    let filtered = emailAuditTrail;

    if (startDate) {
      filtered = filtered.filter(entry => entry.timestamp >= startDate);
    }

    if (endDate) {
      filtered = filtered.filter(entry => entry.timestamp <= endDate);
    }

    return filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  // Get protection statistics
  getProtectionStats(): {
    totalAuditEntries: number;
    encryptionEnabled: boolean;
    obfuscationEnabled: boolean;
    auditEnabled: boolean;
    oldestAuditEntry?: Date;
    newestAuditEntry?: Date;
  } {
    const sortedEntries = emailAuditTrail.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    
    return {
      totalAuditEntries: emailAuditTrail.length,
      encryptionEnabled: this.config.enableFieldEncryption,
      obfuscationEnabled: this.config.enableEmailObfuscation,
      auditEnabled: this.config.enableAuditTrail,
      oldestAuditEntry: sortedEntries[0]?.timestamp,
      newestAuditEntry: sortedEntries[sortedEntries.length - 1]?.timestamp
    };
  }
}

// Global email protection instance
export const emailProtection = new EmailProtection();
