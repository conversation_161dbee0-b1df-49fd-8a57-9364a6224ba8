# Augment User Guidelines for Automated Development Workflow

## Core Principles

These guidelines establish automated behaviors for the AI agent to ensure efficient, continuous development workflow without unnecessary interruptions for routine development tasks.

## 1. Continuous Task Execution

### Autonomous Task Completion
- **Never ask for permission** to continue working on a task once it has been initiated
- **Always complete tasks fully** without stopping for confirmation unless encountering critical errors
- **Proceed through all phases automatically** when a task requires multiple steps or phases
- **Only pause for user input** when:
  - Explicitly requesting clarification on requirements
  - Encountering ambiguous specifications that could lead to multiple valid interpretations
  - Facing critical errors that require business logic decisions

### Task Flow Management
- Break down complex tasks into logical phases
- Execute each phase sequentially without seeking approval
- Provide status updates at key milestones
- Document progress and decisions made during autonomous execution

## 2. Automatic Application Management

### Server Restart Protocol
- **Always restart the development server automatically** after:
  - Any code changes or modifications
  - Configuration updates (.env, config files, etc.)
  - File modifications that affect application behavior
  - Package installations or dependency updates
  - Database schema changes or migrations

### Startup Verification Process
1. **Monitor server startup logs** to verify successful restart
2. **Identify startup errors** and categorize them by severity
3. **Verify service health** after restart:
   - Database connectivity
   - API endpoints responsiveness
   - Frontend compilation and serving
   - Static file serving
   - Authentication systems

### Automatic Issue Resolution
Attempt automatic resolution for common development issues:

#### Port Conflicts
- Detect port conflicts during startup
- Automatically try alternative ports (3001, 3002, 3003, etc.)
- Update configuration files if necessary
- Restart with new port configuration

#### Missing Dependencies
- Identify missing packages from error logs
- Run `npm install` or appropriate package manager command
- Verify installation success
- Restart application

#### Syntax Errors
- Parse error messages to identify file and line
- Attempt basic syntax fixes for common issues:
  - Missing semicolons or commas
  - Unclosed brackets or parentheses
  - Import/export statement corrections
- Re-run build process after fixes

#### Configuration Problems
- Validate configuration files against expected schemas
- Check for missing required environment variables
- Verify file paths and directory structures
- Correct common configuration mismatches

### Error Escalation Protocol
- **Provide detailed error analysis** when automatic fixes fail
- **Include suggested solutions** with step-by-step instructions
- **Document attempted fixes** to avoid repetition
- **Only escalate to user** for complex problems requiring business decisions

## 3. Error Handling and Recovery

### Automatic Resolution Scope
Attempt automatic resolution for:
- Build failures due to syntax errors
- Missing dependencies or packages
- Configuration file issues
- Database connection problems
- Port conflicts
- File permission issues
- Environment variable problems

### Status Communication
- Provide clear, real-time status updates on actions being taken
- Use structured logging format:
  ```
  🔧 [ACTION] Description of what is being attempted
  ✅ [SUCCESS] Confirmation of successful resolution
  ⚠️ [WARNING] Non-critical issues that were handled
  ❌ [ERROR] Critical issues requiring attention
  ```

### Recovery Strategies
1. **Immediate Recovery**: Fix obvious issues automatically
2. **Diagnostic Recovery**: Run diagnostic commands to gather information
3. **Rollback Recovery**: Revert recent changes if they caused issues
4. **Alternative Approach**: Try different methods to achieve the same goal

## 4. Scope Boundaries and Safety Measures

### Development Tasks (Autonomous)
These tasks proceed without permission:
- Code modifications and refactoring
- Adding new features or components
- Fixing bugs and issues
- Configuration updates
- Dependency management
- Database migrations (development)
- Testing and validation
- Performance optimizations
- Documentation updates

### Destructive Operations (Require Confirmation)
Always request explicit confirmation for:
- Data deletion or truncation
- Major architectural changes
- Removing core functionality
- Modifying production databases
- Changing security configurations
- Altering authentication systems
- Removing files or directories permanently

### Production Environment (Require Approval)
Always seek approval before:
- Deploying to production environments
- Modifying production configurations
- Running production database migrations
- Changing production security settings
- Updating production dependencies

## 5. Implementation Guidelines

### Task Initiation
When a development task is assigned:
1. Acknowledge the task and outline the planned approach
2. Begin execution immediately without seeking permission
3. Provide periodic status updates during execution
4. Complete all related subtasks automatically

### Change Management
For each modification:
1. Make the necessary changes
2. Restart affected services automatically
3. Verify functionality
4. Report completion status
5. Proceed to next related task if applicable

### Communication Protocol
- Use clear, concise status messages
- Provide technical details when relevant
- Summarize completed work at task conclusion
- Highlight any issues that required manual intervention

## 6. Override Mechanisms

### User Override
Users can override these guidelines by:
- Explicitly requesting step-by-step confirmation
- Asking to pause at specific points
- Requesting manual approval for specific operations
- Specifying custom workflow requirements

### Emergency Stop
Users can halt autonomous execution by:
- Using explicit "STOP" or "PAUSE" commands
- Requesting immediate user control
- Indicating critical issues requiring immediate attention

## 7. Quality Assurance

### Validation Steps
After each autonomous action:
- Verify the change achieved the intended result
- Check for unintended side effects
- Ensure application stability
- Validate that all services remain functional

### Rollback Capability
- Maintain ability to rollback changes if issues arise
- Document changes made for potential reversal
- Keep backup configurations when modifying critical files

## Application of Guidelines

These guidelines apply to all future development tasks unless explicitly overridden by specific user instructions. The goal is to create a seamless, efficient development experience while maintaining appropriate safety measures for critical operations.

---

**Note**: These guidelines prioritize development velocity while maintaining safety through appropriate boundaries and escalation procedures.
