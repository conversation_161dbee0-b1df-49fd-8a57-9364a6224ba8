import { Router, Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { randomBytes } from 'crypto';
import { isAdmin } from '../middleware/auth';
import { toAbsoluteUrl } from '../utils/url-utils';

const uploadRouter = Router();

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), 'app', 'storage', 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true, mode: 0o750 });
}

// Configure multer storage
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const randomId = randomBytes(8).toString('hex');
    const fileExt = path.extname(file.originalname);
    cb(null, `${Date.now()}-${randomId}${fileExt}`);
  }
});

// File filter to only allow image files
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'));
  }
};

// Configure multer upload
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
  },
  fileFilter: fileFilter
});

// Image upload endpoint
uploadRouter.post('/image', isAdmin, upload.single('image'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No image file provided' });
    }

    // Create the image URL using dynamic URL utility
    const imageUrl = await toAbsoluteUrl(`uploads/${req.file.filename}`, req);

    // Return the image URL
    res.status(200).json({
      url: imageUrl,
      filename: req.file.filename
    });
  } catch (error) {
    console.error('Error uploading image:', error);
    res.status(500).json({
      message: 'Failed to upload image',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Base64 image upload endpoint (fallback for browsers that don't support FormData)
uploadRouter.post('/image-base64', isAdmin, async (req: Request, res: Response) => {
  try {
    const { imageData } = req.body;

    if (!imageData || !imageData.startsWith('data:image/')) {
      return res.status(400).json({ message: 'Valid image data is required' });
    }

    // Extract the image type and data
    const matches = imageData.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);

    if (!matches || matches.length !== 3) {
      return res.status(400).json({ message: 'Invalid image data format' });
    }

    const imageType = matches[1];
    const base64Data = matches[2];
    const buffer = Buffer.from(base64Data, 'base64');

    // Generate a random filename
    const randomId = randomBytes(8).toString('hex');
    const filename = `${Date.now()}-${randomId}.${imageType}`;
    const filePath = path.join(uploadsDir, filename);

    // Save the file
    fs.writeFileSync(filePath, buffer);

    // Create the image URL using dynamic URL utility
    const imageUrl = await toAbsoluteUrl(`uploads/${filename}`, req);

    // Return the image URL
    res.status(200).json({
      url: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('Error processing base64 image upload:', error);
    res.status(500).json({
      message: 'Failed to process image upload',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get all uploaded images endpoint
uploadRouter.get('/images', isAdmin, async (req: Request, res: Response) => {
  try {
    // Read the uploads directory
    const files = fs.readdirSync(uploadsDir);

    // Filter for image files only
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return imageExtensions.includes(ext);
    });

    // Create image objects with metadata using dynamic URLs
    const images = await Promise.all(imageFiles.map(async filename => {
      const filePath = path.join(uploadsDir, filename);
      const stats = fs.statSync(filePath);
      const url = await toAbsoluteUrl(`uploads/${filename}`, req);

      return {
        filename,
        url,
        size: stats.size,
        uploadedAt: stats.birthtime,
        modifiedAt: stats.mtime
      };
    }));

    // Sort by upload date (newest first)
    images.sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());

    res.json(images);
  } catch (error) {
    console.error('Error fetching images:', error);
    res.status(500).json({
      message: 'Failed to fetch images',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete image endpoint
uploadRouter.delete('/images/:filename', isAdmin, (req: Request, res: Response) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(uploadsDir, filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: 'Image not found' });
    }

    // Delete the file
    fs.unlinkSync(filePath);

    res.json({ message: 'Image deleted successfully' });
  } catch (error) {
    console.error('Error deleting image:', error);
    res.status(500).json({
      message: 'Failed to delete image',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default uploadRouter;
