import os from 'os';
import fs from 'fs';
import path from 'path';
import { telegramBot } from './telegram-bot';

interface SystemMetrics {
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    percentage: number;
  };
  uptime: number;
  timestamp: string;
}

interface ErrorLog {
  timestamp: string;
  level: 'error' | 'warning' | 'critical';
  message: string;
  stack?: string;
  source: string;
}

class SystemMonitor {
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private errorLogs: ErrorLog[] = [];
  private lastMetrics: SystemMetrics | null = null;
  private alertThresholds = {
    cpu: 80, // CPU usage percentage
    memory: 85, // Memory usage percentage
    disk: 90, // Disk usage percentage
    responseTime: 5000, // Response time in ms
  };

  constructor() {
    this.setupErrorHandlers();
  }

  private setupErrorHandlers() {
    // Capture uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.logError('critical', `Uncaught Exception: ${error.message}`, error.stack, 'process');
    });

    // Capture unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      this.logError('critical', `Unhandled Rejection: ${reason}`, undefined, 'promise');
    });

    // Capture warnings
    process.on('warning', (warning) => {
      this.logError('warning', `Process Warning: ${warning.message}`, warning.stack, 'process');
    });
  }

  async startMonitoring(intervalMinutes: number = 5) {
    if (this.isMonitoring) {
      console.log('System monitoring is already running');
      return;
    }

    this.isMonitoring = true;
    console.log(`🔍 Starting system monitoring with ${intervalMinutes} minute intervals`);

    // Send startup notification
    await this.sendStartupNotification();

    // Set up periodic monitoring
    this.monitoringInterval = setInterval(async () => {
      try {
        const metrics = await this.collectMetrics();
        await this.checkAlerts(metrics);
        this.lastMetrics = metrics;
      } catch (error) {
        console.error('Error in system monitoring:', error);
        this.logError('error', `Monitoring error: ${error.message}`, error.stack, 'monitor');
      }
    }, intervalMinutes * 60 * 1000);

    // Initial metrics collection
    try {
      this.lastMetrics = await this.collectMetrics();
    } catch (error) {
      console.error('Error collecting initial metrics:', error);
    }
  }

  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('🛑 System monitoring stopped');
  }

  private async collectMetrics(): Promise<SystemMetrics> {
    const cpus = os.cpus();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;

    // Get disk usage (for the current directory)
    const diskStats = await this.getDiskUsage();

    // Calculate CPU usage (simplified)
    const loadAvg = os.loadavg();
    const cpuUsage = Math.min((loadAvg[0] / cpus.length) * 100, 100);

    return {
      cpu: {
        usage: Math.round(cpuUsage * 100) / 100,
        loadAverage: loadAvg,
      },
      memory: {
        total: totalMem,
        used: usedMem,
        free: freeMem,
        percentage: Math.round((usedMem / totalMem) * 100 * 100) / 100,
      },
      disk: diskStats,
      uptime: os.uptime(),
      timestamp: new Date().toISOString(),
    };
  }

  private async getDiskUsage(): Promise<{ total: number; used: number; free: number; percentage: number }> {
    try {
      const stats = fs.statSync(process.cwd());
      // This is a simplified disk usage calculation
      // In a real implementation, you might want to use a library like 'node-disk-info'
      const total = 100 * 1024 * 1024 * 1024; // 100GB placeholder
      const used = 50 * 1024 * 1024 * 1024; // 50GB placeholder
      const free = total - used;
      
      return {
        total,
        used,
        free,
        percentage: Math.round((used / total) * 100 * 100) / 100,
      };
    } catch (error) {
      return {
        total: 0,
        used: 0,
        free: 0,
        percentage: 0,
      };
    }
  }

  private async checkAlerts(metrics: SystemMetrics) {
    const alerts: string[] = [];

    // CPU Alert
    if (metrics.cpu.usage > this.alertThresholds.cpu) {
      alerts.push(`🔥 High CPU Usage: ${metrics.cpu.usage}% (threshold: ${this.alertThresholds.cpu}%)`);
    }

    // Memory Alert
    if (metrics.memory.percentage > this.alertThresholds.memory) {
      alerts.push(`💾 High Memory Usage: ${metrics.memory.percentage}% (threshold: ${this.alertThresholds.memory}%)`);
    }

    // Disk Alert
    if (metrics.disk.percentage > this.alertThresholds.disk) {
      alerts.push(`💿 High Disk Usage: ${metrics.disk.percentage}% (threshold: ${this.alertThresholds.disk}%)`);
    }

    // Send alerts if any
    if (alerts.length > 0) {
      await this.sendAlert('System Performance Alert', alerts.join('\n'));
    }
  }

  async logError(level: 'error' | 'warning' | 'critical', message: string, stack?: string, source: string = 'unknown') {
    const errorLog: ErrorLog = {
      timestamp: new Date().toISOString(),
      level,
      message,
      stack,
      source,
    };

    this.errorLogs.push(errorLog);

    // Keep only last 100 error logs
    if (this.errorLogs.length > 100) {
      this.errorLogs = this.errorLogs.slice(-100);
    }

    // Send critical and error logs immediately
    if (level === 'critical' || level === 'error') {
      await this.sendErrorNotification(errorLog);
    }

    console.error(`[${level.toUpperCase()}] ${message}`, stack ? `\nStack: ${stack}` : '');
  }

  private async sendStartupNotification() {
    const metrics = await this.collectMetrics();
    const message = `🚀 **System Monitor Started**

📊 **Initial System Status:**
🖥️ **CPU:** ${metrics.cpu.usage}%
💾 **Memory:** ${metrics.memory.percentage}% (${this.formatBytes(metrics.memory.used)}/${this.formatBytes(metrics.memory.total)})
💿 **Disk:** ${metrics.disk.percentage}% (${this.formatBytes(metrics.disk.used)}/${this.formatBytes(metrics.disk.total)})
⏱️ **Uptime:** ${this.formatUptime(metrics.uptime)}

✅ System monitoring is now active`;

    await telegramBot.sendSystemMessage(message);
  }

  private async sendAlert(title: string, message: string) {
    const alertMessage = `🚨 **${title}**

${message}

⏰ Time: ${new Date().toLocaleString()}`;

    await telegramBot.sendSystemMessage(alertMessage);
  }

  private async sendErrorNotification(errorLog: ErrorLog) {
    const emoji = errorLog.level === 'critical' ? '🔴' : errorLog.level === 'error' ? '🟠' : '🟡';
    
    const message = `${emoji} **${errorLog.level.toUpperCase()} LOG**

📝 **Message:** ${errorLog.message}
🔧 **Source:** ${errorLog.source}
⏰ **Time:** ${new Date(errorLog.timestamp).toLocaleString()}

${errorLog.stack ? `📋 **Stack Trace:**\n\`\`\`\n${errorLog.stack.substring(0, 500)}${errorLog.stack.length > 500 ? '...' : ''}\n\`\`\`` : ''}`;

    await telegramBot.sendSystemMessage(message);
  }

  async getSystemReport(): Promise<string> {
    if (!this.lastMetrics) {
      this.lastMetrics = await this.collectMetrics();
    }

    const metrics = this.lastMetrics;
    const recentErrors = this.errorLogs.slice(-5);

    return `📊 **System Performance Report**

🖥️ **CPU Usage:** ${metrics.cpu.usage}%
📈 **Load Average:** ${metrics.cpu.loadAverage.map(l => l.toFixed(2)).join(', ')}

💾 **Memory Usage:** ${metrics.memory.percentage}%
📊 **Memory:** ${this.formatBytes(metrics.memory.used)} / ${this.formatBytes(metrics.memory.total)}
🆓 **Free Memory:** ${this.formatBytes(metrics.memory.free)}

💿 **Disk Usage:** ${metrics.disk.percentage}%
📊 **Disk:** ${this.formatBytes(metrics.disk.used)} / ${this.formatBytes(metrics.disk.total)}
🆓 **Free Disk:** ${this.formatBytes(metrics.disk.free)}

⏱️ **System Uptime:** ${this.formatUptime(metrics.uptime)}
🕐 **Last Updated:** ${new Date(metrics.timestamp).toLocaleString()}

${recentErrors.length > 0 ? `🚨 **Recent Errors (${recentErrors.length}):**\n${recentErrors.map(e => `• ${e.level}: ${e.message.substring(0, 100)}${e.message.length > 100 ? '...' : ''}`).join('\n')}` : '✅ **No Recent Errors**'}

📈 **Status:** ${this.getSystemStatus(metrics)}`;
  }

  private getSystemStatus(metrics: SystemMetrics): string {
    if (metrics.cpu.usage > this.alertThresholds.cpu || 
        metrics.memory.percentage > this.alertThresholds.memory || 
        metrics.disk.percentage > this.alertThresholds.disk) {
      return '🔴 Critical';
    } else if (metrics.cpu.usage > this.alertThresholds.cpu * 0.7 || 
               metrics.memory.percentage > this.alertThresholds.memory * 0.7 || 
               metrics.disk.percentage > this.alertThresholds.disk * 0.7) {
      return '🟡 Warning';
    } else {
      return '🟢 Healthy';
    }
  }

  private formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  private formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  // Getters for external access
  get isActive(): boolean {
    return this.isMonitoring;
  }

  get currentMetrics(): SystemMetrics | null {
    return this.lastMetrics;
  }

  get recentErrors(): ErrorLog[] {
    return this.errorLogs.slice(-10);
  }

  // Update alert thresholds
  updateThresholds(thresholds: Partial<typeof this.alertThresholds>) {
    this.alertThresholds = { ...this.alertThresholds, ...thresholds };
    console.log('Updated alert thresholds:', this.alertThresholds);
  }
}

export const systemMonitor = new SystemMonitor();
