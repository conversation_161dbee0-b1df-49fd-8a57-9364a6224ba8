import React, { useState } from 'react';
import { useLocation } from 'wouter';
import AdminLayout from '@/components/admin/AdminLayout';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Settings as SettingsIcon,
  Mail,
  Globe,
  Shield,
  User,
  Activity,
  Home
} from 'lucide-react';

// Import existing settings components
import AdminEmailSettings from './EmailSettings';
import UrlSettings from './UrlSettings';
import SecuritySettings from './SecuritySettings';
import AccountSettings from './AccountSettings';
import SystemMonitoring from './SystemMonitoring';
import HomepageEditor from './HomepageEditor';

export default function AdminSettings() {
  const [location] = useLocation();

  // Extract tab from URL hash or default to 'general'
  const getActiveTab = () => {
    const hash = window.location.hash.replace('#', '');
    const validTabs = ['general', 'homepage', 'email', 'url', 'security', 'account', 'monitoring'];
    return validTabs.includes(hash) ? hash : 'general';
  };

  const [activeTab, setActiveTab] = useState(getActiveTab());

  // Update URL hash when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    window.location.hash = value;
  };

  const settingsTabs = [
    {
      id: 'general',
      label: 'General',
      icon: <SettingsIcon className="h-4 w-4" />,
      description: 'Basic application settings and configuration'
    },
    {
      id: 'homepage',
      label: 'Homepage',
      icon: <Home className="h-4 w-4" />,
      description: 'Customize your homepage content and layout'
    },
    {
      id: 'email',
      label: 'Email',
      icon: <Mail className="h-4 w-4" />,
      description: 'Configure SMTP providers and email templates'
    },
    {
      id: 'url',
      label: 'URL Settings',
      icon: <Globe className="h-4 w-4" />,
      description: 'Manage URL redirects and domain settings'
    },
    {
      id: 'security',
      label: 'Security',
      icon: <Shield className="h-4 w-4" />,
      description: 'Security settings and access controls'
    },
    {
      id: 'account',
      label: 'Account',
      icon: <User className="h-4 w-4" />,
      description: 'User account and profile settings'
    },
    {
      id: 'monitoring',
      label: 'Monitoring',
      icon: <Activity className="h-4 w-4" />,
      description: 'System monitoring and performance metrics'
    }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-muted-foreground">
            Manage your application settings and configuration
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
          <TabsList className="grid w-full grid-cols-7 lg:w-auto lg:grid-cols-7">
            {settingsTabs.map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className="flex items-center gap-2 text-xs lg:text-sm"
              >
                {tab.icon}
                <span className="hidden sm:inline">{tab.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <SettingsIcon className="h-5 w-5" />
                  General Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  General application settings will be available here. This section is currently under development.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="homepage" className="space-y-6">
            <HomepageEditor />
          </TabsContent>

          <TabsContent value="email" className="space-y-6">
            <AdminEmailSettings />
          </TabsContent>

          <TabsContent value="url" className="space-y-6">
            <UrlSettings />
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <SecuritySettings />
          </TabsContent>

          <TabsContent value="account" className="space-y-6">
            <AccountSettings />
          </TabsContent>

          <TabsContent value="monitoring" className="space-y-6">
            <SystemMonitoring />
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}