import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

// Enhanced validation schemas with security measures

// SQL injection prevention patterns
const SQL_INJECTION_PATTERNS = [
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
  /(--|\/\*|\*\/|;|'|"|`)/g,
  /(\bOR\b|\bAND\b).*?[=<>]/gi,
  /(\bUNION\b.*?\bSELECT\b)/gi
];

// XSS prevention patterns
const XSS_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
  /javascript:/gi,
  /on\w+\s*=/gi,
  /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi
];

// Path traversal patterns
const PATH_TRAVERSAL_PATTERNS = [
  /\.\./g,
  /\/\.\./g,
  /\.\.\\/g,
  /\.\.%2f/gi,
  /\.\.%5c/gi
];

// Sanitize string input
export function sanitizeString(input: string, options: {
  allowHtml?: boolean;
  maxLength?: number;
  preventSqlInjection?: boolean;
  preventXss?: boolean;
  preventPathTraversal?: boolean;
} = {}): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  let sanitized = input.trim();

  // Apply length limit
  if (options.maxLength && sanitized.length > options.maxLength) {
    sanitized = sanitized.substring(0, options.maxLength);
  }

  // Prevent SQL injection
  if (options.preventSqlInjection !== false) {
    for (const pattern of SQL_INJECTION_PATTERNS) {
      if (pattern.test(sanitized)) {
        throw new Error('Input contains potentially dangerous SQL patterns');
      }
    }
  }

  // Prevent XSS
  if (options.preventXss !== false) {
    for (const pattern of XSS_PATTERNS) {
      if (pattern.test(sanitized)) {
        throw new Error('Input contains potentially dangerous script patterns');
      }
    }
  }

  // Prevent path traversal
  if (options.preventPathTraversal !== false) {
    for (const pattern of PATH_TRAVERSAL_PATTERNS) {
      if (pattern.test(sanitized)) {
        throw new Error('Input contains path traversal patterns');
      }
    }
  }

  // HTML sanitization
  if (options.allowHtml) {
    sanitized = DOMPurify.sanitize(sanitized, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: []
    });
  } else {
    // Escape HTML entities
    sanitized = sanitized
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  }

  return sanitized;
}

// Enhanced email validation
export const secureEmailSchema = z.string()
  .min(1, 'Email is required')
  .max(254, 'Email is too long')
  .email('Invalid email format')
  .refine((email) => {
    // Additional email security checks
    const sanitized = sanitizeString(email, { preventSqlInjection: true, preventXss: true });
    return sanitized === email;
  }, 'Email contains invalid characters')
  .transform((email) => email.toLowerCase().trim());

// Enhanced username validation
export const secureUsernameSchema = z.string()
  .min(3, 'Username must be at least 3 characters')
  .max(50, 'Username must be no more than 50 characters')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens')
  .refine((username) => {
    // Check for reserved usernames
    const reserved = ['admin', 'root', 'system', 'api', 'www', 'mail', 'ftp'];
    return !reserved.includes(username.toLowerCase());
  }, 'Username is reserved')
  .transform((username) => sanitizeString(username, { maxLength: 50 }));

// Enhanced password validation
export const securePasswordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password must be no more than 128 characters')
  .refine((password) => {
    // Check password strength
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    
    return hasUppercase && hasLowercase && hasNumbers && hasSpecialChars;
  }, 'Password must contain uppercase, lowercase, numbers, and special characters')
  .refine((password) => {
    // Check for common weak patterns
    const weakPatterns = [
      /(.)\1{2,}/, // Repeated characters
      /^(123|abc|qwe|password|admin)/i, // Common weak starts
      /(password|123456|qwerty|admin)/i // Common weak words
    ];
    
    return !weakPatterns.some(pattern => pattern.test(password));
  }, 'Password contains weak patterns');

// Enhanced text content validation
export const secureTextSchema = (maxLength: number = 1000) => z.string()
  .max(maxLength, `Text must be no more than ${maxLength} characters`)
  .transform((text) => sanitizeString(text, { 
    allowHtml: false, 
    maxLength,
    preventSqlInjection: true,
    preventXss: true 
  }));

// Enhanced HTML content validation (for rich text)
export const secureHtmlSchema = (maxLength: number = 5000) => z.string()
  .max(maxLength, `Content must be no more than ${maxLength} characters`)
  .transform((html) => sanitizeString(html, { 
    allowHtml: true, 
    maxLength,
    preventSqlInjection: true 
  }));

// Enhanced URL validation
export const secureUrlSchema = z.string()
  .url('Invalid URL format')
  .max(2048, 'URL is too long')
  .refine((url) => {
    // Only allow HTTP and HTTPS protocols
    return url.startsWith('http://') || url.startsWith('https://');
  }, 'Only HTTP and HTTPS URLs are allowed')
  .refine((url) => {
    // Prevent dangerous URLs
    const dangerousPatterns = [
      /javascript:/i,
      /data:/i,
      /vbscript:/i,
      /file:/i
    ];
    
    return !dangerousPatterns.some(pattern => pattern.test(url));
  }, 'URL contains dangerous protocol');

// Enhanced numeric validation
export const secureNumberSchema = (min?: number, max?: number) => z.number()
  .int('Must be an integer')
  .min(min ?? Number.MIN_SAFE_INTEGER, min ? `Must be at least ${min}` : undefined)
  .max(max ?? Number.MAX_SAFE_INTEGER, max ? `Must be no more than ${max}` : undefined);

// Enhanced ID validation
export const secureIdSchema = z.number()
  .int('ID must be an integer')
  .positive('ID must be positive')
  .max(Number.MAX_SAFE_INTEGER, 'ID is too large');

// File upload validation
export const secureFileSchema = z.object({
  filename: z.string()
    .min(1, 'Filename is required')
    .max(255, 'Filename is too long')
    .refine((filename) => {
      // Check for dangerous file extensions
      const dangerousExtensions = [
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
        '.php', '.asp', '.aspx', '.jsp', '.sh', '.py', '.rb', '.pl'
      ];
      
      const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
      return !dangerousExtensions.includes(ext);
    }, 'File type not allowed')
    .transform((filename) => sanitizeString(filename, { 
      preventPathTraversal: true,
      preventXss: true 
    })),
  
  mimetype: z.string()
    .refine((mimetype) => {
      // Only allow safe MIME types
      const allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'text/plain', 'text/csv',
        'application/json', 'application/xml'
      ];
      
      return allowedTypes.includes(mimetype);
    }, 'File type not allowed'),
    
  size: z.number()
    .positive('File size must be positive')
    .max(10 * 1024 * 1024, 'File size must be less than 10MB') // 10MB limit
});

// Enhanced checkout form validation
export const secureCheckoutSchema = z.object({
  fullName: secureTextSchema(100)
    .min(2, 'Full name must be at least 2 characters'),
  
  email: secureEmailSchema,
  
  country: secureTextSchema(100)
    .min(1, 'Country is required'),
    
  productId: secureIdSchema,
  
  appType: secureTextSchema(50).optional(),
  
  macAddress: z.string()
    .regex(/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/, 'Invalid MAC address format')
    .optional()
    .transform((mac) => mac ? mac.toUpperCase() : undefined)
});

// Rate limiting validation
export const rateLimitSchema = z.object({
  windowMs: z.number().positive().max(3600000), // Max 1 hour
  maxRequests: z.number().positive().max(1000), // Max 1000 requests
  skipSuccessfulRequests: z.boolean().default(false),
  skipFailedRequests: z.boolean().default(false)
});

// Validation middleware factory
export function createValidationMiddleware<T>(schema: z.ZodSchema<T>) {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.validatedData = validatedData;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: 'Validation failed',
          details: error.errors
        });
      }
      
      return res.status(400).json({
        error: 'Invalid input data'
      });
    }
  };
}
