import Database from 'better-sqlite3';
import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * Run the URL settings migration
 */
async function runUrlSettingsMigration() {
  let transactionStarted = false;

  try {
    console.log('🗄️ Running URL settings migration...');

    // Open database connection
    const db = new Database('data.db');

    // Read the migration SQL
    const migrationSQL = readFileSync(join(__dirname, 'migrations', 'add-url-settings.sql'), 'utf8');

    // Split by semicolon and execute each statement
    const statements = migrationSQL.split(';').filter(stmt => stmt.trim());

    db.exec('BEGIN TRANSACTION');
    transactionStarted = true;

    for (const statement of statements) {
      if (statement.trim()) {
        console.log('Executing:', statement.trim().substring(0, 50) + '...');
        try {
          db.exec(statement);
        } catch (error: any) {
          if (error.message.includes('duplicate column name') ||
              error.message.includes('already exists')) {
            console.log('⚠️  Skipping (already exists):', statement.trim().substring(0, 50) + '...');
          } else {
            throw error;
          }
        }
      }
    }

    db.exec('COMMIT');
    transactionStarted = false;
    console.log('✅ URL settings migration completed successfully!');

    // Verify the column exists
    const columns = db.prepare("PRAGMA table_info(general_settings)").all();
    const hasUrlSettingsColumn = columns.some((col: any) => col.name === 'url_settings');
    console.log('🔗 URL settings column added to general_settings table:', hasUrlSettingsColumn);

    db.close();

  } catch (error) {
    console.error('❌ Error running URL settings migration:', error);
    
    if (transactionStarted) {
      try {
        const db = new Database('data.db');
        db.exec('ROLLBACK');
        db.close();
        console.log('🔄 Transaction rolled back');
      } catch (rollbackError) {
        console.error('❌ Error rolling back transaction:', rollbackError);
      }
    }
    
    throw error;
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runUrlSettingsMigration()
    .then(() => {
      console.log('🎉 Migration completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

export { runUrlSettingsMigration };
