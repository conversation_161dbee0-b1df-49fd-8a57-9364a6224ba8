import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Beaker, ExternalLink } from 'lucide-react';

interface TestCheckoutPage {
  id: number;
  title: string;
  slug: string;
  productName: string;
  price: number;
  paymentMethod: string;
}

export function TestDataCard() {
  const [testPages, setTestPages] = useState<TestCheckoutPage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Fetch test checkout pages
  useEffect(() => {
    const fetchTestPages = async () => {
      try {
        setIsLoading(true);
        const response = await apiRequest('/api/custom-checkout', 'GET');
        const pages = response.filter((page: any) => 
          page.title.toLowerCase().includes('test') || 
          page.slug.toLowerCase().includes('test')
        );
        setTestPages(pages);
      } catch (error) {
        console.error('Error fetching test pages:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch test checkout pages',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTestPages();
  }, [toast]);

  // Open a test checkout page
  const openTestPage = (slug: string) => {
    window.open(`/checkout/${slug}`, '_blank');
  };

  // Create a test sale
  const createTestSale = async () => {
    try {
      const response = await apiRequest('/api/admin/create-test-sale', 'POST', {
        customerName: 'Test Customer',
        customerEmail: '<EMAIL>',
        productId: 3, // Productivity App Template
        amount: 79.99
      });

      toast({
        title: 'Test Sale Created',
        description: `Invoice #${response.invoiceId} created successfully`,
      });
    } catch (error) {
      console.error('Error creating test sale:', error);
      toast({
        title: 'Error',
        description: 'Failed to create test sale',
        variant: 'destructive'
      });
    }
  };

  return (
    <Card className="shadow-md">
      <CardHeader className="bg-muted/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Beaker className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Test Data</CardTitle>
          </div>
          <Badge variant="outline" className="bg-primary/10">Testing Tools</Badge>
        </div>
        <CardDescription>
          Quick access to test data and checkout pages for testing
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Test Customer</h3>
            <div className="text-sm text-muted-foreground">
              <p><strong>Name:</strong> Test Customer</p>
              <p><strong>Email:</strong> <EMAIL></p>
            </div>
          </div>

          <div>
            <h3 className="font-medium mb-2">Test Checkout Pages</h3>
            {isLoading ? (
              <div className="flex justify-center py-4">
                <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              </div>
            ) : testPages.length > 0 ? (
              <div className="space-y-2">
                {testPages.map(page => (
                  <div key={page.id} className="flex items-center justify-between border rounded-md p-2">
                    <div className="text-sm">
                      <p className="font-medium">{page.productName}</p>
                      <p className="text-muted-foreground">${parseFloat(page.price).toFixed(2)} - {page.paymentMethod === 'paypal' ? 'PayPal' : 'Custom Link'}</p>
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => openTestPage(page.slug)}
                      className="flex items-center gap-1"
                    >
                      <ExternalLink className="h-3 w-3" />
                      <span>Open</span>
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No test checkout pages found. Restart the server to create default test data.</p>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <Button 
          variant="outline" 
          onClick={createTestSale}
        >
          Create Test Sale
        </Button>
        <Button 
          variant="default"
          onClick={() => window.open('/admin/sales', '_self')}
        >
          View Sales History
        </Button>
      </CardFooter>
    </Card>
  );
}
