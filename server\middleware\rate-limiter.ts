import { Request, Response, NextFunction } from 'express';

interface LoginAttempt {
  count: number;
  firstAttempt: number;
  lastAttempt: number;
  blockedUntil?: number;
}

// In-memory store for login attempts (in production, use Redis or database)
const loginAttempts = new Map<string, LoginAttempt>();

// Rate limiting configuration
const RATE_LIMIT_CONFIG = {
  maxAttempts: 5,
  windowMs: 15 * 60 * 1000, // 15 minutes
  blockDurationMs: 15 * 60 * 1000, // 15 minutes initial block
  maxBlockDurationMs: 24 * 60 * 60 * 1000, // 24 hours max block
  exponentialBackoffMultiplier: 2,
};

// Get client identifier (IP + User Agent for better uniqueness)
function getClientId(req: Request): string {
  const ip = req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
  const userAgent = req.get('User-Agent') || 'unknown';
  return `${ip}:${userAgent.substring(0, 100)}`; // Limit UA length
}

// Calculate exponential backoff duration
function calculateBlockDuration(attemptCount: number): number {
  const baseBlock = RATE_LIMIT_CONFIG.blockDurationMs;
  const multiplier = Math.pow(RATE_LIMIT_CONFIG.exponentialBackoffMultiplier, attemptCount - RATE_LIMIT_CONFIG.maxAttempts);
  const duration = Math.min(baseBlock * multiplier, RATE_LIMIT_CONFIG.maxBlockDurationMs);
  return duration;
}

// Format time remaining for user display
function formatTimeRemaining(ms: number): string {
  const minutes = Math.ceil(ms / (1000 * 60));
  if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
  const hours = Math.ceil(minutes / 60);
  return `${hours} hour${hours !== 1 ? 's' : ''}`;
}

// Clean up old entries periodically
function cleanupOldEntries() {
  const now = Date.now();
  const cutoff = now - RATE_LIMIT_CONFIG.windowMs;
  
  for (const [clientId, attempt] of loginAttempts.entries()) {
    // Remove entries that are outside the window and not currently blocked
    if (attempt.lastAttempt < cutoff && (!attempt.blockedUntil || attempt.blockedUntil < now)) {
      loginAttempts.delete(clientId);
    }
  }
}

// Run cleanup every 5 minutes
setInterval(cleanupOldEntries, 5 * 60 * 1000);

export function loginRateLimiter(req: Request, res: Response, next: NextFunction) {
  const clientId = getClientId(req);
  const now = Date.now();
  
  // Get or create attempt record
  let attempt = loginAttempts.get(clientId);
  if (!attempt) {
    attempt = {
      count: 0,
      firstAttempt: now,
      lastAttempt: now,
    };
  }

  // Check if currently blocked
  if (attempt.blockedUntil && attempt.blockedUntil > now) {
    const timeRemaining = attempt.blockedUntil - now;
    const formattedTime = formatTimeRemaining(timeRemaining);
    
    console.log(`🚫 Rate limit block active for ${clientId}. Blocked until: ${new Date(attempt.blockedUntil).toISOString()}`);
    
    return res.status(429).json({
      message: 'Too many failed login attempts',
      error: `Account temporarily locked. Please try again in ${formattedTime}.`,
      retryAfter: Math.ceil(timeRemaining / 1000),
      blockedUntil: attempt.blockedUntil
    });
  }

  // Reset window if enough time has passed
  if (now - attempt.firstAttempt > RATE_LIMIT_CONFIG.windowMs) {
    attempt.count = 0;
    attempt.firstAttempt = now;
    attempt.blockedUntil = undefined;
  }

  // Store the attempt record for potential failure handling
  (req as any).rateLimitAttempt = attempt;
  (req as any).rateLimitClientId = clientId;

  next();
}

export function handleLoginFailure(req: Request, res: Response) {
  const clientId = (req as any).rateLimitClientId;
  const attempt = (req as any).rateLimitAttempt as LoginAttempt;
  const now = Date.now();

  if (!clientId || !attempt) {
    return;
  }

  // Increment failure count
  attempt.count++;
  attempt.lastAttempt = now;

  console.log(`❌ Login failure for ${clientId}. Attempt ${attempt.count}/${RATE_LIMIT_CONFIG.maxAttempts}`);

  // Check if we should block
  if (attempt.count >= RATE_LIMIT_CONFIG.maxAttempts) {
    const blockDuration = calculateBlockDuration(attempt.count);
    attempt.blockedUntil = now + blockDuration;
    
    const formattedTime = formatTimeRemaining(blockDuration);
    
    console.log(`🔒 Blocking ${clientId} for ${formattedTime} (attempt ${attempt.count})`);
    
    // Store the updated attempt
    loginAttempts.set(clientId, attempt);
    
    return res.status(429).json({
      message: 'Too many failed login attempts',
      error: `Too many failed attempts. Account locked for ${formattedTime}.`,
      retryAfter: Math.ceil(blockDuration / 1000),
      blockedUntil: attempt.blockedUntil
    });
  }

  // Store the updated attempt
  loginAttempts.set(clientId, attempt);

  // Return normal failure response with remaining attempts
  const remainingAttempts = RATE_LIMIT_CONFIG.maxAttempts - attempt.count;
  return res.status(401).json({
    message: 'Invalid credentials',
    remainingAttempts,
    warning: remainingAttempts <= 2 ? `Only ${remainingAttempts} attempt${remainingAttempts !== 1 ? 's' : ''} remaining before account lockout.` : undefined
  });
}

export function handleLoginSuccess(req: Request) {
  const clientId = (req as any).rateLimitClientId;
  
  if (clientId) {
    // Clear the attempt record on successful login
    loginAttempts.delete(clientId);
    console.log(`✅ Login success for ${clientId}. Rate limit record cleared.`);
  }
}

// Export for monitoring/admin purposes
export function getRateLimitStats() {
  const now = Date.now();
  const stats = {
    totalTrackedClients: loginAttempts.size,
    currentlyBlocked: 0,
    recentAttempts: 0,
  };

  for (const attempt of loginAttempts.values()) {
    if (attempt.blockedUntil && attempt.blockedUntil > now) {
      stats.currentlyBlocked++;
    }
    if (now - attempt.lastAttempt < RATE_LIMIT_CONFIG.windowMs) {
      stats.recentAttempts++;
    }
  }

  return stats;
}
