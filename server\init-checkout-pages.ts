import { storage } from './storage-factory';
import { nanoid } from 'nanoid';

/**
 * Initialize default checkout pages
 */
export async function initializeDefaultCheckoutPages() {
  try {
    console.log('Initializing default checkout pages...');

    // Check if default pages already exist
    const existingPages = await storage.getCustomCheckoutPages();
    const existingTrialPage = existingPages.find(page => page.title === 'Default Trial Checkout');
    const existingRegularPage = existingPages.find(page => page.title === 'Default Regular Checkout');

    // Update existing pages to require email validation
    if (existingTrialPage && !existingTrialPage.requireAllowedEmail) {
      console.log('Updating existing trial page to require email validation...');
      await storage.updateCustomCheckoutPage(existingTrialPage.id, {
        requireAllowedEmail: true
      });
    }

    if (existingRegularPage && !existingRegularPage.requireAllowedEmail) {
      console.log('Updating existing regular page to require email validation...');
      await storage.updateCustomCheckoutPage(existingRegularPage.id, {
        requireAllowedEmail: true
      });
    }

    if (existingTrialPage && existingRegularPage) {
      console.log('Default checkout pages already exist and updated.');
      return;
    }

    // Create trial checkout page if it doesn't exist
    if (!existingTrialPage) {
      const trialSlug = `default-trial-checkout-${nanoid(6)}`;
      const trialCheckoutPage = await storage.createCustomCheckoutPage({
      title: 'Default Trial Checkout',
      slug: trialSlug,
      productName: 'IPTV Trial Subscription',
      productDescription: 'Try our premium IPTV service for 24 hours with full access to all channels and features.',
      price: 4.99,
      imageUrl: '',
      paymentMethod: 'trial-custom-link',
      smtpProviderId: 'smtp-1',
      requireAllowedEmail: true,
      isTrialCheckout: true,
      confirmationMessage: '<div class="space-y-3"><p><strong>🎯 You are about to start your 24-hour trial!</strong></p><p>✅ <strong>What you get:</strong></p><ul class="list-disc list-inside space-y-1"><li>Full access to 10,000+ channels</li><li>Premium VOD content</li><li>HD/4K streaming quality</li><li>24/7 customer support</li></ul><p class="text-sm text-muted-foreground mt-3">💡 <em>Your trial will begin immediately after payment confirmation.</em></p><p class="text-xs text-muted-foreground">By proceeding, you agree to our terms of service and privacy policy.</p></div>',
      headerTitle: '',
      footerText: '',
      headerLogo: '',
      footerLogo: '',
      themeMode: 'light',
      useReferrerMasking: false,
      redirectDelay: 2000,
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

      console.log('Created trial checkout page:', trialCheckoutPage);
    }

    // Create regular checkout page if it doesn't exist
    if (!existingRegularPage) {
      const regularSlug = `default-regular-checkout-${nanoid(6)}`;
      const regularCheckoutPage = await storage.createCustomCheckoutPage({
      title: 'Default Regular Checkout',
      slug: regularSlug,
      productName: 'IPTV Premium Subscription',
      productDescription: 'Get access to our premium IPTV service with over 10,000 channels, VOD, and more.',
      price: 19.99,
      imageUrl: '',
      paymentMethod: 'custom-link',
      smtpProviderId: 'smtp-1',
      requireAllowedEmail: true,
      isTrialCheckout: false,
      confirmationMessage: '<div class="space-y-3"><p><strong>🚀 Ready to upgrade to Premium?</strong></p><p>✅ <strong>Your Premium subscription includes:</strong></p><ul class="list-disc list-inside space-y-1"><li>10,000+ live TV channels</li><li>50,000+ movies & TV shows</li><li>4K Ultra HD streaming</li><li>Multi-device support</li><li>Premium sports packages</li><li>24/7 priority support</li></ul><p class="text-sm text-muted-foreground mt-3">💳 <em>Secure payment processing via PayPal</em></p><p class="text-xs text-muted-foreground">By proceeding, you agree to our terms of service and privacy policy.</p></div>',
      headerTitle: '',
      footerText: '',
      headerLogo: '',
      footerLogo: '',
      themeMode: 'light',
      useReferrerMasking: false,
      redirectDelay: 2000,
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

      console.log('Created regular checkout page:', regularCheckoutPage);
    }

    console.log('Default checkout pages initialized successfully!');
  } catch (error) {
    console.error('Error initializing default checkout pages:', error);
  }
}
