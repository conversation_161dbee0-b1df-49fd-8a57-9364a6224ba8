import Database from 'better-sqlite3';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function runMigration() {
  console.log('🔄 Running 2FA migration...');
  
  const db = new Database('data.db');
  
  let transactionStarted = false;

  try {
    // Read the migration SQL
    const migrationSQL = readFileSync(join(__dirname, 'migrations', 'add-2fa-tables.sql'), 'utf8');

    // Split by semicolon and execute each statement
    const statements = migrationSQL.split(';').filter(stmt => stmt.trim());

    db.exec('BEGIN TRANSACTION');
    transactionStarted = true;

    for (const statement of statements) {
      if (statement.trim()) {
        console.log('Executing:', statement.trim().substring(0, 50) + '...');
        try {
          db.exec(statement);
        } catch (error: any) {
          if (error.message.includes('duplicate column name') ||
              error.message.includes('already exists')) {
            console.log('⚠️  Skipping (already exists):', statement.trim().substring(0, 50) + '...');
          } else {
            throw error;
          }
        }
      }
    }

    db.exec('COMMIT');
    transactionStarted = false;
    console.log('✅ 2FA migration completed successfully!');

    // Verify the tables exist
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('devices', 'recovery_codes')").all();
    console.log('📋 Created tables:', tables.map(t => t.name));

    // Check users table columns
    const userColumns = db.prepare("PRAGMA table_info(users)").all();
    const has2FAColumns = userColumns.some(col => col.name === 'two_factor_secret');
    console.log('🔐 2FA columns added to users table:', has2FAColumns);

  } catch (error) {
    if (transactionStarted) {
      try {
        db.exec('ROLLBACK');
      } catch (rollbackError) {
        console.error('Error during rollback:', rollbackError);
      }
    }
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    db.close();
  }
}

// Run the migration immediately
runMigration().catch(console.error);

export { runMigration };
