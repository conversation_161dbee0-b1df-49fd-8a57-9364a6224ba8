const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '..', 'app', 'data', 'data.db');
const db = new Database(dbPath);

console.log('🔍 Checking homepage_config table...');

try {
  const homepageConfigs = db.prepare('SELECT * FROM homepage_config').all();
  console.log('Homepage configs found:', homepageConfigs.length);
  
  if (homepageConfigs.length > 0) {
    const config = homepageConfigs[0];
    console.log('Homepage config details:');
    console.log('- ID:', config.id);
    console.log('- Version:', config.version);
    console.log('- Created:', config.created_at);
    console.log('- Updated:', config.updated_at);
    
    // Parse sections data
    try {
      const sections = JSON.parse(config.sections_data);
      console.log('- Sections count:', sections.length);
      sections.forEach((section, index) => {
        console.log(`  ${index + 1}. ${section.type} - ${section.title} (enabled: ${section.enabled})`);
      });
      
      // Show first section details
      if (sections.length > 0) {
        console.log('\nFirst section details:');
        console.log(JSON.stringify(sections[0], null, 2));
      }
    } catch (e) {
      console.log('- Sections data parse error:', e.message);
      console.log('- Raw sections data:', config.sections_data);
    }
  } else {
    console.log('❌ No homepage configuration found in database');
  }
} catch (error) {
  console.error('❌ Error checking homepage_config:', error.message);
}

db.close();
