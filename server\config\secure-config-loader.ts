import * as fs from 'fs';
import * as path from 'path';

// Security configuration interface
interface SecurityConfig {
  database: {
    encryption: boolean;
    backupEncryption: boolean;
    queryLogging: boolean;
    accessControl: boolean;
  };
  passwords: {
    bcryptRounds: number;
    minLength: number;
    requireStrong: boolean;
  };
  monitoring: {
    securityLogging: boolean;
    failedLoginTracking: boolean;
    maxLoginAttempts: number;
    lockoutDuration: number;
  };
  backup: {
    enabled: boolean;
    interval: number;
    retention: number;
    encrypted: boolean;
  };
}

// Default security configuration
const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  database: {
    encryption: true,
    backupEncryption: true,
    queryLogging: false,
    accessControl: true
  },
  passwords: {
    bcryptRounds: 12,
    minLength: 8,
    requireStrong: true
  },
  monitoring: {
    securityLogging: true,
    failedLoginTracking: true,
    maxLoginAttempts: 5,
    lockoutDuration: 300
  },
  backup: {
    enabled: true,
    interval: 24,
    retention: 7,
    encrypted: true
  }
};

// Load security configuration from secure location
export function loadSecurityConfig(): SecurityConfig {
  const configPath = path.join(process.cwd(), 'app', 'config', 'security-config.json');
  
  try {
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8');
      const parsedConfig = JSON.parse(configData);
      
      // Merge with defaults to ensure all properties exist
      return {
        database: { ...DEFAULT_SECURITY_CONFIG.database, ...parsedConfig.database },
        passwords: { ...DEFAULT_SECURITY_CONFIG.passwords, ...parsedConfig.passwords },
        monitoring: { ...DEFAULT_SECURITY_CONFIG.monitoring, ...parsedConfig.monitoring },
        backup: { ...DEFAULT_SECURITY_CONFIG.backup, ...parsedConfig.backup }
      };
    } else {
      console.warn('⚠️ Security config file not found, using defaults');
      return DEFAULT_SECURITY_CONFIG;
    }
  } catch (error) {
    console.error('❌ Failed to load security config, using defaults:', error);
    return DEFAULT_SECURITY_CONFIG;
  }
}

// Save security configuration to secure location
export function saveSecurityConfig(config: SecurityConfig): boolean {
  const configPath = path.join(process.cwd(), 'app', 'config', 'security-config.json');
  const configDir = path.dirname(configPath);
  
  try {
    // Ensure config directory exists
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true, mode: 0o750 });
    }
    
    // Write configuration file with secure permissions
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2), { mode: 0o600 });
    
    console.log('✅ Security configuration saved successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to save security config:', error);
    return false;
  }
}

// Get secure paths for various components
export function getSecurePaths() {
  const appRoot = path.join(process.cwd(), 'app');
  
  return {
    appRoot,
    data: path.join(appRoot, 'data'),
    config: path.join(appRoot, 'config'),
    storage: path.join(appRoot, 'storage'),
    logs: path.join(appRoot, 'logs'),
    database: path.join(appRoot, 'data', 'data.db'),
    backups: path.join(appRoot, 'data', 'backups'),
    uploads: path.join(appRoot, 'storage', 'uploads'),
    securityLog: path.join(appRoot, 'logs', 'security.log'),
    securityConfig: path.join(appRoot, 'config', 'security-config.json')
  };
}

// Validate secure directory structure
export function validateSecureStructure(): boolean {
  const paths = getSecurePaths();
  const requiredDirs = [paths.data, paths.config, paths.storage, paths.logs];
  
  let isValid = true;
  
  for (const dir of requiredDirs) {
    if (!fs.existsSync(dir)) {
      console.error(`❌ Required secure directory missing: ${dir}`);
      isValid = false;
    }
  }
  
  return isValid;
}

// Initialize secure directory structure
export function initializeSecureStructure(): boolean {
  const paths = getSecurePaths();
  const requiredDirs = [
    paths.data,
    paths.config,
    paths.storage,
    paths.logs,
    paths.backups,
    paths.uploads
  ];
  
  try {
    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true, mode: 0o750 });
        console.log(`📁 Created secure directory: ${dir}`);
      }
    }
    
    console.log('✅ Secure directory structure initialized');
    return true;
  } catch (error) {
    console.error('❌ Failed to initialize secure structure:', error);
    return false;
  }
}

// Export the loaded configuration
export const securityConfig = loadSecurityConfig();
