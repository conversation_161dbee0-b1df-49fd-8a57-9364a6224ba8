#!/usr/bin/env node

/**
 * SQLCipher Integration Test Script
 * Tests database-level encryption, migration, and compatibility
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

console.log('🔐 Starting SQLCipher integration tests...\n');

// Test 1: SQLCipher Package Installation
async function testSQLCipherInstallation() {
  console.log('📋 Test 1: SQLCipher Package Installation');
  
  try {
    // Check if SQLCipher package is installed
    const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
    
    if (packageJson.dependencies && packageJson.dependencies['@journeyapps/sqlcipher']) {
      console.log(`✅ SQLCipher package installed: ${packageJson.dependencies['@journeyapps/sqlcipher']}`);
      return true;
    } else {
      console.log('❌ SQLCipher package not found in dependencies');
      return false;
    }
  } catch (error) {
    console.log(`❌ Error checking SQLCipher installation: ${error.message}`);
    return false;
  }
}

// Test 2: Database Security Configuration Files
async function testSecurityConfigFiles() {
  console.log('\n📋 Test 2: Database Security Configuration Files');
  
  try {
    const requiredFiles = [
      'server/config/database-security.ts',
      'server/migrations/migrate-to-sqlcipher.ts',
      'server/config/email-protection.ts'
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        console.log(`✅ File exists: ${file}`);
        passed++;
      } else {
        console.log(`❌ File missing: ${file}`);
        failed++;
      }
    }
    
    console.log(`📊 File Check Results: ${passed} passed, ${failed} failed`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ File check failed: ${error.message}`);
    return false;
  }
}

// Test 3: Environment Configuration
async function testEnvironmentConfig() {
  console.log('\n📋 Test 3: Environment Configuration');
  
  try {
    const envContent = fs.readFileSync('./.env', 'utf8');
    
    const requiredSettings = [
      'USE_SQLCIPHER',
      'ENABLE_EMAIL_ENCRYPTION',
      'ENABLE_EMAIL_OBFUSCATION',
      'DATABASE_PATH',
      'BACKUP_PATH'
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const setting of requiredSettings) {
      if (envContent.includes(setting)) {
        console.log(`✅ Environment setting found: ${setting}`);
        passed++;
      } else {
        console.log(`❌ Environment setting missing: ${setting}`);
        failed++;
      }
    }
    
    console.log(`📊 Environment Config Results: ${passed} passed, ${failed} failed`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ Environment config test failed: ${error.message}`);
    return false;
  }
}

// Test 4: Encryption Key Management
async function testEncryptionKeyManagement() {
  console.log('\n📋 Test 4: Encryption Key Management');
  
  try {
    const keyPath = './.db-key';
    let keyExists = fs.existsSync(keyPath);
    
    if (!keyExists) {
      // Generate test key
      const key = crypto.randomBytes(32).toString('hex');
      fs.writeFileSync(keyPath, key, { mode: 0o600 });
      console.log('✅ Generated test encryption key');
      keyExists = true;
    } else {
      console.log('✅ Encryption key already exists');
    }
    
    if (keyExists) {
      // Test key reading
      const key = fs.readFileSync(keyPath, 'utf8').trim();
      
      if (key.length === 64) {
        console.log('✅ Encryption key has correct length (64 chars)');
        
        // Test key permissions (on Unix-like systems)
        if (process.platform !== 'win32') {
          const stats = fs.statSync(keyPath);
          const permissions = (stats.mode & parseInt('777', 8)).toString(8);
          
          if (permissions === '600') {
            console.log('✅ Encryption key has secure permissions (600)');
          } else {
            console.log(`⚠️ Encryption key permissions: ${permissions} (should be 600)`);
          }
        } else {
          console.log('⚠️ Permission check skipped on Windows');
        }
        
        return true;
      } else {
        console.log(`❌ Encryption key has incorrect length: ${key.length} (should be 64)`);
        return false;
      }
    }
    
    return false;
    
  } catch (error) {
    console.log(`❌ Encryption key test failed: ${error.message}`);
    return false;
  }
}

// Test 5: Database Migration Files
async function testMigrationFiles() {
  console.log('\n📋 Test 5: Database Migration Files');
  
  try {
    const migrationFiles = [
      'server/migrations/migrate-to-sqlcipher.ts',
      'server/migrations/encrypt-existing-emails.ts'
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const file of migrationFiles) {
      if (fs.existsSync(file)) {
        // Check if file has required functions
        const content = fs.readFileSync(file, 'utf8');
        
        if (file.includes('sqlcipher')) {
          if (content.includes('migrateToSQLCipher') && content.includes('rollbackSQLCipherMigration')) {
            console.log(`✅ SQLCipher migration file complete: ${file}`);
            passed++;
          } else {
            console.log(`❌ SQLCipher migration file incomplete: ${file}`);
            failed++;
          }
        } else if (file.includes('encrypt-existing-emails')) {
          if (content.includes('encryptExistingEmails') && content.includes('rollbackEmailEncryption')) {
            console.log(`✅ Email encryption migration file complete: ${file}`);
            passed++;
          } else {
            console.log(`❌ Email encryption migration file incomplete: ${file}`);
            failed++;
          }
        }
      } else {
        console.log(`❌ Migration file missing: ${file}`);
        failed++;
      }
    }
    
    console.log(`📊 Migration Files Results: ${passed} passed, ${failed} failed`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ Migration files test failed: ${error.message}`);
    return false;
  }
}

// Test 6: Backup System
async function testBackupSystem() {
  console.log('\n📋 Test 6: Backup System');
  
  try {
    const backupDir = './backups';
    
    // Check if backup directory exists or can be created
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true, mode: 0o750 });
      console.log('✅ Created backup directory');
    } else {
      console.log('✅ Backup directory exists');
    }
    
    // Check backup script
    const backupScript = './backup-database.sh';
    if (fs.existsSync(backupScript)) {
      const content = fs.readFileSync(backupScript, 'utf8');
      
      if (content.includes('secure_backup') && content.includes('encrypt')) {
        console.log('✅ Secure backup script found');
        return true;
      } else {
        console.log('❌ Backup script exists but lacks encryption features');
        return false;
      }
    } else {
      console.log('❌ Backup script not found');
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Backup system test failed: ${error.message}`);
    return false;
  }
}

// Test 7: Database File Security
async function testDatabaseFileSecurity() {
  console.log('\n📋 Test 7: Database File Security');
  
  try {
    const dbPath = './data.db';
    
    if (fs.existsSync(dbPath)) {
      const stats = fs.statSync(dbPath);
      
      // Check file size
      const sizeKB = (stats.size / 1024).toFixed(2);
      console.log(`📊 Database file size: ${sizeKB} KB`);
      
      // Check permissions (on Unix-like systems)
      if (process.platform !== 'win32') {
        const permissions = (stats.mode & parseInt('777', 8)).toString(8);
        
        if (permissions === '600') {
          console.log('✅ Database file has secure permissions (600)');
        } else {
          console.log(`⚠️ Database file permissions: ${permissions} (should be 600)`);
        }
      } else {
        console.log('⚠️ Permission check skipped on Windows');
      }
      
      // Check if file is encrypted (basic test)
      const buffer = fs.readFileSync(dbPath);
      const header = buffer.toString('utf8', 0, 16);
      
      if (header.includes('SQLite')) {
        console.log('⚠️ Database appears to be unencrypted (SQLite header visible)');
        console.log('💡 Run migration to enable SQLCipher encryption');
      } else {
        console.log('✅ Database appears to be encrypted (no SQLite header)');
      }
      
      return true;
    } else {
      console.log('⚠️ Database file does not exist yet');
      return true; // Not a failure if DB doesn't exist
    }
    
  } catch (error) {
    console.log(`❌ Database file security test failed: ${error.message}`);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  const tests = [
    { name: 'SQLCipher Package Installation', fn: testSQLCipherInstallation },
    { name: 'Security Configuration Files', fn: testSecurityConfigFiles },
    { name: 'Environment Configuration', fn: testEnvironmentConfig },
    { name: 'Encryption Key Management', fn: testEncryptionKeyManagement },
    { name: 'Database Migration Files', fn: testMigrationFiles },
    { name: 'Backup System', fn: testBackupSystem },
    { name: 'Database File Security', fn: testDatabaseFileSecurity }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ Test "${test.name}" threw an error:`, error.message);
      failed++;
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log(`🔐 SQLCipher Integration Test Results:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All SQLCipher integration tests passed!');
    console.log('\n📋 Next steps:');
    console.log('1. Run: npm run migrate:sqlcipher migrate (to enable database encryption)');
    console.log('2. Run: npm run migrate:encrypt-emails encrypt (to encrypt existing emails)');
    console.log('3. Test the application to ensure everything works');
    console.log('4. Create encrypted backups');
    process.exit(0);
  } else {
    console.log('\n⚠️ Some SQLCipher integration tests failed. Please review and fix issues.');
    process.exit(1);
  }
}

// Run tests
runAllTests().catch(error => {
  console.error('❌ Test runner error:', error);
  process.exit(1);
});
