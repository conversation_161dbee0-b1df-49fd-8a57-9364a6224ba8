import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter
} from '@/components/ui/card';
import {
  Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage
} from '@/components/ui/form';
import {
  AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent,
  AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import AdminLayout from '@/components/admin/AdminLayout';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Mail, Send, Check, Trash, Plus } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

// Type definitions
interface EmailProvider {
  id: string;
  name: string;
  active: boolean;
  isDefault?: boolean;
  isBackup?: boolean;
  credentials: Record<string, string | boolean>;
}

interface EmailConfig {
  providers: EmailProvider[];
}

// Form schema
const smtpSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'SMTP name is required'),
  host: z.string().min(1, 'SMTP host is required'),
  port: z.string().min(1, 'Port is required'),
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  fromEmail: z.string().email('Please enter a valid email'),
  fromName: z.string().min(1, 'From name is required'),
  adminEmail: z.string().email('Please enter a valid admin email').optional().or(z.literal('')),
  secure: z.boolean().default(true),
  active: z.boolean().default(false),
  isDefault: z.boolean().default(false),
  isBackup: z.boolean().default(false)
});

const testEmailSchema = z.object({
  email: z.string().email('Please enter a valid email')
});

export default function EmailSettingsPage() {
  const [testEmail, setTestEmail] = useState('');
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<EmailProvider | null>(null);

  const { toast } = useToast();

  // Query to fetch email configuration
  const { data: emailConfig, isLoading, refetch } = useQuery<EmailConfig>({
    queryKey: ['/api/admin/email-config'],
    retry: false
  });

  // Default values for form
  const smtpDefaults = {
    id: '',
    name: '',
    host: '',
    port: '587',
    username: '',
    password: '',
    fromEmail: '',
    fromName: '',
    adminEmail: '',
    secure: true,
    active: false,
    isDefault: false,
    isBackup: false
  };

  // SMTP form
  const smtpForm = useForm<z.infer<typeof smtpSchema>>({
    resolver: zodResolver(smtpSchema),
    defaultValues: smtpDefaults
  });

  // Test email form
  const testEmailForm = useForm<z.infer<typeof testEmailSchema>>({
    resolver: zodResolver(testEmailSchema),
    defaultValues: {
      email: ''
    }
  });

  // Mutation to create/update config
  const updateConfigMutation = useMutation({
    mutationFn: (data: z.infer<typeof smtpSchema>) => {
      const { id, name, active, isDefault, isBackup, ...credentials } = data;
      const providerId = id || `smtp-${Date.now()}`;

      return apiRequest('/api/admin/email-config', 'POST', {
        providerId,
        name,
        config: credentials,
        active,
        isDefault,
        isBackup
      });
    },
    onSuccess: () => {
      refetch();
      setIsCreateDialogOpen(false);
      setIsEditDialogOpen(false);
      smtpForm.reset(smtpDefaults);
      toast({
        title: "Settings updated",
        description: "Email provider settings have been updated successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to update settings: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to delete SMTP config
  const deleteConfigMutation = useMutation({
    mutationFn: (providerId: string) =>
      apiRequest('/api/admin/email-config/delete', 'POST', { providerId }),
    onSuccess: () => {
      refetch();
      toast({
        title: "SMTP Deleted",
        description: "SMTP configuration has been deleted successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to delete SMTP: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  // Mutation to send test email
  const sendTestEmailMutation = useMutation({
    mutationFn: ({ email, providerId }: { email: string, providerId?: string }) =>
      apiRequest('/api/admin/email-test', 'POST', { email, providerId }),
    onSuccess: () => {
      setIsTestDialogOpen(false);
      setTestEmail('');
      toast({
        title: "Test email sent",
        description: "A test email has been sent to the provided address."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to send test email: ${error.message}`,
        variant: "destructive"
      });
    }
  });

  const handleCreateSMTP = () => {
    smtpForm.reset(smtpDefaults);
    setIsCreateDialogOpen(true);
  };

  const handleEditSMTP = (provider: EmailProvider) => {
    setSelectedProvider(provider);

    const credentials = provider.credentials as any;
    smtpForm.reset({
      id: provider.id,
      name: provider.name,
      host: credentials.host || '',
      port: credentials.port || '587',
      username: credentials.auth?.user || '',
      password: credentials.auth?.pass || '',
      fromEmail: credentials.fromEmail || '',
      fromName: credentials.fromName || '',
      adminEmail: credentials.adminEmail || '',
      secure: credentials.secure || false,
      active: provider.active || false,
      isDefault: provider.isDefault || false,
      isBackup: provider.isBackup || false
    });

    setIsEditDialogOpen(true);
  };

  const handleDeleteSMTP = (providerId: string) => {
    if (confirm('Are you sure you want to delete this SMTP configuration?')) {
      deleteConfigMutation.mutate(providerId);
    }
  };

  const onCreateSubmit = (data: z.infer<typeof smtpSchema>) => {
    updateConfigMutation.mutate(data);
  };

  const onEditSubmit = (data: z.infer<typeof smtpSchema>) => {
    updateConfigMutation.mutate(data);
  };

  const onTestEmailSubmit = (e: React.FormEvent, providerId?: string) => {
    e.preventDefault();
    if (!testEmail) {
      toast({
        title: "Error",
        description: "Please enter an email address",
        variant: "destructive"
      });
      return;
    }

    sendTestEmailMutation.mutate({
      email: testEmail,
      providerId
    });
  };

  return (
    <AdminLayout>
      <Card className="shadow-lg">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold">Email Settings</CardTitle>
            <CardDescription>
              Configure email service providers for invoices and notifications
            </CardDescription>
          </div>
          <Button onClick={handleCreateSMTP}>
            <Plus className="mr-2 h-4 w-4" /> Add SMTP
          </Button>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <div className="mt-6 space-y-8">
              {emailConfig?.providers && emailConfig.providers.length > 0 ? (
                <div className="grid grid-cols-1 gap-6">
                  {emailConfig.providers.map((provider) => (
                    <Card key={provider.id} className="overflow-hidden">
                      <CardHeader className="pb-3">
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-xl">{provider.name}</CardTitle>
                            <CardDescription className="mt-1">
                              {(provider.credentials as any).host}:{(provider.credentials as any).port}
                            </CardDescription>
                          </div>
                          <div className="flex flex-col gap-2">
                            <Badge variant={provider.active ? "default" : "outline"}>
                              {provider.active ? "Active" : "Inactive"}
                            </Badge>
                            {provider.isDefault && (
                              <Badge variant="secondary">Default</Badge>
                            )}
                            {provider.isBackup && (
                              <Badge variant="secondary">Backup</Badge>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pb-3">
                        <div className="space-y-3">
                          <div>
                            <h4 className="text-sm font-medium">From</h4>
                            <p className="text-sm text-muted-foreground">
                              {(provider.credentials as any).fromName} &lt;{(provider.credentials as any).fromEmail}&gt;
                            </p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium">Authentication</h4>
                            <p className="text-sm text-muted-foreground">
                              {(provider.credentials as any).auth?.user}
                            </p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium">Security</h4>
                            <p className="text-sm text-muted-foreground">
                              {(provider.credentials as any).secure ? 'SSL/TLS Enabled' : 'SSL/TLS Disabled'}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between pt-3 border-t">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedProvider(provider);
                            setTestEmail('');
                            setIsTestDialogOpen(true);
                          }}
                          disabled={!provider.active}
                        >
                          <Send className="h-4 w-4 mr-2" /> Test
                        </Button>
                        <div className="space-x-2">
                          <Button variant="outline" size="sm" onClick={() => handleEditSMTP(provider)}>
                            Edit
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteSMTP(provider.id)}
                            disabled={provider.isDefault}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card className="p-8 text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                    <Mail className="h-6 w-6" />
                  </div>
                  <h3 className="mb-2 text-lg font-semibold">No SMTP configurations yet</h3>
                  <p className="mb-6 text-sm text-muted-foreground">
                    Add your first SMTP configuration to start sending emails.
                  </p>
                  <Button onClick={handleCreateSMTP}>
                    <Plus className="mr-2 h-4 w-4" /> Add SMTP
                  </Button>
                </Card>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create SMTP Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add SMTP Configuration</DialogTitle>
            <DialogDescription>
              Configure a new SMTP server for email delivery
            </DialogDescription>
          </DialogHeader>

          <Form {...smtpForm}>
            <form onSubmit={smtpForm.handleSubmit(onCreateSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Configuration Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Primary SMTP"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0 mt-8">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {field.value ? 'Active' : 'Inactive'}
                      </FormLabel>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="host"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SMTP Host</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="smtp.example.com"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="port"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SMTP Port</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="587"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="username"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="password"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="fromEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>From Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="fromName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>From Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Your Business Name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="adminEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Admin Email (Contact Form Recipient)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Email address where contact form submissions will be sent. If not specified, contact forms will use the From Email address.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="secure"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Use SSL/TLS</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="isDefault"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Default SMTP</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="isBackup"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Backup SMTP</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateConfigMutation.isPending}
                >
                  {updateConfigMutation.isPending ? 'Saving...' : 'Save SMTP'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit SMTP Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit SMTP Configuration</DialogTitle>
            <DialogDescription>
              Update your SMTP server settings
            </DialogDescription>
          </DialogHeader>

          <Form {...smtpForm}>
            <form onSubmit={smtpForm.handleSubmit(onEditSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Configuration Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Primary SMTP"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0 mt-8">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel className="font-normal">
                        {field.value ? 'Active' : 'Inactive'}
                      </FormLabel>
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="host"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SMTP Host</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="smtp.example.com"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="port"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SMTP Port</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="587"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="username"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="password"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="fromEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>From Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="fromName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>From Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Your Business Name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="adminEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Admin Email (Contact Form Recipient)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Email address where contact form submissions will be sent. If not specified, contact forms will use the From Email address.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={smtpForm.control}
                  name="secure"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Use SSL/TLS</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="isDefault"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Default SMTP</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={smtpForm.control}
                  name="isBackup"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Backup SMTP</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateConfigMutation.isPending}
                >
                  {updateConfigMutation.isPending ? 'Saving...' : 'Update SMTP'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Test Email Dialog */}
      <AlertDialog open={isTestDialogOpen} onOpenChange={setIsTestDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Send Test Email</AlertDialogTitle>
            <AlertDialogDescription>
              Send a test email to verify your SMTP configuration
              {selectedProvider && (
                <div className="mt-2 text-sm font-medium">
                  Using: {selectedProvider.name} ({(selectedProvider.credentials as any).host})
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div>
            <div className="py-4">
              <Input
                placeholder="Enter email address"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                className="w-full"
              />
            </div>
            <AlertDialogFooter>
              <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
              <Button
                type="button"
                onClick={(e) => onTestEmailSubmit(e, selectedProvider?.id)}
                disabled={!testEmail || sendTestEmailMutation.isPending}>
                {sendTestEmailMutation.isPending ? 'Sending...' : 'Send Test'}
              </Button>
            </AlertDialogFooter>
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
}
