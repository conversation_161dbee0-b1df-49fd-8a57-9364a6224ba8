import { useState, useRef, ChangeEvent, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Upload, Image, X, Loader2, Trash2, FolderOpen, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";

interface ImageSelectorProps {
  initialUrl?: string;
  onImageSelected: (url: string) => void;
  label?: string;
}

interface UploadedImage {
  filename: string;
  url: string;
  size: number;
  uploadedAt: string;
  modifiedAt: string;
}

export default function ImageSelector({ initialUrl, onImageSelected, label = "Image" }: ImageSelectorProps) {
  const [imageUrl, setImageUrl] = useState<string>(initialUrl || "");
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [existingImages, setExistingImages] = useState<UploadedImage[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Update imageUrl when initialUrl prop changes
  useEffect(() => {
    setImageUrl(initialUrl || "");
  }, [initialUrl]);

  // Load existing images when dialog opens
  const loadExistingImages = async () => {
    setIsLoadingImages(true);
    try {
      const response = await fetch('/api/upload/images', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch images');
      }

      const images = await response.json();
      setExistingImages(images);
    } catch (error) {
      toast({
        title: "Failed to load images",
        description: error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive"
      });
    } finally {
      setIsLoadingImages(false);
    }
  };

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await uploadFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      await uploadFile(file);
    }
  };

  const uploadFile = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();
      setImageUrl(data.url);
      onImageSelected(data.url);
      setIsDialogOpen(false);

      toast({
        title: "Image uploaded",
        description: "The image was uploaded successfully"
      });

      // Refresh the existing images list
      loadExistingImages();
    } catch (error) {
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleSelectExistingImage = (url: string) => {
    setImageUrl(url);
    onImageSelected(url);
    setIsDialogOpen(false);
    toast({
      title: "Image selected",
      description: "The image has been selected successfully"
    });
  };

  const handleRemoveImage = () => {
    setImageUrl("");
    onImageSelected("");
  };

  const handleDeleteImage = async (filename: string) => {
    try {
      const response = await fetch(`/api/upload/images/${filename}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to delete image');
      }

      toast({
        title: "Image deleted",
        description: "The image has been deleted successfully"
      });

      // Refresh the existing images list
      loadExistingImages();
    } catch (error) {
      toast({
        title: "Delete failed",
        description: error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive"
      });
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-2">
      <Label>{label}</Label>

      {!imageUrl ? (
        <div className="flex gap-2">
          <div
            className={`flex-1 border-2 border-dashed rounded-md p-6 text-center cursor-pointer transition-colors ${
              isDragging ? "border-primary bg-primary/5" : "border-gray-300 hover:border-primary/50"
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleFileChange}
            />

            <div className="flex flex-col items-center justify-center gap-2">
              <Upload className="h-8 w-8 text-gray-400" />
              <div className="text-sm text-gray-600">
                {isUploading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Uploading...</span>
                  </div>
                ) : (
                  <>
                    <p className="font-medium">Upload new image</p>
                    <p className="text-xs text-gray-500">SVG, PNG, JPG or GIF (max. 5MB)</p>
                  </>
                )}
              </div>
            </div>
          </div>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button 
                variant="outline" 
                className="px-4 py-6 h-auto"
                onClick={loadExistingImages}
              >
                <div className="flex flex-col items-center gap-2">
                  <FolderOpen className="h-6 w-6" />
                  <span className="text-xs">Select Existing</span>
                </div>
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
              <DialogHeader>
                <DialogTitle>Select or Upload Image</DialogTitle>
              </DialogHeader>

              <Tabs defaultValue="existing" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="existing">
                    <FolderOpen className="h-4 w-4 mr-2" />
                    Existing Images
                  </TabsTrigger>
                  <TabsTrigger value="upload">
                    <Plus className="h-4 w-4 mr-2" />
                    Upload New
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="existing" className="mt-4">
                  <div className="max-h-96 overflow-y-auto">
                    {isLoadingImages ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                        <span>Loading images...</span>
                      </div>
                    ) : existingImages.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <Image className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>No images uploaded yet</p>
                        <p className="text-sm">Upload your first image to get started</p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        {existingImages.map((image) => (
                          <div key={image.filename} className="group relative">
                            <div 
                              className="aspect-square rounded-lg overflow-hidden border-2 border-transparent hover:border-primary cursor-pointer transition-all"
                              onClick={() => handleSelectExistingImage(image.url)}
                            >
                              <img
                                src={image.url}
                                alt={image.filename}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                              />
                            </div>
                            <div className="mt-2 space-y-1">
                              <p className="text-xs font-medium truncate" title={image.filename}>
                                {image.filename}
                              </p>
                              <div className="flex justify-between items-center">
                                <Badge variant="secondary" className="text-xs">
                                  {formatFileSize(image.size)}
                                </Badge>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteImage(image.filename);
                                  }}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                              <p className="text-xs text-gray-500">
                                {formatDate(image.uploadedAt)}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="upload" className="mt-4">
                  <div
                    className={`border-2 border-dashed rounded-md p-8 text-center cursor-pointer transition-colors ${
                      isDragging ? "border-primary bg-primary/5" : "border-gray-300 hover:border-primary/50"
                    }`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <div className="flex flex-col items-center justify-center gap-4">
                      <Upload className="h-12 w-12 text-gray-400" />
                      <div className="text-sm text-gray-600">
                        {isUploading ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Uploading...</span>
                          </div>
                        ) : (
                          <>
                            <p className="font-medium text-lg">Click to upload or drag and drop</p>
                            <p className="text-gray-500">SVG, PNG, JPG or GIF (max. 5MB)</p>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </DialogContent>
          </Dialog>
        </div>
      ) : (
        <div className="relative aspect-video w-full max-w-md overflow-hidden rounded-md border">
          <img
            src={imageUrl}
            alt="Selected"
            className="h-full w-full object-cover"
          />
          <div className="absolute top-2 right-2 flex gap-1">
            <Button
              variant="secondary"
              size="icon"
              className="h-7 w-7 rounded-full opacity-90"
              onClick={() => setIsDialogOpen(true)}
            >
              <FolderOpen className="h-4 w-4" />
            </Button>
            <Button
              variant="destructive"
              size="icon"
              className="h-7 w-7 rounded-full opacity-90"
              onClick={handleRemoveImage}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
