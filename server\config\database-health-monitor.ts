import { EventEmitter } from 'events';
import { getConnectionManager } from './connection-manager';
import { securityMonitor, SecurityEventType, SecuritySeverity } from './security-monitoring';

// Health check configuration
interface HealthCheckConfig {
  checkInterval: number;
  timeoutMs: number;
  maxFailures: number;
  alertThresholds: {
    connectionPoolUtilization: number;
    averageQueryTime: number;
    errorRate: number;
    unhealthyConnections: number;
  };
  enableAlerts: boolean;
  enableAutoRecovery: boolean;
}

// Health status
interface HealthStatus {
  isHealthy: boolean;
  lastCheck: Date;
  consecutiveFailures: number;
  uptime: number;
  checks: {
    connectionPool: boolean;
    queryExecution: boolean;
    encryptionVerification: boolean;
    diskSpace: boolean;
    performance: boolean;
  };
  metrics: {
    connectionPoolUtilization: number;
    averageQueryTime: number;
    errorRate: number;
    unhealthyConnections: number;
    totalQueries: number;
    totalErrors: number;
  };
  alerts: HealthAlert[];
}

// Health alert
interface HealthAlert {
  id: string;
  type: 'warning' | 'error' | 'critical';
  message: string;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
}

// Default configuration
const DEFAULT_CONFIG: HealthCheckConfig = {
  checkInterval: parseInt(process.env.DB_HEALTH_CHECK_INTERVAL || '30000'), // 30 seconds
  timeoutMs: parseInt(process.env.DB_HEALTH_CHECK_TIMEOUT || '5000'), // 5 seconds
  maxFailures: parseInt(process.env.DB_MAX_HEALTH_FAILURES || '3'),
  alertThresholds: {
    connectionPoolUtilization: parseFloat(process.env.DB_POOL_UTILIZATION_THRESHOLD || '0.8'), // 80%
    averageQueryTime: parseInt(process.env.DB_QUERY_TIME_THRESHOLD || '1000'), // 1 second
    errorRate: parseFloat(process.env.DB_ERROR_RATE_THRESHOLD || '0.05'), // 5%
    unhealthyConnections: parseInt(process.env.DB_UNHEALTHY_CONNECTIONS_THRESHOLD || '1')
  },
  enableAlerts: process.env.ENABLE_DB_HEALTH_ALERTS !== 'false',
  enableAutoRecovery: process.env.ENABLE_DB_AUTO_RECOVERY !== 'false'
};

export class DatabaseHealthMonitor extends EventEmitter {
  private config: HealthCheckConfig;
  private status: HealthStatus;
  private checkTimer: NodeJS.Timeout | null = null;
  private startTime: Date;
  private alertCounter: number = 0;

  constructor(config: Partial<HealthCheckConfig> = {}) {
    super();
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.startTime = new Date();
    
    this.status = {
      isHealthy: true,
      lastCheck: new Date(),
      consecutiveFailures: 0,
      uptime: 0,
      checks: {
        connectionPool: true,
        queryExecution: true,
        encryptionVerification: true,
        diskSpace: true,
        performance: true
      },
      metrics: {
        connectionPoolUtilization: 0,
        averageQueryTime: 0,
        errorRate: 0,
        unhealthyConnections: 0,
        totalQueries: 0,
        totalErrors: 0
      },
      alerts: []
    };

    this.startMonitoring();
  }

  // Start health monitoring
  private startMonitoring(): void {
    console.log('💓 Starting database health monitoring...');
    
    this.checkTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.config.checkInterval);

    // Perform initial health check
    this.performHealthCheck();
  }

  // Perform comprehensive health check
  private async performHealthCheck(): Promise<void> {
    const checkStartTime = Date.now();
    
    try {
      console.log('🔍 Performing database health check...');
      
      // Update uptime
      this.status.uptime = Date.now() - this.startTime.getTime();
      this.status.lastCheck = new Date();

      // Perform individual checks
      const checks = await Promise.allSettled([
        this.checkConnectionPool(),
        this.checkQueryExecution(),
        this.checkEncryptionVerification(),
        this.checkDiskSpace(),
        this.checkPerformance()
      ]);

      // Update check results
      this.status.checks.connectionPool = checks[0].status === 'fulfilled' && checks[0].value;
      this.status.checks.queryExecution = checks[1].status === 'fulfilled' && checks[1].value;
      this.status.checks.encryptionVerification = checks[2].status === 'fulfilled' && checks[2].value;
      this.status.checks.diskSpace = checks[3].status === 'fulfilled' && checks[3].value;
      this.status.checks.performance = checks[4].status === 'fulfilled' && checks[4].value;

      // Determine overall health
      const allChecksHealthy = Object.values(this.status.checks).every(check => check);
      
      if (allChecksHealthy) {
        if (!this.status.isHealthy) {
          console.log('✅ Database health restored');
          this.createAlert('info', 'Database health restored', 'warning');
        }
        
        this.status.isHealthy = true;
        this.status.consecutiveFailures = 0;
      } else {
        this.status.isHealthy = false;
        this.status.consecutiveFailures++;
        
        console.error(`❌ Database health check failed (${this.status.consecutiveFailures}/${this.config.maxFailures})`);
        
        // Create alert for health check failure
        this.createAlert('error', `Database health check failed: ${this.getFailedChecks().join(', ')}`, 'error');

        // Trigger auto-recovery if enabled and max failures reached
        if (this.config.enableAutoRecovery && this.status.consecutiveFailures >= this.config.maxFailures) {
          await this.attemptAutoRecovery();
        }
      }

      // Check alert thresholds
      this.checkAlertThresholds();

      // Emit health status event
      this.emit('healthCheck', this.status);

      const checkDuration = Date.now() - checkStartTime;
      console.log(`🔍 Health check completed in ${checkDuration}ms - Status: ${this.status.isHealthy ? 'Healthy' : 'Unhealthy'}`);

    } catch (error) {
      console.error('❌ Health check error:', error);
      this.status.isHealthy = false;
      this.status.consecutiveFailures++;
      
      this.createAlert('critical', `Health check error: ${error.message}`, 'critical');
    }
  }

  // Check connection pool health
  private async checkConnectionPool(): Promise<boolean> {
    try {
      const connectionManager = getConnectionManager();
      const poolStatus = connectionManager.getStatus();
      const metrics = connectionManager.getMetrics();

      // Update metrics
      this.status.metrics.connectionPoolUtilization = 
        poolStatus.totalConnections > 0 ? poolStatus.activeConnections / poolStatus.totalConnections : 0;
      this.status.metrics.unhealthyConnections = poolStatus.totalConnections - poolStatus.healthyConnections;
      this.status.metrics.totalQueries = metrics.totalQueries;
      this.status.metrics.totalErrors = metrics.totalErrors;
      this.status.metrics.averageQueryTime = metrics.averageQueryTime;
      this.status.metrics.errorRate = metrics.totalQueries > 0 ? metrics.totalErrors / metrics.totalQueries : 0;

      // Check if pool is healthy
      const isHealthy = poolStatus.healthyConnections > 0 && 
                       poolStatus.waitingRequests < poolStatus.totalConnections;

      if (!isHealthy) {
        console.warn(`⚠️ Connection pool unhealthy: ${poolStatus.healthyConnections}/${poolStatus.totalConnections} healthy connections`);
      }

      return isHealthy;
    } catch (error) {
      console.error('❌ Connection pool check failed:', error);
      return false;
    }
  }

  // Check query execution
  private async checkQueryExecution(): Promise<boolean> {
    try {
      const connectionManager = getConnectionManager();
      
      // Execute a simple test query
      const result = await connectionManager.executeQuery((db) => {
        return db.prepare('SELECT 1 as test').get();
      });

      return result && (result as any).test === 1;
    } catch (error) {
      console.error('❌ Query execution check failed:', error);
      return false;
    }
  }

  // Check encryption verification
  private async checkEncryptionVerification(): Promise<boolean> {
    try {
      const connectionManager = getConnectionManager();
      
      // Check if SQLCipher is working (if enabled)
      const result = await connectionManager.executeQuery((db) => {
        // Try to get cipher version (SQLCipher specific)
        try {
          const cipherVersion = (db as any).pragma('cipher_version');
          return { usingSQLCipher: true, version: cipherVersion };
        } catch {
          // Not using SQLCipher, which is also valid
          return { usingSQLCipher: false };
        }
      });

      return true; // Both SQLCipher and regular SQLite are valid
    } catch (error) {
      console.error('❌ Encryption verification check failed:', error);
      return false;
    }
  }

  // Check disk space
  private async checkDiskSpace(): Promise<boolean> {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const dbPath = process.env.DATABASE_PATH || 'data.db';
      const dbDir = path.dirname(dbPath);
      
      const stats = fs.statSync(dbDir);
      // This is a basic check - in production, you'd want to check actual disk space
      return fs.existsSync(dbPath);
    } catch (error) {
      console.error('❌ Disk space check failed:', error);
      return false;
    }
  }

  // Check performance metrics
  private async checkPerformance(): Promise<boolean> {
    try {
      const connectionManager = getConnectionManager();
      const metrics = connectionManager.getMetrics();

      // Check if performance is within acceptable limits
      const performanceOk = 
        metrics.averageQueryTime < this.config.alertThresholds.averageQueryTime &&
        (metrics.totalQueries === 0 || metrics.totalErrors / metrics.totalQueries < this.config.alertThresholds.errorRate);

      if (!performanceOk) {
        console.warn(`⚠️ Performance degraded: avg query time ${metrics.averageQueryTime}ms, error rate ${(metrics.totalErrors / metrics.totalQueries * 100).toFixed(2)}%`);
      }

      return performanceOk;
    } catch (error) {
      console.error('❌ Performance check failed:', error);
      return false;
    }
  }

  // Check alert thresholds
  private checkAlertThresholds(): void {
    if (!this.config.enableAlerts) {
      return;
    }

    const { metrics } = this.status;
    const { alertThresholds } = this.config;

    // Connection pool utilization
    if (metrics.connectionPoolUtilization > alertThresholds.connectionPoolUtilization) {
      this.createAlert('warning', 
        `High connection pool utilization: ${(metrics.connectionPoolUtilization * 100).toFixed(1)}%`, 
        'warning'
      );
    }

    // Average query time
    if (metrics.averageQueryTime > alertThresholds.averageQueryTime) {
      this.createAlert('warning', 
        `High average query time: ${metrics.averageQueryTime}ms`, 
        'warning'
      );
    }

    // Error rate
    if (metrics.errorRate > alertThresholds.errorRate) {
      this.createAlert('error', 
        `High error rate: ${(metrics.errorRate * 100).toFixed(2)}%`, 
        'error'
      );
    }

    // Unhealthy connections
    if (metrics.unhealthyConnections > alertThresholds.unhealthyConnections) {
      this.createAlert('error', 
        `Unhealthy connections detected: ${metrics.unhealthyConnections}`, 
        'error'
      );
    }
  }

  // Create health alert
  private createAlert(type: 'warning' | 'error' | 'critical' | 'info', message: string, severity: 'warning' | 'error' | 'critical'): void {
    const alertId = `alert_${Date.now()}_${++this.alertCounter}`;
    
    const alert: HealthAlert = {
      id: alertId,
      type: type === 'info' ? 'warning' : type,
      message,
      timestamp: new Date(),
      resolved: type === 'info'
    };

    if (type === 'info') {
      // Resolve previous alerts of the same type
      this.status.alerts
        .filter(a => !a.resolved && a.message.includes(message.split(':')[0]))
        .forEach(a => {
          a.resolved = true;
          a.resolvedAt = new Date();
        });
    } else {
      this.status.alerts.push(alert);
    }

    // Log to security monitor
    const severityMap = {
      'warning': SecuritySeverity.MEDIUM,
      'error': SecuritySeverity.HIGH,
      'critical': SecuritySeverity.CRITICAL
    };

    securityMonitor.logSecurityEvent(
      SecurityEventType.DATABASE_ERROR,
      {
        userId: undefined,
        username: 'system',
        role: 'system' as any,
        permissions: [],
        ipAddress: 'localhost'
      },
      {
        action: 'DATABASE_HEALTH_ALERT',
        alertType: type,
        alertId,
        message
      },
      severityMap[severity]
    );

    // Emit alert event
    this.emit('alert', alert);

    console.log(`🚨 Health Alert [${type.toUpperCase()}]: ${message}`);
  }

  // Get failed checks
  private getFailedChecks(): string[] {
    return Object.entries(this.status.checks)
      .filter(([_, healthy]) => !healthy)
      .map(([check, _]) => check);
  }

  // Attempt auto-recovery
  private async attemptAutoRecovery(): Promise<void> {
    console.log('🔄 Attempting database auto-recovery...');
    
    try {
      const connectionManager = getConnectionManager();
      
      // Force health check on all connections
      // This will mark unhealthy connections and potentially recreate them
      console.log('🔄 Forcing connection pool health check...');
      
      // The connection manager's health check will handle recovery
      this.createAlert('info', 'Auto-recovery attempted', 'warning');
      
    } catch (error) {
      console.error('❌ Auto-recovery failed:', error);
      this.createAlert('critical', `Auto-recovery failed: ${error.message}`, 'critical');
    }
  }

  // Get current health status
  getHealthStatus(): HealthStatus {
    return { ...this.status };
  }

  // Get health summary
  getHealthSummary(): {
    isHealthy: boolean;
    uptime: string;
    lastCheck: string;
    activeAlerts: number;
    connectionPoolUtilization: string;
    averageQueryTime: string;
    errorRate: string;
  } {
    const uptimeHours = Math.floor(this.status.uptime / (1000 * 60 * 60));
    const uptimeMinutes = Math.floor((this.status.uptime % (1000 * 60 * 60)) / (1000 * 60));
    
    return {
      isHealthy: this.status.isHealthy,
      uptime: `${uptimeHours}h ${uptimeMinutes}m`,
      lastCheck: this.status.lastCheck.toISOString(),
      activeAlerts: this.status.alerts.filter(a => !a.resolved).length,
      connectionPoolUtilization: `${(this.status.metrics.connectionPoolUtilization * 100).toFixed(1)}%`,
      averageQueryTime: `${this.status.metrics.averageQueryTime.toFixed(1)}ms`,
      errorRate: `${(this.status.metrics.errorRate * 100).toFixed(2)}%`
    };
  }

  // Stop monitoring
  stop(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
    console.log('💓 Database health monitoring stopped');
  }
}

// Global health monitor instance
let healthMonitor: DatabaseHealthMonitor | null = null;

// Initialize health monitor
export function initializeHealthMonitor(config?: Partial<HealthCheckConfig>): DatabaseHealthMonitor {
  if (healthMonitor) {
    return healthMonitor;
  }

  healthMonitor = new DatabaseHealthMonitor(config);
  return healthMonitor;
}

// Get health monitor instance
export function getHealthMonitor(): DatabaseHealthMonitor | null {
  return healthMonitor;
}
