import express, { Request, Response } from 'express';
import { isAdmin } from '../middleware/auth';
import { storage } from '../storage-factory';
import { clearUrlConfigCache, detectPortFromDatabase } from '../utils/url-utils';
import {
  migrateAllUrls,
  convertToRelativeUrls,
  updateUrlsToDomain,
  detectPortsInDatabase,
  smartPortMigration,
  migrateWithPort
} from '../utils/url-migration';

const urlSettingsRouter = express.Router();

// Get URL settings
urlSettingsRouter.get('/url-settings', isAdmin, async (req: Request, res: Response) => {
  try {
    const settings = await storage.getGeneralSettings();
    const urlSettings = settings?.urlSettings ? JSON.parse(settings.urlSettings) : {
      autoDetect: true,
      forceHttps: false
    };
    
    res.json({
      success: true,
      urlSettings
    });
  } catch (error) {
    console.error('Error fetching URL settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch URL settings'
    });
  }
});

// Update URL settings
urlSettingsRouter.post('/url-settings', isAdmin, async (req: Request, res: Response) => {
  try {
    const { baseUrl, autoDetect, forceHttps } = req.body;
    
    // Validate settings
    if (baseUrl && typeof baseUrl !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Base URL must be a string'
      });
    }
    
    if (typeof autoDetect !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'Auto detect must be a boolean'
      });
    }
    
    if (typeof forceHttps !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'Force HTTPS must be a boolean'
      });
    }
    
    // Validate base URL format if provided
    if (baseUrl) {
      try {
        new URL(baseUrl);
      } catch {
        return res.status(400).json({
          success: false,
          message: 'Invalid base URL format'
        });
      }
    }
    
    const urlSettings = {
      baseUrl: baseUrl || undefined,
      autoDetect,
      forceHttps
    };
    
    // Get current settings or create new ones
    const currentSettings = await storage.getGeneralSettings();
    const updates = {
      urlSettings: JSON.stringify(urlSettings),
      updatedAt: new Date().toISOString()
    };
    
    if (currentSettings) {
      await storage.updateGeneralSettings(updates);
    } else {
      await storage.createGeneralSettings({
        ...updates,
        createdAt: new Date().toISOString()
      });
    }
    
    // Clear URL config cache to force reload
    clearUrlConfigCache();
    
    res.json({
      success: true,
      message: 'URL settings updated successfully',
      urlSettings
    });
  } catch (error) {
    console.error('Error updating URL settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update URL settings'
    });
  }
});

// Get migration status (check for localhost URLs)
urlSettingsRouter.get('/url-migration/status', isAdmin, async (req: Request, res: Response) => {
  try {
    let localhostUrlsFound = 0;
    const details: Record<string, number> = {};
    
    // Check custom checkout pages
    const pages = await storage.getCustomCheckoutPages();
    let pageCount = 0;
    pages.forEach(page => {
      if (page.imageUrl?.includes('localhost')) pageCount++;
      if (page.headerLogo?.includes('localhost')) pageCount++;
      if (page.footerLogo?.includes('localhost')) pageCount++;
    });
    details.customCheckoutPages = pageCount;
    localhostUrlsFound += pageCount;
    
    // Check general settings
    const settings = await storage.getGeneralSettings();
    let settingsCount = 0;
    if (settings?.logoUrl?.includes('localhost')) settingsCount++;
    if (settings?.faviconUrl?.includes('localhost')) settingsCount++;
    details.generalSettings = settingsCount;
    localhostUrlsFound += settingsCount;
    
    // Check products
    const products = await storage.getProducts();
    let productCount = 0;
    products.forEach(product => {
      if (product.imageUrl?.includes('localhost')) productCount++;
    });
    details.products = productCount;
    localhostUrlsFound += productCount;
    
    // Check homepage
    const homepage = await storage.getHomepageConfig();
    let homepageCount = 0;
    if (homepage) {
      const sectionsData = JSON.parse(homepage.sectionsData || '[]');
      const seoSettings = JSON.parse(homepage.seoSettings || '{}');
      
      sectionsData.forEach((section: any) => {
        if (section.content) {
          ['backgroundImage', 'image', 'logoUrl', 'iconUrl'].forEach(field => {
            if (section.content[field]?.includes('localhost')) homepageCount++;
          });
        }
      });
      
      ['ogImage', 'twitterImage'].forEach(field => {
        if (seoSettings[field]?.includes('localhost')) homepageCount++;
      });
    }
    details.homepage = homepageCount;
    localhostUrlsFound += homepageCount;
    
    res.json({
      success: true,
      localhostUrlsFound,
      needsMigration: localhostUrlsFound > 0,
      details
    });
  } catch (error) {
    console.error('Error checking migration status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check migration status'
    });
  }
});

// Run URL migration to convert localhost URLs to relative URLs
urlSettingsRouter.post('/url-migration/to-relative', isAdmin, async (req: Request, res: Response) => {
  try {
    const result = await convertToRelativeUrls();
    
    res.json({
      success: true,
      message: `Migration completed: ${result.totalUpdated} records updated`,
      ...result
    });
  } catch (error) {
    console.error('Error running URL migration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to run URL migration'
    });
  }
});

// Run URL migration to update URLs to a specific domain
urlSettingsRouter.post('/url-migration/to-domain', isAdmin, async (req: Request, res: Response) => {
  try {
    const { domain } = req.body;
    
    if (!domain || typeof domain !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Domain is required and must be a string'
      });
    }
    
    // Validate domain format
    try {
      const testUrl = domain.startsWith('http') ? domain : `https://${domain}`;
      new URL(testUrl);
    } catch {
      return res.status(400).json({
        success: false,
        message: 'Invalid domain format'
      });
    }
    
    const result = await updateUrlsToDomain(domain);
    
    res.json({
      success: true,
      message: `Migration completed: ${result.totalUpdated} records updated to use domain ${domain}`,
      ...result
    });
  } catch (error) {
    console.error('Error running URL migration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to run URL migration'
    });
  }
});

// Run complete URL migration (auto-detect current domain)
urlSettingsRouter.post('/url-migration/auto', isAdmin, async (req: Request, res: Response) => {
  try {
    // Get current domain from request
    const protocol = req.get('x-forwarded-proto') || req.protocol;
    const host = req.get('x-forwarded-host') || req.get('host');
    const currentDomain = `${protocol}://${host}`;

    const result = await updateUrlsToDomain(currentDomain);

    res.json({
      success: true,
      message: `Migration completed: ${result.totalUpdated} records updated to use current domain ${currentDomain}`,
      currentDomain,
      ...result
    });
  } catch (error) {
    console.error('Error running auto URL migration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to run auto URL migration'
    });
  }
});

// Get port detection information
urlSettingsRouter.get('/url-migration/port-detection', isAdmin, async (req: Request, res: Response) => {
  try {
    const portDetection = await detectPortsInDatabase();
    const currentPort = process.env.PORT || '3001';

    res.json({
      success: true,
      portDetection,
      currentPort,
      recommendations: {
        useDetectedPort: portDetection.mostCommonPort,
        useCurrentPort: currentPort,
        hasConflict: portDetection.mostCommonPort && portDetection.mostCommonPort !== currentPort
      }
    });
  } catch (error) {
    console.error('Error getting port detection info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get port detection information'
    });
  }
});

// Run smart port migration
urlSettingsRouter.post('/url-migration/smart-port', isAdmin, async (req: Request, res: Response) => {
  try {
    const { targetDomain } = req.body;

    if (!targetDomain || typeof targetDomain !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Target domain is required and must be a string'
      });
    }

    // Validate domain format
    try {
      const testUrl = targetDomain.startsWith('http') ? targetDomain : `https://${targetDomain}`;
      new URL(testUrl);
    } catch {
      return res.status(400).json({
        success: false,
        message: 'Invalid target domain format'
      });
    }

    const finalDomain = targetDomain.startsWith('http') ? targetDomain : `https://${targetDomain}`;
    const result = await smartPortMigration(finalDomain);

    res.json({
      success: true,
      message: `Smart migration completed: ${result.totalUpdated} records updated`,
      targetDomain: finalDomain,
      ...result
    });
  } catch (error) {
    console.error('Error running smart port migration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to run smart port migration'
    });
  }
});

// Run migration with manual port
urlSettingsRouter.post('/url-migration/with-port', isAdmin, async (req: Request, res: Response) => {
  try {
    const { targetDomain, port } = req.body;

    if (!targetDomain || typeof targetDomain !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Target domain is required and must be a string'
      });
    }

    if (!port || typeof port !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Port is required and must be a string'
      });
    }

    // Validate port number
    const portNum = parseInt(port, 10);
    if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
      return res.status(400).json({
        success: false,
        message: 'Port must be a valid number between 1 and 65535'
      });
    }

    // Validate domain format
    try {
      const testUrl = targetDomain.startsWith('http') ? targetDomain : `https://${targetDomain}`;
      new URL(testUrl);
    } catch {
      return res.status(400).json({
        success: false,
        message: 'Invalid target domain format'
      });
    }

    const finalDomain = targetDomain.startsWith('http') ? targetDomain : `https://${targetDomain}`;
    const result = await migrateWithPort(finalDomain, port);

    res.json({
      success: true,
      message: `Migration with port ${port} completed: ${result.totalUpdated} records updated`,
      targetDomain: finalDomain,
      port,
      ...result
    });
  } catch (error) {
    console.error('Error running migration with port:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to run migration with port'
    });
  }
});

export { urlSettingsRouter };
