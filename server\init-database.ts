import { db } from './db';
import {
  products,
  allowedEmails,
  paypalButtons,
  emailTemplates,
  customCheckoutPages,
  generalSettings,
  homepageConfig,
  systemMessages
} from '@shared/schema';
import { nanoid } from 'nanoid';
import { sql } from 'drizzle-orm';

/**
 * Initialize the database with sample data
 */
export async function initializeDatabase() {
  try {
    console.log('🗄️ Initializing database with sample data...');

    // Run URL settings migration first
    try {
      await db.execute(sql`ALTER TABLE general_settings ADD COLUMN url_settings TEXT`);
      console.log('✅ Added url_settings column to general_settings table');
    } catch (error: any) {
      if (error.message.includes('duplicate column name') || error.message.includes('already exists')) {
        console.log('⚠️ url_settings column already exists in general_settings table');
      } else {
        console.warn('⚠️ Could not add url_settings column:', error.message);
      }
    }

    // Run missing invoice fields migration
    try {
      await db.execute(sql`ALTER TABLE invoices ADD COLUMN notes TEXT`);
      console.log('✅ Added notes column to invoices table');
    } catch (error: any) {
      if (error.message.includes('duplicate column name') || error.message.includes('already exists')) {
        console.log('⚠️ notes column already exists in invoices table');
      } else {
        console.warn('⚠️ Could not add notes column:', error.message);
      }
    }

    try {
      await db.execute(sql`ALTER TABLE invoices ADD COLUMN app_type TEXT`);
      console.log('✅ Added app_type column to invoices table');
    } catch (error: any) {
      if (error.message.includes('duplicate column name') || error.message.includes('already exists')) {
        console.log('⚠️ app_type column already exists in invoices table');
      } else {
        console.warn('⚠️ Could not add app_type column:', error.message);
      }
    }

    try {
      await db.execute(sql`ALTER TABLE invoices ADD COLUMN mac_address TEXT`);
      console.log('✅ Added mac_address column to invoices table');
    } catch (error: any) {
      if (error.message.includes('duplicate column name') || error.message.includes('already exists')) {
        console.log('⚠️ mac_address column already exists in invoices table');
      } else {
        console.warn('⚠️ Could not add mac_address column:', error.message);
      }
    }

    // Check if products already exist
    const existingProducts = await db.select().from(products).limit(1);
    if (existingProducts.length > 0) {
      console.log('📦 Database already has data, skipping initialization');
      return;
    }

    // Insert sample products
    console.log('📦 Creating sample products...');
    const sampleProducts = [
      {
        name: "Dashboard Pro Template",
        description: "Modern admin dashboard template with 50+ components, dark/light mode, and responsive design. Perfect for productivity apps and SaaS platforms.",
        price: "89.99",
        imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "manual-payment"
      },
      {
        name: "Task Management UI Kit",
        description: "Complete UI kit for task management applications with 100+ screens, components, and interactive prototypes for Figma.",
        price: "59.99",
        imageUrl: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "manual-payment"
      },
      {
        name: "Mobile Productivity App Template",
        description: "React Native template for productivity apps with calendar, notes, tasks, and team collaboration features. iOS & Android ready.",
        price: "129.99",
        imageUrl: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "manual-payment"
      },
      {
        name: "Design System Starter Kit",
        description: "Complete design system with 200+ components, design tokens, documentation, and code examples for React and Vue.js.",
        price: "149.99",
        imageUrl: "https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "manual-payment"
      },
      {
        name: "Calendar & Scheduling Template",
        description: "Advanced calendar and scheduling template with booking system, time zones, recurring events, and team management features.",
        price: "79.99",
        imageUrl: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "manual-payment"
      },
      {
        name: "Note-Taking App UI Kit",
        description: "Beautiful note-taking app interface with rich text editor, markdown support, tags, and collaborative features. Includes Figma files.",
        price: "49.99",
        imageUrl: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=60",
        active: 1,
        paymentMethod: "manual-payment"
      }
    ];

    await db.insert(products).values(sampleProducts);
    console.log(`✅ Created ${sampleProducts.length} sample products`);

    // Insert sample allowed emails
    console.log('📧 Creating sample allowed emails...');
    const sampleEmails = [
      "<EMAIL>",
      "<EMAIL>", 
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ];

    const emailData = sampleEmails.map(email => ({
      email,
      notes: "Test email",
      lastSubject: "Welcome to our service",
      smtpProvider: "smtp-1",
      lastUpdated: new Date().toISOString(),
      createdAt: new Date().toISOString()
    }));

    await db.insert(allowedEmails).values(emailData);
    console.log(`✅ Created ${sampleEmails.length} sample allowed emails`);

    // Insert sample PayPal buttons
    console.log('💳 Creating sample PayPal buttons...');
    const sampleButtons = [
      {
        name: "Basic PayPal Button",
        buttonCode: `<form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_top">
<input type="hidden" name="cmd" value="_s-xclick">
<input type="hidden" name="hosted_button_id" value="SAMPLE123456">
<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_buynowCC_LG.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">
</form>`,
        description: "Standard PayPal Buy Now button",
        active: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        name: "Premium PayPal Button",
        buttonCode: `<form action="https://www.paypal.com/cgi-bin/webscr" method="post" target="_top">
<input type="hidden" name="cmd" value="_s-xclick">
<input type="hidden" name="hosted_button_id" value="PREMIUM789012">
<input type="image" src="https://www.paypalobjects.com/en_US/i/btn/btn_paynowCC_LG.gif" border="0" name="submit" alt="PayPal - The safer, easier way to pay online!">
<img alt="" border="0" src="https://www.paypalobjects.com/en_US/i/scr/pixel.gif" width="1" height="1">
</form>`,
        description: "Premium PayPal Pay Now button",
        active: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    await db.insert(paypalButtons).values(sampleButtons);
    console.log(`✅ Created ${sampleButtons.length} sample PayPal buttons`);

    // Check if general settings already exist
    const existingGeneralSettings = await db.select().from(generalSettings).limit(1);
    if (existingGeneralSettings.length === 0) {
      console.log('⚙️ Creating default general settings...');
      const defaultGeneralSettings = {
        siteName: "🚀 TESTING - TemplateHub Pro",
        siteDescription: "🎯 TESTING - Premium productivity app templates and UI/UX design systems",
        logoUrl: "",
        faviconUrl: "",
        primaryColor: "#ff6b6b",
        secondaryColor: "#4ecdc4",
        footerText: "© 2024 🚀 TESTING - TemplateHub Pro",
        enableCheckout: 1,
        enableCustomCheckout: 1,
        enableTestMode: 1,
        defaultTestCustomerEnabled: 1,
        defaultTestCustomerName: "Test Designer",
        defaultTestCustomerEmail: "<EMAIL>",
        emailDomainRestrictionEnabled: 0,
        emailDomainRestrictionDomains: "gmail.com, hotmail.com, yahoo.com",
        seoPrivacySettings: JSON.stringify({
          globalNoIndex: true,
          hideFromSearchEngines: true,
          disableSitemaps: true,
          hideFramework: true,
          customRobotsTxt: "User-agent: *\nDisallow: /",
          pageIndexingRules: {
            homepage: false,
            checkoutPages: false,
            adminPages: false,
            customPages: false
          },
          privacyHeaders: {
            hideServerInfo: true,
            preventFraming: true,
            disableReferrer: true,
            hideGenerator: true
          }
        }),
        telegramBotSettings: JSON.stringify({
          enabled: false,
          botToken: '',
          adminChatId: '',
          webhookUrl: '',
          notifications: {
            newOrders: true,
            paymentConfirmations: true,
            trialUpgrades: true,
            orderStatusChanges: true
          },
          emailIntegration: {
            enabled: false,
            allowQuickSend: false,
            defaultTemplateId: ''
          },
          m3uManagement: {
            enabled: false,
            autoExtractCredentials: false,
            credentialFormat: 'Username: {username}\nPassword: {password}\nM3U URL: {m3u_url}',
            defaultM3uLinks: []
          },
          security: {
            verifyAdminOnly: true,
            rateLimitEnabled: true,
            auditLogging: true
          }
        }),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await db.insert(generalSettings).values(defaultGeneralSettings);
      console.log('✅ Created default general settings');
    }

    // Check if homepage config already exists
    const existingHomepageConfig = await db.select().from(homepageConfig).limit(1);
    if (existingHomepageConfig.length === 0) {
      console.log('🏠 Creating default homepage configuration...');
      const defaultHomepageConfig = {
        sectionsData: JSON.stringify([
          {
            id: 'hero-1',
            type: 'hero',
            title: 'Hero Section',
            enabled: true,
            order: 1,
            content: {
              title: 'Premium Productivity App Templates',
              subtitle: 'Design Systems & UI Kits',
              description: 'Build stunning productivity apps with our comprehensive collection of templates, components, and design systems. Professional, modern, and ready to use.',
              ctaText: 'Explore Templates',
              ctaLink: '#products',
              backgroundImage: '',
              backgroundType: 'gradient',
              backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              textColor: '#ffffff',
              showVideo: false,
              videoUrl: ''
            }
          }
        ]),
        seoSettings: JSON.stringify({
          title: 'Productivity App Templates & UI/UX Design Systems',
          description: 'Premium collection of productivity app templates, UI components, and design systems. Build stunning apps with our modern, responsive templates.',
          keywords: 'productivity app templates, ui design system, app ui kit, dashboard templates, react components, design system, ui components',
          ogTitle: 'Productivity App Templates & UI/UX Design Systems',
          ogDescription: 'Premium collection of productivity app templates, UI components, and design systems. Build stunning apps with our modern, responsive templates.',
          ogImage: '',
          twitterTitle: 'Productivity App Templates & Design Systems',
          twitterDescription: 'Premium collection of productivity app templates and UI components for modern applications.',
          twitterImage: ''
        }),
        themeSettings: JSON.stringify({
          primaryColor: '#6366f1',
          secondaryColor: '#4f46e5',
          accentColor: '#8b5cf6',
          backgroundColor: '#ffffff',
          textColor: '#1e293b',
          fontFamily: 'Inter, system-ui, sans-serif',
          borderRadius: '8px',
          spacing: '1rem'
        }),
        version: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await db.insert(homepageConfig).values(defaultHomepageConfig);
      console.log('✅ Created default homepage configuration');
    }

    // Check if system messages already exist
    const existingSystemMessages = await db.select().from(systemMessages).limit(1);
    if (existingSystemMessages.length === 0) {
      console.log('💬 Creating default system messages...');
      const defaultSystemMessages = [
        {
          messageId: "checkout_subscriber_only",
          category: "checkout",
          name: "Subscriber Only Message",
          description: "Message shown when checkout is restricted to subscribers only",
          content: "This checkout is currently available to subscribers only. Please contact support for access.",
          isHtml: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          messageId: "checkout_maintenance",
          category: "checkout",
          name: "Maintenance Mode Message",
          description: "Message shown when checkout is in maintenance mode",
          content: "Checkout is temporarily unavailable due to maintenance. Please try again later.",
          isHtml: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          messageId: "payment_processing",
          category: "payment",
          name: "Payment Processing Message",
          description: "Message shown during payment processing",
          content: "Your payment is being processed. Please do not close this window.",
          isHtml: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          messageId: "order_confirmation",
          category: "order",
          name: "Order Confirmation Message",
          description: "Message shown after successful order completion",
          content: "Thank you for your order! You will receive a confirmation email shortly.",
          isHtml: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      await db.insert(systemMessages).values(defaultSystemMessages);
      console.log(`✅ Created ${defaultSystemMessages.length} default system messages`);
    }

    console.log('🎉 Database initialization completed successfully!');

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    throw error;
  }
}
