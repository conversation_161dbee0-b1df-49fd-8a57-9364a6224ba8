#!/usr/bin/env node

/**
 * Key Rotation System Test Script
 * Tests automated key rotation, data re-encryption, and rollback functionality
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

console.log('🔑 Starting key rotation system tests...\n');

// Test 1: Key Rotation Configuration Files
async function testKeyRotationFiles() {
  console.log('📋 Test 1: Key Rotation Configuration Files');
  
  try {
    const requiredFiles = [
      'server/config/key-rotation.ts',
      'server/config/data-reencryption.ts',
      'server/scripts/key-rotation-cli.ts'
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        console.log(`✅ File exists: ${file}`);
        passed++;
      } else {
        console.log(`❌ File missing: ${file}`);
        failed++;
      }
    }
    
    console.log(`📊 File Check Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ File check failed: ${error.message}\n`);
    return false;
  }
}

// Test 2: Environment Configuration
async function testEnvironmentConfig() {
  console.log('📋 Test 2: Environment Configuration');
  
  try {
    const envContent = fs.readFileSync('./.env', 'utf8');
    
    const requiredSettings = [
      'KEY_ROTATION_INTERVAL_DAYS',
      'MAX_KEY_VERSIONS',
      'ENABLE_AUTO_KEY_ROTATION',
      'KEY_DERIVATION_ITERATIONS',
      'MASTER_KEY_PATH',
      'KEY_STORE_PATH',
      'REENCRYPTION_BATCH_SIZE'
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const setting of requiredSettings) {
      if (envContent.includes(setting)) {
        console.log(`✅ Environment setting found: ${setting}`);
        passed++;
      } else {
        console.log(`❌ Environment setting missing: ${setting}`);
        failed++;
      }
    }
    
    console.log(`📊 Environment Config Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ Environment config test failed: ${error.message}\n`);
    return false;
  }
}

// Test 3: Master Key Generation
async function testMasterKeyGeneration() {
  console.log('📋 Test 3: Master Key Generation');
  
  try {
    const masterKeyPath = '.master-key';
    
    // Remove existing master key for test
    if (fs.existsSync(masterKeyPath)) {
      fs.unlinkSync(masterKeyPath);
    }
    
    // Generate test master key
    const masterKey = crypto.randomBytes(64).toString('hex');
    fs.writeFileSync(masterKeyPath, masterKey, { mode: 0o600 });
    
    if (fs.existsSync(masterKeyPath)) {
      const storedKey = fs.readFileSync(masterKeyPath, 'utf8').trim();
      
      if (storedKey === masterKey && storedKey.length === 128) {
        console.log('✅ Master key generation and storage working');
        
        // Test key permissions (on Unix-like systems)
        if (process.platform !== 'win32') {
          const stats = fs.statSync(masterKeyPath);
          const permissions = (stats.mode & parseInt('777', 8)).toString(8);
          
          if (permissions === '600') {
            console.log('✅ Master key has secure permissions (600)');
          } else {
            console.log(`⚠️ Master key permissions: ${permissions} (should be 600)`);
          }
        } else {
          console.log('⚠️ Permission check skipped on Windows');
        }
        
        console.log('📊 Master Key Test Results: 2 passed, 0 failed\n');
        return true;
      } else {
        console.log('❌ Master key storage verification failed');
        console.log('📊 Master Key Test Results: 0 passed, 1 failed\n');
        return false;
      }
    } else {
      console.log('❌ Master key file not created');
      console.log('📊 Master Key Test Results: 0 passed, 1 failed\n');
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Master key test failed: ${error.message}\n`);
    return false;
  }
}

// Test 4: Key Derivation Logic
async function testKeyDerivation() {
  console.log('📋 Test 4: Key Derivation Logic');
  
  try {
    const masterKey = 'test-master-key-for-derivation-testing-12345678901234567890123456789012';
    const salt = crypto.randomBytes(32);
    const iterations = 100000;
    
    // Test key derivation
    const derivedKey1 = crypto.pbkdf2Sync(masterKey, salt, iterations, 32, 'sha256');
    const derivedKey2 = crypto.pbkdf2Sync(masterKey, salt, iterations, 32, 'sha256');
    
    let passed = 0;
    let failed = 0;
    
    // Same inputs should produce same output
    if (derivedKey1.equals(derivedKey2)) {
      console.log('✅ Key derivation is deterministic');
      passed++;
    } else {
      console.log('❌ Key derivation is not deterministic');
      failed++;
    }
    
    // Different salts should produce different keys
    const differentSalt = crypto.randomBytes(32);
    const derivedKey3 = crypto.pbkdf2Sync(masterKey, differentSalt, iterations, 32, 'sha256');
    
    if (!derivedKey1.equals(derivedKey3)) {
      console.log('✅ Different salts produce different keys');
      passed++;
    } else {
      console.log('❌ Different salts produce same keys');
      failed++;
    }
    
    // Key should be correct length
    if (derivedKey1.length === 32) {
      console.log('✅ Derived key has correct length (32 bytes)');
      passed++;
    } else {
      console.log(`❌ Derived key has incorrect length: ${derivedKey1.length} (should be 32)`);
      failed++;
    }
    
    console.log(`📊 Key Derivation Test Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ Key derivation test failed: ${error.message}\n`);
    return false;
  }
}

// Test 5: Key Store Structure
async function testKeyStoreStructure() {
  console.log('📋 Test 5: Key Store Structure');
  
  try {
    const keyStorePath = '.key-store.json';
    
    // Remove existing key store for test
    if (fs.existsSync(keyStorePath)) {
      fs.unlinkSync(keyStorePath);
    }
    
    // Create test key store structure
    const now = new Date();
    const nextRotation = new Date(now.getTime() + (90 * 24 * 60 * 60 * 1000));
    
    const testKeyStore = {
      currentVersion: 'v1',
      masterKeyHash: crypto.createHash('sha256').update('test-master-key').digest('hex'),
      keys: {
        'v1': {
          version: 'v1',
          key: crypto.randomBytes(32).toString('hex'),
          createdAt: now,
          status: 'active',
          derivationSalt: crypto.randomBytes(32).toString('hex'),
          algorithm: 'PBKDF2-SHA256'
        }
      },
      lastRotation: now,
      nextRotation: nextRotation
    };
    
    fs.writeFileSync(keyStorePath, JSON.stringify(testKeyStore, null, 2), { mode: 0o600 });
    
    let passed = 0;
    let failed = 0;
    
    if (fs.existsSync(keyStorePath)) {
      console.log('✅ Key store file created');
      passed++;
      
      const storedData = JSON.parse(fs.readFileSync(keyStorePath, 'utf8'));
      
      // Check required fields
      const requiredFields = ['currentVersion', 'masterKeyHash', 'keys', 'lastRotation', 'nextRotation'];
      const hasAllFields = requiredFields.every(field => storedData[field] !== undefined);
      
      if (hasAllFields) {
        console.log('✅ Key store has all required fields');
        passed++;
      } else {
        console.log('❌ Key store missing required fields');
        failed++;
      }
      
      // Check key structure
      const key = storedData.keys['v1'];
      const requiredKeyFields = ['version', 'key', 'createdAt', 'status', 'derivationSalt', 'algorithm'];
      const keyHasAllFields = requiredKeyFields.every(field => key[field] !== undefined);
      
      if (keyHasAllFields) {
        console.log('✅ Key structure is correct');
        passed++;
      } else {
        console.log('❌ Key structure is incorrect');
        failed++;
      }
      
    } else {
      console.log('❌ Key store file not created');
      failed++;
    }
    
    console.log(`📊 Key Store Test Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ Key store test failed: ${error.message}\n`);
    return false;
  }
}

// Test 6: Rotation Interval Logic
async function testRotationIntervalLogic() {
  console.log('📋 Test 6: Rotation Interval Logic');
  
  try {
    const rotationIntervalDays = 90;
    const now = new Date();
    
    // Test future rotation date
    const futureRotation = new Date(now.getTime() + (rotationIntervalDays * 24 * 60 * 60 * 1000));
    const isRotationNeededFuture = now >= futureRotation;
    
    // Test past rotation date
    const pastRotation = new Date(now.getTime() - (1 * 24 * 60 * 60 * 1000)); // 1 day ago
    const isRotationNeededPast = now >= pastRotation;
    
    let passed = 0;
    let failed = 0;
    
    if (!isRotationNeededFuture) {
      console.log('✅ Future rotation date correctly identified as not needed');
      passed++;
    } else {
      console.log('❌ Future rotation date incorrectly identified as needed');
      failed++;
    }
    
    if (isRotationNeededPast) {
      console.log('✅ Past rotation date correctly identified as needed');
      passed++;
    } else {
      console.log('❌ Past rotation date incorrectly identified as not needed');
      failed++;
    }
    
    // Test interval calculation
    const daysDifference = Math.floor((futureRotation.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDifference === rotationIntervalDays) {
      console.log('✅ Rotation interval calculation is correct');
      passed++;
    } else {
      console.log(`❌ Rotation interval calculation is incorrect: ${daysDifference} (should be ${rotationIntervalDays})`);
      failed++;
    }
    
    console.log(`📊 Rotation Interval Test Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ Rotation interval test failed: ${error.message}\n`);
    return false;
  }
}

// Test 7: CLI Script Existence
async function testCLIScript() {
  console.log('📋 Test 7: CLI Script Existence');
  
  try {
    const cliScript = 'server/scripts/key-rotation-cli.ts';
    
    let passed = 0;
    let failed = 0;
    
    if (fs.existsSync(cliScript)) {
      console.log('✅ CLI script exists');
      passed++;
      
      const content = fs.readFileSync(cliScript, 'utf8');
      
      // Check for required commands
      const requiredCommands = ['status', 'rotate', 'force-rotate', 'reencrypt', 'rollback', 'revoke'];
      const hasAllCommands = requiredCommands.every(cmd => content.includes(cmd));
      
      if (hasAllCommands) {
        console.log('✅ CLI script has all required commands');
        passed++;
      } else {
        console.log('❌ CLI script missing required commands');
        failed++;
      }
      
    } else {
      console.log('❌ CLI script not found');
      failed++;
    }
    
    console.log(`📊 CLI Script Test Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ CLI script test failed: ${error.message}\n`);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  const tests = [
    { name: 'Key Rotation Configuration Files', fn: testKeyRotationFiles },
    { name: 'Environment Configuration', fn: testEnvironmentConfig },
    { name: 'Master Key Generation', fn: testMasterKeyGeneration },
    { name: 'Key Derivation Logic', fn: testKeyDerivation },
    { name: 'Key Store Structure', fn: testKeyStoreStructure },
    { name: 'Rotation Interval Logic', fn: testRotationIntervalLogic },
    { name: 'CLI Script Existence', fn: testCLIScript }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ Test "${test.name}" threw an error:`, error.message);
      failed++;
    }
  }
  
  console.log('='.repeat(50));
  console.log(`🔑 Key Rotation System Test Results:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All key rotation system tests passed!');
    console.log('\n📋 Next steps:');
    console.log('1. Initialize key rotation: npm run key-rotation status');
    console.log('2. Test manual rotation: npm run key-rotation force-rotate');
    console.log('3. Test data re-encryption: npm run key-rotation reencrypt <version>');
    console.log('4. Monitor automatic rotation schedule');
    process.exit(0);
  } else {
    console.log('\n⚠️ Some key rotation system tests failed. Please review and fix issues.');
    process.exit(1);
  }
}

// Run tests
runAllTests().catch(error => {
  console.error('❌ Test runner error:', error);
  process.exit(1);
});
