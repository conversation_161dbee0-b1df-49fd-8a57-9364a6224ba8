#!/usr/bin/env node

/**
 * Comprehensive Security Test Suite
 * Runs all security feature tests and generates a detailed report
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🔒 Starting Comprehensive Security Test Suite...\n');

// Test suite configuration
const testSuites = [
  {
    name: 'Email Protection System',
    script: 'test-email-protection.js',
    description: 'Tests field-level encryption, obfuscation, and audit trails'
  },
  {
    name: 'SQLCipher Integration',
    script: 'test-sqlcipher-integration.js',
    description: 'Tests database-level encryption and migration tools'
  },
  {
    name: 'Key Rotation System',
    script: 'test-key-rotation.js',
    description: 'Tests automated key rotation and data re-encryption'
  },
  {
    name: 'Database Connection Security',
    script: 'test-database-connection-security.js',
    description: 'Tests connection pooling, health monitoring, and timeouts'
  },
  {
    name: 'Security Headers',
    script: 'test-security-headers.js',
    description: 'Tests CSP, HSTS, and other web security headers'
  }
];

// Test results storage
const testResults = {
  totalSuites: testSuites.length,
  passedSuites: 0,
  failedSuites: 0,
  suiteResults: [],
  startTime: new Date(),
  endTime: null,
  duration: 0
};

// Run a single test suite
function runTestSuite(testSuite) {
  return new Promise((resolve) => {
    console.log(`🧪 Running ${testSuite.name}...`);
    console.log(`📝 ${testSuite.description}`);
    
    const startTime = Date.now();
    const child = spawn('node', [testSuite.script], {
      stdio: 'pipe',
      cwd: process.cwd()
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const result = {
        name: testSuite.name,
        script: testSuite.script,
        description: testSuite.description,
        passed: code === 0,
        exitCode: code,
        duration: duration,
        stdout: stdout,
        stderr: stderr
      };

      if (code === 0) {
        console.log(`✅ ${testSuite.name} PASSED (${duration}ms)\n`);
        testResults.passedSuites++;
      } else {
        console.log(`❌ ${testSuite.name} FAILED (${duration}ms)`);
        console.log(`Exit code: ${code}`);
        if (stderr) {
          console.log(`Error output: ${stderr.substring(0, 200)}...`);
        }
        console.log('');
        testResults.failedSuites++;
      }

      testResults.suiteResults.push(result);
      resolve(result);
    });

    child.on('error', (error) => {
      console.log(`❌ ${testSuite.name} ERROR: ${error.message}\n`);
      
      const result = {
        name: testSuite.name,
        script: testSuite.script,
        description: testSuite.description,
        passed: false,
        exitCode: -1,
        duration: Date.now() - startTime,
        stdout: '',
        stderr: error.message
      };

      testResults.failedSuites++;
      testResults.suiteResults.push(result);
      resolve(result);
    });
  });
}

// Generate detailed test report
function generateTestReport() {
  const report = {
    summary: {
      totalSuites: testResults.totalSuites,
      passedSuites: testResults.passedSuites,
      failedSuites: testResults.failedSuites,
      successRate: ((testResults.passedSuites / testResults.totalSuites) * 100).toFixed(1),
      totalDuration: testResults.duration,
      timestamp: testResults.endTime.toISOString()
    },
    suiteResults: testResults.suiteResults.map(result => ({
      name: result.name,
      passed: result.passed,
      duration: result.duration,
      exitCode: result.exitCode,
      hasErrors: result.stderr.length > 0
    })),
    detailedResults: testResults.suiteResults
  };

  // Save report to file
  const reportPath = `security-test-report-${Date.now()}.json`;
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  return { report, reportPath };
}

// Generate HTML report
function generateHTMLReport(report, reportPath) {
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .suite { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 6px; }
        .suite-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .suite-name { font-weight: bold; font-size: 1.1em; }
        .duration { color: #666; font-size: 0.9em; }
        .description { color: #666; margin-bottom: 10px; }
        .status { padding: 4px 8px; border-radius: 4px; color: white; font-size: 0.9em; }
        .status.passed { background-color: #28a745; }
        .status.failed { background-color: #dc3545; }
        .footer { text-align: center; margin-top: 30px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Security Test Report</h1>
            <p>Generated on ${report.summary.timestamp}</p>
        </div>
        
        <div class="summary">
            <div class="metric">
                <div class="metric-value">${report.summary.totalSuites}</div>
                <div>Total Suites</div>
            </div>
            <div class="metric">
                <div class="metric-value passed">${report.summary.passedSuites}</div>
                <div>Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value failed">${report.summary.failedSuites}</div>
                <div>Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.summary.successRate}%</div>
                <div>Success Rate</div>
            </div>
            <div class="metric">
                <div class="metric-value">${(report.summary.totalDuration / 1000).toFixed(1)}s</div>
                <div>Total Duration</div>
            </div>
        </div>
        
        <h2>Test Suite Results</h2>
        ${report.suiteResults.map(suite => `
            <div class="suite">
                <div class="suite-header">
                    <div class="suite-name">${suite.name}</div>
                    <div>
                        <span class="status ${suite.passed ? 'passed' : 'failed'}">
                            ${suite.passed ? 'PASSED' : 'FAILED'}
                        </span>
                        <span class="duration">${suite.duration}ms</span>
                    </div>
                </div>
                <div class="description">${testSuites.find(s => s.name === suite.name)?.description || ''}</div>
                ${suite.hasErrors ? '<div style="color: #dc3545; font-size: 0.9em;">⚠️ Errors detected in output</div>' : ''}
            </div>
        `).join('')}
        
        <div class="footer">
            <p>Detailed results saved to: ${reportPath}</p>
        </div>
    </div>
</body>
</html>`;

  const htmlReportPath = reportPath.replace('.json', '.html');
  fs.writeFileSync(htmlReportPath, htmlContent);
  return htmlReportPath;
}

// Performance benchmark
async function runPerformanceBenchmark() {
  console.log('📊 Running performance benchmarks...\n');
  
  const benchmarks = {
    emailEncryption: {
      name: 'Email Encryption Performance',
      operations: 1000,
      startTime: 0,
      endTime: 0,
      duration: 0,
      opsPerSecond: 0
    },
    keyDerivation: {
      name: 'Key Derivation Performance',
      operations: 100,
      startTime: 0,
      endTime: 0,
      duration: 0,
      opsPerSecond: 0
    }
  };

  // Import crypto module
  const crypto = await import('crypto');

  // Simulate email encryption benchmark
  benchmarks.emailEncryption.startTime = Date.now();
  for (let i = 0; i < benchmarks.emailEncryption.operations; i++) {
    // Simulate encryption operation
    const data = `test${i}@example.com`;
    const hash = crypto.createHash('sha256').update(data).digest('hex');
  }
  benchmarks.emailEncryption.endTime = Date.now();
  benchmarks.emailEncryption.duration = benchmarks.emailEncryption.endTime - benchmarks.emailEncryption.startTime;
  benchmarks.emailEncryption.opsPerSecond = Math.round((benchmarks.emailEncryption.operations / benchmarks.emailEncryption.duration) * 1000);

  // Simulate key derivation benchmark
  benchmarks.keyDerivation.startTime = Date.now();
  for (let i = 0; i < benchmarks.keyDerivation.operations; i++) {
    // Simulate key derivation operation
    const key = crypto.pbkdf2Sync('password', 'salt', 1000, 32, 'sha256');
  }
  benchmarks.keyDerivation.endTime = Date.now();
  benchmarks.keyDerivation.duration = benchmarks.keyDerivation.endTime - benchmarks.keyDerivation.startTime;
  benchmarks.keyDerivation.opsPerSecond = Math.round((benchmarks.keyDerivation.operations / benchmarks.keyDerivation.duration) * 1000);

  console.log('📊 Performance Benchmark Results:');
  Object.values(benchmarks).forEach(benchmark => {
    console.log(`  ${benchmark.name}:`);
    console.log(`    Operations: ${benchmark.operations}`);
    console.log(`    Duration: ${benchmark.duration}ms`);
    console.log(`    Ops/sec: ${benchmark.opsPerSecond}`);
    console.log('');
  });

  return benchmarks;
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting comprehensive security test suite...\n');
  
  // Run all test suites sequentially
  for (const testSuite of testSuites) {
    await runTestSuite(testSuite);
  }

  // Calculate final results
  testResults.endTime = new Date();
  testResults.duration = testResults.endTime.getTime() - testResults.startTime.getTime();

  // Run performance benchmarks
  const benchmarks = await runPerformanceBenchmark();

  // Generate reports
  console.log('📋 Generating test reports...\n');
  const { report, reportPath } = generateTestReport();
  const htmlReportPath = generateHTMLReport(report, reportPath);

  // Display final summary
  console.log('='.repeat(60));
  console.log('🔒 COMPREHENSIVE SECURITY TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`📊 Total Test Suites: ${testResults.totalSuites}`);
  console.log(`✅ Passed: ${testResults.passedSuites}`);
  console.log(`❌ Failed: ${testResults.failedSuites}`);
  console.log(`📈 Success Rate: ${((testResults.passedSuites / testResults.totalSuites) * 100).toFixed(1)}%`);
  console.log(`⏱️ Total Duration: ${(testResults.duration / 1000).toFixed(1)} seconds`);
  console.log('');

  // Display individual suite results
  console.log('📋 Individual Suite Results:');
  testResults.suiteResults.forEach(result => {
    const status = result.passed ? '✅ PASSED' : '❌ FAILED';
    const duration = `(${result.duration}ms)`;
    console.log(`  ${status} ${result.name} ${duration}`);
  });
  console.log('');

  // Display performance benchmarks
  console.log('📊 Performance Benchmarks:');
  Object.values(benchmarks).forEach(benchmark => {
    console.log(`  ${benchmark.name}: ${benchmark.opsPerSecond} ops/sec`);
  });
  console.log('');

  // Display report file locations
  console.log('📄 Reports Generated:');
  console.log(`  JSON Report: ${reportPath}`);
  console.log(`  HTML Report: ${htmlReportPath}`);
  console.log('');

  // Final recommendations
  if (testResults.failedSuites === 0) {
    console.log('🎉 ALL SECURITY TESTS PASSED!');
    console.log('');
    console.log('✅ Your application has comprehensive security measures in place:');
    console.log('  • Field-level email encryption with obfuscation');
    console.log('  • SQLCipher database-level encryption');
    console.log('  • Automated key rotation system');
    console.log('  • Enhanced database connection security');
    console.log('  • Comprehensive web security headers');
    console.log('');
    console.log('🔒 Security Implementation Complete!');
    console.log('');
    console.log('📋 Next Steps:');
    console.log('  1. Deploy to production with environment variables configured');
    console.log('  2. Monitor security logs and alerts');
    console.log('  3. Schedule regular security audits');
    console.log('  4. Keep dependencies updated');
    console.log('  5. Review and update security policies regularly');
  } else {
    console.log('⚠️ SOME SECURITY TESTS FAILED');
    console.log('');
    console.log('❌ Failed test suites:');
    testResults.suiteResults
      .filter(result => !result.passed)
      .forEach(result => {
        console.log(`  • ${result.name} (Exit code: ${result.exitCode})`);
      });
    console.log('');
    console.log('🔧 Please review the detailed reports and fix the failing tests before deployment.');
  }

  console.log('='.repeat(60));

  // Exit with appropriate code
  process.exit(testResults.failedSuites === 0 ? 0 : 1);
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️ Test suite interrupted by user');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception in test runner:', error);
  process.exit(1);
});

// Run the test suite if this file is executed directly
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);

if (process.argv[1] === __filename) {
  runAllTests().catch(error => {
    console.error('❌ Test runner error:', error);
    process.exit(1);
  });
}
