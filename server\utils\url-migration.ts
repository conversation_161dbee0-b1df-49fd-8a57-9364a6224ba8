import { storage } from '../storage-factory';
import {
  isLocalhostUrl,
  updateUrlToCurrentDomain,
  detectPortFromDatabase,
  extractPortFromUrl,
  smartUrlMigration
} from './url-utils';

/**
 * Database URL migration utilities
 */

interface MigrationResult {
  updated: number;
  errors: string[];
}

interface PortDetectionResult {
  detectedPorts: string[];
  mostCommonPort: string | null;
  totalLocalhostUrls: number;
}

/**
 * Detect ports used in localhost URLs across the database
 */
export async function detectPortsInDatabase(): Promise<PortDetectionResult> {
  const portCounts: Record<string, number> = {};
  let totalLocalhostUrls = 0;

  try {
    // Check general settings
    const settings = await storage.getGeneralSettings();
    if (settings) {
      [settings.logoUrl, settings.faviconUrl].forEach(url => {
        if (url && isLocalhostUrl(url)) {
          totalLocalhostUrls++;
          const port = extractPortFromUrl(url);
          if (port) {
            portCounts[port] = (portCounts[port] || 0) + 1;
          }
        }
      });
    }

    // Check products
    const products = await storage.getProducts();
    products.forEach(product => {
      if (product.imageUrl && isLocalhostUrl(product.imageUrl)) {
        totalLocalhostUrls++;
        const port = extractPortFromUrl(product.imageUrl);
        if (port) {
          portCounts[port] = (portCounts[port] || 0) + 1;
        }
      }
    });

    // Check custom checkout pages
    const pages = await storage.getCustomCheckoutPages();
    pages.forEach(page => {
      [page.imageUrl, page.headerLogo, page.footerLogo].forEach(url => {
        if (url && isLocalhostUrl(url)) {
          totalLocalhostUrls++;
          const port = extractPortFromUrl(url);
          if (port) {
            portCounts[port] = (portCounts[port] || 0) + 1;
          }
        }
      });
    });

    // Check homepage
    const homepage = await storage.getHomepageConfig();
    if (homepage) {
      const sectionsData = JSON.parse(homepage.sectionsData || '[]');
      const seoSettings = JSON.parse(homepage.seoSettings || '{}');

      sectionsData.forEach((section: any) => {
        if (section.content) {
          ['backgroundImage', 'image', 'logoUrl', 'iconUrl'].forEach(field => {
            const url = section.content[field];
            if (url && isLocalhostUrl(url)) {
              totalLocalhostUrls++;
              const port = extractPortFromUrl(url);
              if (port) {
                portCounts[port] = (portCounts[port] || 0) + 1;
              }
            }
          });
        }
      });

      ['ogImage', 'twitterImage'].forEach(field => {
        const url = seoSettings[field];
        if (url && isLocalhostUrl(url)) {
          totalLocalhostUrls++;
          const port = extractPortFromUrl(url);
          if (port) {
            portCounts[port] = (portCounts[port] || 0) + 1;
          }
        }
      });
    }

    // Determine most common port
    const detectedPorts = Object.keys(portCounts);
    let mostCommonPort: string | null = null;
    let maxCount = 0;

    for (const [port, count] of Object.entries(portCounts)) {
      if (count > maxCount) {
        maxCount = count;
        mostCommonPort = port;
      }
    }

    return {
      detectedPorts,
      mostCommonPort,
      totalLocalhostUrls
    };
  } catch (error) {
    console.error('Error detecting ports in database:', error);
    return {
      detectedPorts: [],
      mostCommonPort: null,
      totalLocalhostUrls: 0
    };
  }
}

/**
 * Update URLs in custom checkout pages
 */
async function migrateCustomCheckoutPageUrls(targetDomain?: string, preservePort: boolean = false): Promise<MigrationResult> {
  const result: MigrationResult = { updated: 0, errors: [] };

  try {
    const pages = await storage.getCustomCheckoutPages();

    for (const page of pages) {
      let hasChanges = false;
      const updates: any = {};

      // Update image URL
      if (page.imageUrl && isLocalhostUrl(page.imageUrl)) {
        try {
          updates.imageUrl = targetDomain
            ? await smartUrlMigration(page.imageUrl, targetDomain, preservePort)
            : page.imageUrl.replace(/^https?:\/\/[^\/]+/, '');
          hasChanges = true;
        } catch (error) {
          result.errors.push(`Failed to update imageUrl for page ${page.id}: ${error}`);
        }
      }

      // Update header logo
      if (page.headerLogo && isLocalhostUrl(page.headerLogo)) {
        try {
          updates.headerLogo = targetDomain
            ? await smartUrlMigration(page.headerLogo, targetDomain, preservePort)
            : page.headerLogo.replace(/^https?:\/\/[^\/]+/, '');
          hasChanges = true;
        } catch (error) {
          result.errors.push(`Failed to update headerLogo for page ${page.id}: ${error}`);
        }
      }

      // Update footer logo
      if (page.footerLogo && isLocalhostUrl(page.footerLogo)) {
        try {
          updates.footerLogo = targetDomain
            ? await smartUrlMigration(page.footerLogo, targetDomain, preservePort)
            : page.footerLogo.replace(/^https?:\/\/[^\/]+/, '');
          hasChanges = true;
        } catch (error) {
          result.errors.push(`Failed to update footerLogo for page ${page.id}: ${error}`);
        }
      }

      if (hasChanges) {
        updates.updatedAt = new Date().toISOString();
        await storage.updateCustomCheckoutPage(page.id, updates);
        result.updated++;
      }
    }
  } catch (error) {
    result.errors.push(`Failed to migrate custom checkout page URLs: ${error}`);
  }

  return result;
}

/**
 * Update URLs in general settings
 */
async function migrateGeneralSettingsUrls(targetDomain?: string, preservePort: boolean = false): Promise<MigrationResult> {
  const result: MigrationResult = { updated: 0, errors: [] };

  try {
    const settings = await storage.getGeneralSettings();
    if (!settings) return result;

    let hasChanges = false;
    const updates: any = {};

    // Update logo URL
    if (settings.logoUrl && isLocalhostUrl(settings.logoUrl)) {
      try {
        updates.logoUrl = targetDomain
          ? await smartUrlMigration(settings.logoUrl, targetDomain, preservePort)
          : settings.logoUrl.replace(/^https?:\/\/[^\/]+/, '');
        hasChanges = true;
      } catch (error) {
        result.errors.push(`Failed to update logoUrl in general settings: ${error}`);
      }
    }

    // Update favicon URL
    if (settings.faviconUrl && isLocalhostUrl(settings.faviconUrl)) {
      try {
        updates.faviconUrl = targetDomain
          ? await smartUrlMigration(settings.faviconUrl, targetDomain, preservePort)
          : settings.faviconUrl.replace(/^https?:\/\/[^\/]+/, '');
        hasChanges = true;
      } catch (error) {
        result.errors.push(`Failed to update faviconUrl in general settings: ${error}`);
      }
    }

    if (hasChanges) {
      updates.updatedAt = new Date().toISOString();
      await storage.updateGeneralSettings(updates);
      result.updated++;
    }
  } catch (error) {
    result.errors.push(`Failed to migrate general settings URLs: ${error}`);
  }

  return result;
}

/**
 * Update URLs in products
 */
async function migrateProductUrls(targetDomain?: string, preservePort: boolean = false): Promise<MigrationResult> {
  const result: MigrationResult = { updated: 0, errors: [] };

  try {
    const products = await storage.getProducts();

    for (const product of products) {
      if (product.imageUrl && isLocalhostUrl(product.imageUrl)) {
        try {
          const newImageUrl = targetDomain
            ? await smartUrlMigration(product.imageUrl, targetDomain, preservePort)
            : product.imageUrl.replace(/^https?:\/\/[^\/]+/, '');

          await storage.updateProduct(product.id, { imageUrl: newImageUrl });
          result.updated++;
        } catch (error) {
          result.errors.push(`Failed to update imageUrl for product ${product.id}: ${error}`);
        }
      }
    }
  } catch (error) {
    result.errors.push(`Failed to migrate product URLs: ${error}`);
  }

  return result;
}

/**
 * Update URLs in homepage configuration
 */
async function migrateHomepageUrls(targetDomain?: string, preservePort: boolean = false): Promise<MigrationResult> {
  const result: MigrationResult = { updated: 0, errors: [] };

  try {
    const homepage = await storage.getHomepageConfig();
    if (!homepage) return result;

    let hasChanges = false;
    let sectionsData = JSON.parse(homepage.sectionsData || '[]');
    let seoSettings = JSON.parse(homepage.seoSettings || '{}');

    // Update URLs in sections data
    sectionsData = sectionsData.map((section: any) => {
      if (section.content) {
        // Update background images
        if (section.content.backgroundImage && isLocalhostUrl(section.content.backgroundImage)) {
          section.content.backgroundImage = targetDomain
            ? smartUrlMigration(section.content.backgroundImage, targetDomain, preservePort)
            : section.content.backgroundImage.replace(/^https?:\/\/[^\/]+/, '');
          hasChanges = true;
        }

        // Update other image fields that might exist
        ['image', 'logoUrl', 'iconUrl'].forEach(field => {
          if (section.content[field] && isLocalhostUrl(section.content[field])) {
            section.content[field] = targetDomain
              ? smartUrlMigration(section.content[field], targetDomain, preservePort)
              : section.content[field].replace(/^https?:\/\/[^\/]+/, '');
            hasChanges = true;
          }
        });
      }
      return section;
    });

    // Update URLs in SEO settings
    ['ogImage', 'twitterImage'].forEach(field => {
      if (seoSettings[field] && isLocalhostUrl(seoSettings[field])) {
        seoSettings[field] = targetDomain
          ? smartUrlMigration(seoSettings[field], targetDomain, preservePort)
          : seoSettings[field].replace(/^https?:\/\/[^\/]+/, '');
        hasChanges = true;
      }
    });

    if (hasChanges) {
      await storage.updateHomepageConfig({
        sectionsData: JSON.stringify(sectionsData),
        seoSettings: JSON.stringify(seoSettings),
        updatedAt: new Date().toISOString()
      });
      result.updated++;
    }
  } catch (error) {
    result.errors.push(`Failed to migrate homepage URLs: ${error}`);
  }

  return result;
}

/**
 * Run complete URL migration
 */
export async function migrateAllUrls(targetDomain?: string, preservePort: boolean = false): Promise<{
  totalUpdated: number;
  results: Record<string, MigrationResult>;
}> {
  console.log('🔄 Starting URL migration...');

  const results = {
    customCheckoutPages: await migrateCustomCheckoutPageUrls(targetDomain, preservePort),
    generalSettings: await migrateGeneralSettingsUrls(targetDomain, preservePort),
    products: await migrateProductUrls(targetDomain, preservePort),
    homepage: await migrateHomepageUrls(targetDomain, preservePort)
  };

  const totalUpdated = Object.values(results).reduce((sum, result) => sum + result.updated, 0);
  const totalErrors = Object.values(results).reduce((sum, result) => sum + result.errors.length, 0);

  console.log(`✅ URL migration completed: ${totalUpdated} records updated, ${totalErrors} errors`);

  if (totalErrors > 0) {
    console.log('❌ Migration errors:');
    Object.entries(results).forEach(([key, result]) => {
      if (result.errors.length > 0) {
        console.log(`  ${key}:`, result.errors);
      }
    });
  }

  return { totalUpdated, results };
}

/**
 * Convert localhost URLs to relative URLs (for better portability)
 */
export async function convertToRelativeUrls(): Promise<{
  totalUpdated: number;
  results: Record<string, MigrationResult>;
}> {
  return migrateAllUrls(); // No target domain = convert to relative
}

/**
 * Update URLs to a specific domain
 */
export async function updateUrlsToDomain(domain: string, preservePort: boolean = false): Promise<{
  totalUpdated: number;
  results: Record<string, MigrationResult>;
}> {
  // Ensure domain has protocol
  const targetDomain = domain.startsWith('http') ? domain : `https://${domain}`;
  return migrateAllUrls(targetDomain, preservePort);
}

/**
 * Smart migration that auto-detects the best port to use
 */
export async function smartPortMigration(targetDomain: string): Promise<{
  totalUpdated: number;
  results: Record<string, MigrationResult>;
  portDetection: PortDetectionResult;
}> {
  console.log('🔍 Starting smart port migration...');

  // Detect ports in the database
  const portDetection = await detectPortsInDatabase();

  console.log(`📊 Port detection results:`, {
    detectedPorts: portDetection.detectedPorts,
    mostCommonPort: portDetection.mostCommonPort,
    totalLocalhostUrls: portDetection.totalLocalhostUrls
  });

  // If we have a common port and the target domain doesn't specify one, add it
  let finalTargetDomain = targetDomain;
  if (portDetection.mostCommonPort && !targetDomain.includes(':')) {
    try {
      const urlObj = new URL(targetDomain);
      // Only add port if it's not a standard port (80 for HTTP, 443 for HTTPS)
      const isStandardPort = (urlObj.protocol === 'http:' && portDetection.mostCommonPort === '80') ||
                            (urlObj.protocol === 'https:' && portDetection.mostCommonPort === '443');

      if (!isStandardPort) {
        finalTargetDomain = `${urlObj.protocol}//${urlObj.hostname}:${portDetection.mostCommonPort}${urlObj.pathname}`;
      }
    } catch (error) {
      console.warn('Error parsing target domain for port addition:', error);
    }
  }

  console.log(`🎯 Final target domain: ${finalTargetDomain}`);

  const migrationResult = await migrateAllUrls(finalTargetDomain, false);

  return {
    ...migrationResult,
    portDetection
  };
}

/**
 * Migration with manual port specification
 */
export async function migrateWithPort(targetDomain: string, port: string): Promise<{
  totalUpdated: number;
  results: Record<string, MigrationResult>;
}> {
  console.log(`🔧 Starting migration with manual port: ${port}`);

  // Add port to target domain if not already present
  let finalTargetDomain = targetDomain;
  try {
    const urlObj = new URL(targetDomain);
    if (!urlObj.port) {
      finalTargetDomain = `${urlObj.protocol}//${urlObj.hostname}:${port}${urlObj.pathname}`;
    }
  } catch (error) {
    console.warn('Error parsing target domain for manual port:', error);
  }

  console.log(`🎯 Target domain with port: ${finalTargetDomain}`);

  return migrateAllUrls(finalTargetDomain, false);
}
