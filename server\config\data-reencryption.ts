import { db } from '../db';
import { allowedEmails } from '../../shared/schema';
import { eq } from 'drizzle-orm';
import { keyRotationManager } from './key-rotation';
import { encryptEmail, decryptEmail } from './database-security';
import { securityMonitor, SecurityEventType, SecuritySeverity } from './security-monitoring';

// Re-encryption configuration
interface ReencryptionConfig {
  batchSize: number;
  maxRetries: number;
  retryDelayMs: number;
  enableRollback: boolean;
  verifyAfterReencryption: boolean;
}

// Re-encryption result
interface ReencryptionResult {
  success: boolean;
  totalRecords: number;
  reencryptedRecords: number;
  failedRecords: number;
  errors: string[];
  rollbackData?: ReencryptionRollbackData[];
}

// Rollback data structure
interface ReencryptionRollbackData {
  id: number;
  originalData: string;
  newData: string;
  keyVersion: string;
}

// Default configuration
const DEFAULT_CONFIG: ReencryptionConfig = {
  batchSize: parseInt(process.env.REENCRYPTION_BATCH_SIZE || '100'),
  maxRetries: parseInt(process.env.REENCRYPTION_MAX_RETRIES || '3'),
  retryDelayMs: parseInt(process.env.REENCRYPTION_RETRY_DELAY || '1000'),
  enableRollback: process.env.ENABLE_REENCRYPTION_ROLLBACK !== 'false',
  verifyAfterReencryption: process.env.VERIFY_AFTER_REENCRYPTION !== 'false'
};

export class DataReencryptionManager {
  private config: ReencryptionConfig;

  constructor(config: Partial<ReencryptionConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // Re-encrypt all email data with new key
  async reencryptEmailData(newKeyVersion: string): Promise<ReencryptionResult> {
    const result: ReencryptionResult = {
      success: false,
      totalRecords: 0,
      reencryptedRecords: 0,
      failedRecords: 0,
      errors: [],
      rollbackData: this.config.enableRollback ? [] : undefined
    };

    console.log(`🔄 Starting email data re-encryption with key version: ${newKeyVersion}`);

    try {
      // Get all email records
      const emailRecords = await db.select().from(allowedEmails);
      result.totalRecords = emailRecords.length;

      console.log(`📊 Found ${result.totalRecords} email records to re-encrypt`);

      if (result.totalRecords === 0) {
        result.success = true;
        return result;
      }

      // Get current and new encryption keys
      const currentKey = keyRotationManager.getCurrentKey();
      const newKey = keyRotationManager.getKeyByVersion(newKeyVersion);

      if (!newKey) {
        throw new Error(`New key version ${newKeyVersion} not found`);
      }

      // Process records in batches
      for (let i = 0; i < emailRecords.length; i += this.config.batchSize) {
        const batch = emailRecords.slice(i, i + this.config.batchSize);
        
        console.log(`🔄 Processing batch ${Math.floor(i / this.config.batchSize) + 1}/${Math.ceil(emailRecords.length / this.config.batchSize)}`);

        for (const record of batch) {
          let retryCount = 0;
          let success = false;

          while (retryCount < this.config.maxRetries && !success) {
            try {
              await this.reencryptSingleRecord(record, currentKey, newKey, newKeyVersion, result);
              success = true;
              result.reencryptedRecords++;
            } catch (error) {
              retryCount++;
              const errorMsg = `Failed to re-encrypt record ${record.id} (attempt ${retryCount}): ${error.message}`;
              
              if (retryCount >= this.config.maxRetries) {
                console.error(`❌ ${errorMsg}`);
                result.errors.push(errorMsg);
                result.failedRecords++;
              } else {
                console.warn(`⚠️ ${errorMsg}, retrying...`);
                await this.delay(this.config.retryDelayMs);
              }
            }
          }
        }
      }

      // Verify re-encryption if enabled
      if (this.config.verifyAfterReencryption) {
        console.log('🔍 Verifying re-encryption...');
        const verificationResult = await this.verifyReencryption(newKeyVersion);
        
        if (!verificationResult.success) {
          result.errors.push(...verificationResult.errors);
          throw new Error('Re-encryption verification failed');
        }
      }

      result.success = result.failedRecords === 0;

      // Log security event
      securityMonitor.logSecurityEvent(
        SecurityEventType.DATA_EXPORT, // Using this as closest match for data re-encryption
        {
          userId: undefined,
          username: 'system',
          role: 'system' as any,
          permissions: [],
          ipAddress: 'localhost'
        },
        {
          action: 'DATA_REENCRYPTION',
          keyVersion: newKeyVersion,
          totalRecords: result.totalRecords,
          reencryptedRecords: result.reencryptedRecords,
          failedRecords: result.failedRecords,
          success: result.success
        },
        result.success ? SecuritySeverity.MEDIUM : SecuritySeverity.HIGH
      );

      console.log(`📊 Re-encryption Summary:`);
      console.log(`  - Total records: ${result.totalRecords}`);
      console.log(`  - Re-encrypted: ${result.reencryptedRecords}`);
      console.log(`  - Failed: ${result.failedRecords}`);
      console.log(`  - Success: ${result.success ? 'Yes' : 'No'}`);

    } catch (error) {
      const errorMsg = `Re-encryption failed: ${error.message}`;
      console.error(`❌ ${errorMsg}`);
      result.errors.push(errorMsg);
    }

    return result;
  }

  // Re-encrypt a single record
  private async reencryptSingleRecord(
    record: any,
    currentKey: string,
    newKey: string,
    newKeyVersion: string,
    result: ReencryptionResult
  ): Promise<void> {
    // Check if email is already encrypted
    if (!record.email.includes(':') || !record.email.startsWith('v')) {
      // Email is not encrypted, skip
      return;
    }

    // Decrypt with current key
    const decryptedEmail = decryptEmail(record.email);

    // Store rollback data if enabled
    if (this.config.enableRollback && result.rollbackData) {
      result.rollbackData.push({
        id: record.id,
        originalData: record.email,
        newData: '', // Will be filled after encryption
        keyVersion: newKeyVersion
      });
    }

    // Re-encrypt with new key
    const reencryptedEmail = encryptEmail(decryptedEmail, newKeyVersion);

    // Update rollback data with new encrypted value
    if (this.config.enableRollback && result.rollbackData) {
      const rollbackEntry = result.rollbackData[result.rollbackData.length - 1];
      rollbackEntry.newData = reencryptedEmail;
    }

    // Update database record
    await db
      .update(allowedEmails)
      .set({ 
        email: reencryptedEmail,
        lastUpdated: new Date().toISOString()
      })
      .where(eq(allowedEmails.id, record.id));
  }

  // Verify re-encryption
  private async verifyReencryption(keyVersion: string): Promise<{ success: boolean; errors: string[] }> {
    const result = { success: true, errors: [] };

    try {
      // Get a sample of records to verify
      const sampleRecords = await db.select().from(allowedEmails).limit(10);

      for (const record of sampleRecords) {
        try {
          // Skip non-encrypted emails
          if (!record.email.includes(':') || !record.email.startsWith('v')) {
            continue;
          }

          // Try to decrypt with new key
          const decryptedEmail = decryptEmail(record.email);
          
          // Verify it's a valid email format
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(decryptedEmail)) {
            result.errors.push(`Invalid email format after re-encryption: record ${record.id}`);
            result.success = false;
          }
        } catch (error) {
          result.errors.push(`Failed to decrypt re-encrypted record ${record.id}: ${error.message}`);
          result.success = false;
        }
      }
    } catch (error) {
      result.errors.push(`Verification failed: ${error.message}`);
      result.success = false;
    }

    return result;
  }

  // Rollback re-encryption
  async rollbackReencryption(rollbackData: ReencryptionRollbackData[]): Promise<{ success: boolean; errors: string[] }> {
    const result = { success: true, errors: [] };

    console.log(`🔄 Starting re-encryption rollback for ${rollbackData.length} records...`);

    try {
      for (const rollbackEntry of rollbackData) {
        try {
          await db
            .update(allowedEmails)
            .set({ 
              email: rollbackEntry.originalData,
              lastUpdated: new Date().toISOString()
            })
            .where(eq(allowedEmails.id, rollbackEntry.id));

          console.log(`✅ Rolled back record ${rollbackEntry.id}`);
        } catch (error) {
          const errorMsg = `Failed to rollback record ${rollbackEntry.id}: ${error.message}`;
          console.error(`❌ ${errorMsg}`);
          result.errors.push(errorMsg);
          result.success = false;
        }
      }

      // Log security event
      securityMonitor.logSecurityEvent(
        SecurityEventType.DATA_EXPORT,
        {
          userId: undefined,
          username: 'system',
          role: 'system' as any,
          permissions: [],
          ipAddress: 'localhost'
        },
        {
          action: 'DATA_REENCRYPTION_ROLLBACK',
          rolledBackRecords: rollbackData.length,
          success: result.success
        },
        SecuritySeverity.HIGH
      );

      console.log(`📊 Rollback Summary:`);
      console.log(`  - Records rolled back: ${rollbackData.length}`);
      console.log(`  - Errors: ${result.errors.length}`);
      console.log(`  - Success: ${result.success ? 'Yes' : 'No'}`);

    } catch (error) {
      const errorMsg = `Rollback failed: ${error.message}`;
      console.error(`❌ ${errorMsg}`);
      result.errors.push(errorMsg);
      result.success = false;
    }

    return result;
  }

  // Get re-encryption status
  async getReencryptionStatus(): Promise<{
    totalEmails: number;
    encryptedEmails: number;
    unencryptedEmails: number;
    keyVersions: Record<string, number>;
  }> {
    try {
      const emailRecords = await db.select().from(allowedEmails);
      
      const status = {
        totalEmails: emailRecords.length,
        encryptedEmails: 0,
        unencryptedEmails: 0,
        keyVersions: {} as Record<string, number>
      };

      for (const record of emailRecords) {
        if (record.email.includes(':') && record.email.startsWith('v')) {
          // Encrypted email
          status.encryptedEmails++;
          
          // Extract key version
          const version = record.email.split(':')[0];
          status.keyVersions[version] = (status.keyVersions[version] || 0) + 1;
        } else {
          // Unencrypted email
          status.unencryptedEmails++;
        }
      }

      return status;
    } catch (error) {
      console.error('Failed to get re-encryption status:', error);
      return {
        totalEmails: 0,
        encryptedEmails: 0,
        unencryptedEmails: 0,
        keyVersions: {}
      };
    }
  }

  // Utility function for delays
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Global data re-encryption manager instance
export const dataReencryptionManager = new DataReencryptionManager();
