module.exports = {
  apps: [{
    name: 'niraza-site',
    script: 'dist/index.js',
    cwd: '/home/<USER>/htdocs/niraza.site',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: '/home/<USER>/htdocs/niraza.site/logs/err.log',
    out_file: '/home/<USER>/htdocs/niraza.site/logs/out.log',
    log_file: '/home/<USER>/htdocs/niraza.site/logs/combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm Z',
    merge_logs: true,
    max_restarts: 10,
    min_uptime: '10s',
    kill_timeout: 5000,
    listen_timeout: 8000,
    shutdown_with_message: true,
    wait_ready: true
  }]
};
