import {
  User,
  InsertUser,
  Product,
  InsertProduct,
  Invoice,
  InsertInvoice,
  CustomCheckoutPage,
  InsertCustomCheckoutPage,
  AllowedEmail,
  InsertAllowedEmail,
  EmailTemplate,
  InsertEmailTemplate,
  PaypalButton,
  InsertPaypalButton,
  CustomInvoice,
  InsertCustomInvoice
} from '@shared/schema';
import { EmbedCode } from '@shared/embed-codes';

// Device interface
interface Device {
  id: string;
  name: string;
  ip: string;
  userAgent: string;
  lastLogin: string;
  createdAt: string;
}

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(insertUser: InsertUser): Promise<User>;
  verifyUserCredentials(username: string, password: string): Promise<boolean>;
  verifyUserCredentialsWithUser(user: User, password: string): Promise<boolean>;

  // Product methods
  getProducts(): Promise<Product[]>;
  getProduct(id: number): Promise<Product | undefined>;
  createProduct(insertProduct: InsertProduct): Promise<Product>;

  // Invoice methods
  createInvoice(insertInvoice: InsertInvoice): Promise<Invoice>;
  getInvoice(id: number): Promise<Invoice | undefined>;
  getInvoices(): Promise<Invoice[]>;
  getInvoicesPaginated(page: number, limit: number, search: string, orderType: 'all' | 'regular' | 'trial'): Promise<{
    invoices: Invoice[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }>;
  getInvoicesByCustomerEmail(email: string): Promise<Invoice[]>;
  updateInvoice(id: number, update: Partial<InsertInvoice>): Promise<Invoice | undefined>;
  deleteInvoice(id: number): Promise<boolean>;

  // Custom Checkout Page methods
  createCustomCheckoutPage(insertPage: InsertCustomCheckoutPage): Promise<CustomCheckoutPage>;
  getCustomCheckoutPage(id: number): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPageBySlug(slug: string): Promise<CustomCheckoutPage | undefined>;
  getCustomCheckoutPages(): Promise<CustomCheckoutPage[]>;
  updateCustomCheckoutPage(id: number, update: Partial<InsertCustomCheckoutPage>): Promise<CustomCheckoutPage | undefined>;
  incrementCustomCheckoutPageViews(id: number): Promise<void>;
  incrementCustomCheckoutPageConversions(id: number): Promise<void>;
  deleteCustomCheckoutPage(id: number): Promise<boolean>;

  // Allowed Email methods
  getAllowedEmails(): Promise<AllowedEmail[]>;
  getAllowedEmailsPaginated(page: number, limit: number, search: string): Promise<{
    emails: AllowedEmail[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }>;
  getAllowedEmail(id: number): Promise<AllowedEmail | undefined>;
  getEmailByAddress(email: string): Promise<AllowedEmail | undefined>;
  isEmailAllowed(email: string): Promise<boolean>;
  createAllowedEmail(insertAllowedEmail: InsertAllowedEmail): Promise<AllowedEmail>;
  updateAllowedEmail(id: number, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail | undefined>;
  updateOrCreateAllowedEmail(emailAddress: string, update: Partial<InsertAllowedEmail>): Promise<AllowedEmail>;
  deleteAllowedEmail(id: number): Promise<boolean>;
  bulkCreateAllowedEmails(emails: string[]): Promise<{ success: number; failed: number }>;

  // Email Template methods
  getEmailTemplates(): Promise<EmailTemplate[]>;
  getEmailTemplate(id: number): Promise<EmailTemplate | undefined>;
  createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate>;
  updateEmailTemplate(id: number, update: Partial<InsertEmailTemplate>): Promise<EmailTemplate | undefined>;
  deleteEmailTemplate(id: number): Promise<boolean>;

  // PayPal Button methods
  getPaypalButtons(): Promise<PaypalButton[]>;
  getPaypalButton(id: number): Promise<PaypalButton | undefined>;
  createPaypalButton(button: InsertPaypalButton): Promise<PaypalButton>;
  updatePaypalButton(id: number, update: Partial<InsertPaypalButton>): Promise<PaypalButton | undefined>;
  deletePaypalButton(id: number): Promise<boolean>;

  // Custom Invoice methods
  getCustomInvoices(): Promise<CustomInvoice[]>;
  getCustomInvoice(id: number): Promise<CustomInvoice | undefined>;
  getCustomInvoiceByNumber(invoiceNumber: string): Promise<CustomInvoice | undefined>;
  createCustomInvoice(invoice: InsertCustomInvoice): Promise<CustomInvoice>;
  updateCustomInvoice(id: number, update: Partial<InsertCustomInvoice>): Promise<CustomInvoice | undefined>;
  incrementCustomInvoiceViewCount(id: number): Promise<void>;
  markCustomInvoiceAsPaid(id: number): Promise<CustomInvoice | undefined>;
  deleteCustomInvoice(id: number): Promise<boolean>;

  // Configuration methods
  getGeneralSettings(): Promise<any>;
  getEmailConfig(): Promise<any>;
  getPaymentConfig(): Promise<any>;

  // User management methods (placeholders)
  saveResetToken(userId: number, token: string, expiry: Date): Promise<void>;
  validateResetToken(token: string): Promise<boolean>;
  getUserByResetToken(token: string): Promise<User | undefined>;
  updateUserPassword(userId: number, password: string): Promise<void>;
  clearResetToken(userId: number): Promise<void>;
  updateUsername(userId: number, username: string): Promise<void>;
  updateAutoLoginSettings(userId: number, rememberMe: boolean): Promise<void>;
  enableTwoFactor(userId: number, secret: string): Promise<void>;
  disableTwoFactor(userId: number): Promise<void>;
  verifyTwoFactorToken(userId: number, token: string): Promise<boolean>;
  generateRecoveryCodes(userId: number): Promise<string[]>;
  verifyRecoveryCode(userId: number, code: string): Promise<boolean>;
  addDevice(userId: number, deviceInfo: Omit<Device, 'id' | 'createdAt' | 'lastLogin'>): Promise<Device>;
  getDevices(userId: number): Promise<Device[]>;
  updateDeviceLastLogin(userId: number, deviceId: string): Promise<void>;
  removeDevice(userId: number, deviceId: string): Promise<boolean>;

  // General Settings methods
  getGeneralSettings(): Promise<any>;
  createGeneralSettings(settings: any): Promise<any>;
  updateGeneralSettings(update: any): Promise<any>;

  // Homepage Configuration methods
  getHomepageConfig(): Promise<any>;
  createHomepageConfig(config: any): Promise<any>;
  updateHomepageConfig(update: any): Promise<any>;

  // System Messages methods
  getSystemMessages(): Promise<any[]>;
  getSystemMessage(id: number): Promise<any>;
  getSystemMessageByMessageId(messageId: string): Promise<any>;
  createSystemMessage(message: any): Promise<any>;
  updateSystemMessage(id: number, update: any): Promise<any>;
  deleteSystemMessage(id: number): Promise<boolean>;

  // Contact Inquiry methods
  createContactInquiry(inquiry: any): Promise<any>;
  getContactInquiries(): Promise<any[]>;
  updateContactInquiry(id: number, update: any): Promise<any>;
  getEmbedCodes(): Promise<EmbedCode[]>;
  getEmbedCode(id: string): Promise<EmbedCode | undefined>;
  createEmbedCode(embedCode: EmbedCode): Promise<EmbedCode>;
  updateEmbedCode(id: string, update: Partial<EmbedCode>): Promise<EmbedCode | undefined>;
  deleteEmbedCode(id: string): Promise<boolean>;
}
