import { Product, Invoice } from "@shared/schema";

export interface ProductCardProps {
  product: Product;
  onSelect: (product: Product) => void;
}

export interface ProductGridProps {
  products: Product[];
  onSelectProduct: (product: Product) => void;
  isLoading: boolean;
}

export interface CheckoutFormProps {
  product: Product;
  onBack: () => void;
}

export interface OrderSummaryProps {
  product: Product;
}

export interface CheckoutState {
  status: 'form' | 'processing' | 'success' | 'error';
  error?: string;
  paypalInvoiceUrl?: string;
}

export type CheckoutFormData = {
  fullName: string;
  email: string;
  country: string;
};
