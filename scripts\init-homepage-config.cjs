const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '..', 'app', 'data', 'data.db');
const db = new Database(dbPath);

console.log('🏠 Initializing homepage configuration...');

try {
  // Check if homepage config already exists
  const existingConfigs = db.prepare('SELECT * FROM homepage_config').all();
  
  if (existingConfigs.length === 0) {
    console.log('📝 Creating default homepage configuration...');
    
    const defaultHomepageConfig = {
      sections_data: JSON.stringify([
        {
          id: 'hero-1',
          type: 'hero',
          title: 'Hero Section',
          enabled: true,
          order: 1,
          content: {
            title: 'Premium Productivity App Templates',
            subtitle: 'Design Systems & UI Kits',
            description: 'Build stunning productivity apps with our comprehensive collection of templates, components, and design systems. Professional, modern, and ready to use.',
            ctaText: 'Explore Templates',
            ctaLink: '#products',
            backgroundImage: '',
            backgroundType: 'gradient',
            backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            textColor: '#ffffff',
            showVideo: false,
            videoUrl: ''
          }
        },
        {
          id: 'features-1',
          type: 'features',
          title: 'Features Section',
          enabled: true,
          order: 2,
          content: {
            title: 'Why Choose Our Templates',
            subtitle: 'Everything you need to build amazing apps',
            features: [
              {
                id: 'feature-1',
                icon: '🚀',
                title: 'Fast Development',
                description: 'Pre-built components and templates to accelerate your development process.',
                enabled: true
              },
              {
                id: 'feature-2',
                icon: '🎨',
                title: 'Modern Design',
                description: 'Beautiful, responsive designs that work perfectly on all devices.',
                enabled: true
              },
              {
                id: 'feature-3',
                icon: '⚡',
                title: 'High Performance',
                description: 'Optimized code and best practices for lightning-fast applications.',
                enabled: true
              }
            ],
            layout: 'grid',
            columns: 3
          }
        },
        {
          id: 'products-1',
          type: 'products',
          title: 'Products Section',
          enabled: true,
          order: 3,
          content: {
            title: 'Our Template Collection',
            subtitle: 'Choose from our premium selection',
            showAllProducts: true,
            featuredProductIds: [],
            layout: 'grid',
            columns: 3,
            showPrices: true,
            showDescriptions: true
          }
        },
        {
          id: 'cta-1',
          type: 'cta',
          title: 'Call to Action',
          enabled: true,
          order: 4,
          content: {
            title: 'Ready to Get Started?',
            description: 'Join thousands of developers who are building amazing apps with our templates.',
            ctaText: 'Get Started Now',
            ctaLink: '#products',
            backgroundColor: '#6366f1',
            textColor: '#ffffff',
            showSecondaryButton: true,
            secondaryButtonText: 'Learn More',
            secondaryButtonLink: '#features'
          }
        }
      ]),
      seo_settings: JSON.stringify({
        title: 'Productivity App Templates & UI/UX Design Systems',
        description: 'Premium collection of productivity app templates, UI components, and design systems. Build stunning apps with our modern, responsive templates.',
        keywords: 'productivity app templates, ui design system, app ui kit, dashboard templates, react components, design system, ui components',
        ogTitle: 'Productivity App Templates & UI/UX Design Systems',
        ogDescription: 'Premium collection of productivity app templates, UI components, and design systems. Build stunning apps with our modern, responsive templates.',
        ogImage: '',
        twitterTitle: 'Productivity App Templates & Design Systems',
        twitterDescription: 'Premium collection of productivity app templates and UI components for modern applications.',
        twitterImage: ''
      }),
      theme_settings: JSON.stringify({
        primaryColor: '#6366f1',
        secondaryColor: '#4f46e5',
        accentColor: '#8b5cf6',
        backgroundColor: '#ffffff',
        textColor: '#1e293b',
        fontFamily: 'Inter, system-ui, sans-serif',
        borderRadius: '8px',
        spacing: '1rem'
      }),
      version: 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const insertStmt = db.prepare(`
      INSERT INTO homepage_config (
        sections_data, seo_settings, theme_settings, version, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?)
    `);

    insertStmt.run(
      defaultHomepageConfig.sections_data,
      defaultHomepageConfig.seo_settings,
      defaultHomepageConfig.theme_settings,
      defaultHomepageConfig.version,
      defaultHomepageConfig.created_at,
      defaultHomepageConfig.updated_at
    );

    console.log('✅ Created default homepage configuration');
    
    // Verify creation
    const newConfigs = db.prepare('SELECT * FROM homepage_config').all();
    console.log('📊 Homepage configurations in database:', newConfigs.length);
    
    if (newConfigs.length > 0) {
      const config = newConfigs[0];
      const sections = JSON.parse(config.sections_data);
      console.log('📄 Sections created:', sections.length);
      sections.forEach((section, index) => {
        console.log(`  ${index + 1}. ${section.type} - ${section.title}`);
      });
    }
  } else {
    console.log('✅ Homepage configuration already exists');
    const config = existingConfigs[0];
    const sections = JSON.parse(config.sections_data);
    console.log('📄 Existing sections:', sections.length);
    sections.forEach((section, index) => {
      console.log(`  ${index + 1}. ${section.type} - ${section.title} (enabled: ${section.enabled})`);
    });
  }
} catch (error) {
  console.error('❌ Error initializing homepage config:', error);
}

db.close();
