import { db } from './db.js';
import { invoices } from '../shared/schema.js';
import { eq, and, or, like, desc, count } from 'drizzle-orm';

console.log('Testing Drizzle ORM queries...');

try {
  // Test 1: Simple select all
  console.log('\n1. Testing simple select all...');
  const allInvoices = await db.select().from(invoices).limit(5);
  console.log('✅ Simple select works:', allInvoices.length, 'records');

  // Test 2: Test with isTrialOrder filter using integer
  console.log('\n2. Testing isTrialOrder filter with integer...');
  const regularOrders = await db.select().from(invoices).where(eq(invoices.isTrialOrder, 0)).limit(5);
  console.log('✅ Regular orders filter works:', regularOrders.length, 'records');

  const trialOrders = await db.select().from(invoices).where(eq(invoices.isTrialOrder, 1)).limit(5);
  console.log('✅ Trial orders filter works:', trialOrders.length, 'records');

  // Test 3: Test count query
  console.log('\n3. Testing count query...');
  const totalCount = await db.select({ count: count() }).from(invoices);
  console.log('✅ Count query works:', totalCount[0]?.count);

  // Test 4: Test with search (like)
  console.log('\n4. Testing search with like...');
  const searchResults = await db.select().from(invoices)
    .where(like(invoices.customerName, '%Hassan%'))
    .limit(5);
  console.log('✅ Search query works:', searchResults.length, 'records');

  // Test 5: Test combined conditions (the problematic query)
  console.log('\n5. Testing combined conditions...');
  const whereConditions = [];
  whereConditions.push(eq(invoices.isTrialOrder, 0));
  
  const whereCondition = whereConditions.length === 1
    ? whereConditions[0]
    : and(...whereConditions);

  const combinedQuery = await db.select().from(invoices)
    .where(whereCondition)
    .orderBy(desc(invoices.createdAt))
    .limit(20)
    .offset(0);
  
  console.log('✅ Combined query works:', combinedQuery.length, 'records');

  // Test 6: Test the exact query from getInvoicesPaginated
  console.log('\n6. Testing exact getInvoicesPaginated query...');
  
  const page = 1;
  const limit = 20;
  const search = '';
  const orderType = 'regular';
  const offset = (page - 1) * limit;

  let whereConditions2 = [];

  // Filter by order type
  if (orderType === 'regular') {
    whereConditions2.push(eq(invoices.isTrialOrder, 0));
  } else if (orderType === 'trial') {
    whereConditions2.push(eq(invoices.isTrialOrder, 1));
  }

  // Add search conditions
  if (search.trim()) {
    const searchTerm = `%${search.trim()}%`;
    whereConditions2.push(
      or(
        like(invoices.customerName, searchTerm),
        like(invoices.customerEmail, searchTerm),
        like(invoices.status, searchTerm)
      )
    );
  }

  // Combine conditions
  const whereCondition2 = whereConditions2.length > 0
    ? whereConditions2.length === 1
      ? whereConditions2[0]
      : and(...whereConditions2)
    : undefined;

  // Get total count
  let totalQuery = db.select({ count: count() }).from(invoices);
  if (whereCondition2) {
    totalQuery = totalQuery.where(whereCondition2);
  }

  const totalResult = await totalQuery;
  const total = totalResult[0]?.count || 0;
  console.log('✅ Count query in exact method works:', total);

  // Get paginated invoices
  let invoicesQuery = db.select().from(invoices);

  if (whereCondition2) {
    invoicesQuery = invoicesQuery.where(whereCondition2);
  }

  invoicesQuery = invoicesQuery
    .orderBy(desc(invoices.createdAt))
    .limit(limit)
    .offset(offset);

  const paginatedInvoices = await invoicesQuery;
  console.log('✅ Exact getInvoicesPaginated query works:', paginatedInvoices.length, 'records');

  console.log('\n🎉 All tests passed! The issue might be elsewhere.');

} catch (error) {
  console.error('❌ Error in Drizzle test:', error);
}
