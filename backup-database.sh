#!/bin/bash

# Secure Database Backup Script for Production
# Creates encrypted timestamped backups of the SQLite database

BACKUP_DIR="/home/<USER>/htdocs/niraza.site/backups"
DB_PATH="/home/<USER>/htdocs/niraza.site/data.db"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/secure_backup_$TIMESTAMP.db.enc"
TEMP_BACKUP="$BACKUP_DIR/temp_backup_$TIMESTAMP.db"

echo "🔐 Starting secure database backup..."

# Create backup directory if it doesn't exist with secure permissions
mkdir -p $BACKUP_DIR
chmod 750 $BACKUP_DIR

# Check if database exists
if [ ! -f "$DB_PATH" ]; then
    echo "❌ Database file not found: $DB_PATH"
    exit 1
fi

# Check if Node.js is available for encryption
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Cannot create encrypted backup."
    exit 1
fi

# Create temporary backup
echo "📋 Creating temporary backup..."
cp "$DB_PATH" "$TEMP_BACKUP"

if [ $? -ne 0 ]; then
    echo "❌ Failed to create temporary backup!"
    exit 1
fi

# Use Node.js to create encrypted backup
echo "🔒 Encrypting backup..."
node -e "
const { createSecureBackup } = require('./server/config/database-security.ts');
const path = require('path');

async function backup() {
  try {
    const backupPath = await createSecureBackup('$TEMP_BACKUP', '$BACKUP_DIR');
    console.log('✅ Encrypted backup created:', backupPath);
  } catch (error) {
    console.error('❌ Encryption failed:', error.message);
    process.exit(1);
  }
}

backup();
"

# Remove temporary backup
rm -f "$TEMP_BACKUP"

if [ -f "$BACKUP_FILE" ]; then
    echo "✅ Secure backup created successfully: $BACKUP_FILE"

    # Set secure permissions on backup file
    chmod 600 "$BACKUP_FILE"

    # Get file size
    SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
    echo "📊 Backup size: $SIZE"

    # Keep only last 10 encrypted backups
    echo "🧹 Cleaning old backups (keeping last 10)..."
    cd $BACKUP_DIR
    ls -t secure_backup_*.db.enc | tail -n +11 | xargs -r rm

    echo "📋 Current encrypted backups:"
    ls -lah secure_backup_*.db.enc 2>/dev/null || echo "No encrypted backups found"

    # Also clean up any old unencrypted backups
    ls -t data_backup_*.db 2>/dev/null | xargs -r rm
    echo "🗑️ Removed any old unencrypted backups for security"

else
    echo "❌ Secure backup failed!"
    exit 1
fi

echo "🔐 Secure backup process completed!"
