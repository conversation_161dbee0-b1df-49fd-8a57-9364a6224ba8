CREATE TABLE `allowed_emails` (
	`id` integer PRIMARY KEY NOT NULL,
	`email` text NOT NULL,
	`notes` text,
	`last_subject` text,
	`smtp_provider` text,
	`last_updated` text,
	`created_at` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `allowed_emails_email_unique` ON `allowed_emails` (`email`);--> statement-breakpoint
CREATE TABLE `custom_checkout_pages` (
	`id` integer PRIMARY KEY NOT NULL,
	`title` text NOT NULL,
	`slug` text NOT NULL,
	`product_name` text NOT NULL,
	`product_description` text NOT NULL,
	`price` text NOT NULL,
	`image_url` text,
	`payment_method` text NOT NULL,
	`custom_payment_link_id` text,
	`paypal_button_id` text,
	`trial_custom_payment_link_id` text,
	`trial_paypal_button_id` text,
	`embed_code_id` text,
	`smtp_provider_id` text,
	`contact_email` text,
	`require_allowed_email` integer DEFAULT 0 NOT NULL,
	`is_trial_checkout` integer DEFAULT 0 NOT NULL,
	`confirmation_message` text,
	`header_title` text,
	`footer_text` text,
	`header_logo` text,
	`footer_logo` text,
	`theme_mode` text DEFAULT 'light' NOT NULL,
	`use_referrer_masking` integer DEFAULT 0 NOT NULL,
	`redirect_delay` integer DEFAULT 2000 NOT NULL,
	`expires_at` text,
	`active` integer DEFAULT 1 NOT NULL,
	`views` integer DEFAULT 0 NOT NULL,
	`conversions` integer DEFAULT 0 NOT NULL,
	`secondary_smtp_provider_id` text,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `custom_checkout_pages_slug_unique` ON `custom_checkout_pages` (`slug`);--> statement-breakpoint
CREATE TABLE `custom_invoices` (
	`id` integer PRIMARY KEY NOT NULL,
	`invoice_number` text NOT NULL,
	`customer_name` text NOT NULL,
	`customer_email` text NOT NULL,
	`amount` text NOT NULL,
	`currency` text DEFAULT 'USD' NOT NULL,
	`description` text NOT NULL,
	`paypal_button_id` integer NOT NULL,
	`status` text DEFAULT 'pending' NOT NULL,
	`due_date` text,
	`view_count` integer DEFAULT 0 NOT NULL,
	`paid_at` text,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `custom_invoices_invoice_number_unique` ON `custom_invoices` (`invoice_number`);--> statement-breakpoint
CREATE TABLE `custom_payment_links` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`payment_link` text NOT NULL,
	`button_text` text NOT NULL,
	`success_redirect_url` text,
	`active` integer DEFAULT 1 NOT NULL,
	`is_trial_link` integer DEFAULT 0 NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `devices` (
	`id` text PRIMARY KEY NOT NULL,
	`user_id` integer NOT NULL,
	`name` text NOT NULL,
	`ip` text NOT NULL,
	`user_agent` text NOT NULL,
	`last_login` text NOT NULL,
	`created_at` text NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `email_templates` (
	`id` integer PRIMARY KEY NOT NULL,
	`template_id` text NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`subject` text NOT NULL,
	`html_content` text NOT NULL,
	`text_content` text,
	`content` text,
	`category` text DEFAULT 'general' NOT NULL,
	`is_default` integer DEFAULT 0 NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `email_templates_template_id_unique` ON `email_templates` (`template_id`);--> statement-breakpoint
CREATE TABLE `general_settings` (
	`id` integer PRIMARY KEY NOT NULL,
	`site_name` text NOT NULL,
	`site_description` text NOT NULL,
	`logo_url` text,
	`favicon_url` text,
	`primary_color` text NOT NULL,
	`secondary_color` text NOT NULL,
	`footer_text` text NOT NULL,
	`enable_checkout` integer DEFAULT 1 NOT NULL,
	`enable_custom_checkout` integer DEFAULT 1 NOT NULL,
	`enable_test_mode` integer DEFAULT 0 NOT NULL,
	`default_test_customer_enabled` integer DEFAULT 0 NOT NULL,
	`default_test_customer_name` text,
	`default_test_customer_email` text,
	`email_domain_restriction_enabled` integer DEFAULT 0 NOT NULL,
	`email_domain_restriction_domains` text,
	`seo_privacy_settings` text,
	`telegram_bot_settings` text,
	`url_settings` text,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `homepage_config` (
	`id` integer PRIMARY KEY NOT NULL,
	`sections_data` text NOT NULL,
	`seo_settings` text NOT NULL,
	`theme_settings` text NOT NULL,
	`version` integer DEFAULT 1 NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `invoices` (
	`id` integer PRIMARY KEY NOT NULL,
	`customer_name` text NOT NULL,
	`customer_email` text NOT NULL,
	`product_id` integer NOT NULL,
	`amount` text NOT NULL,
	`status` text DEFAULT 'pending' NOT NULL,
	`paypal_invoice_id` text,
	`paypal_invoice_url` text,
	`is_trial_order` integer DEFAULT 0 NOT NULL,
	`has_upgraded` integer DEFAULT 0 NOT NULL,
	`upgraded_at` text,
	`created_at` text NOT NULL,
	`custom_checkout_page_id` integer,
	`country` text,
	`notes` text,
	`app_type` text,
	`mac_address` text
);
--> statement-breakpoint
CREATE TABLE `paypal_buttons` (
	`id` integer PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`button_code` text NOT NULL,
	`description` text,
	`active` integer DEFAULT 1 NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `products` (
	`id` integer PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`description` text NOT NULL,
	`price` text NOT NULL,
	`image_url` text NOT NULL,
	`active` integer DEFAULT 1 NOT NULL,
	`payment_method` text DEFAULT 'manual-payment' NOT NULL,
	`custom_payment_link_id` text,
	`trial_custom_payment_link_id` text,
	`embed_code_id` text
);
--> statement-breakpoint
CREATE TABLE `recovery_codes` (
	`id` integer PRIMARY KEY NOT NULL,
	`user_id` integer NOT NULL,
	`code` text NOT NULL,
	`used` integer DEFAULT false,
	`created_at` text NOT NULL,
	`used_at` text,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `smtp_providers` (
	`id` text PRIMARY KEY NOT NULL,
	`name` text NOT NULL,
	`host` text NOT NULL,
	`port` text NOT NULL,
	`secure` integer DEFAULT 0 NOT NULL,
	`auth_user` text NOT NULL,
	`auth_pass` text NOT NULL,
	`from_email` text NOT NULL,
	`from_name` text NOT NULL,
	`admin_email` text,
	`active` integer DEFAULT 1 NOT NULL,
	`is_default` integer DEFAULT 0 NOT NULL,
	`is_backup` integer DEFAULT 0 NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL
);
--> statement-breakpoint
CREATE TABLE `system_messages` (
	`id` integer PRIMARY KEY NOT NULL,
	`message_id` text NOT NULL,
	`category` text NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`content` text NOT NULL,
	`is_html` integer DEFAULT 0 NOT NULL,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `system_messages_message_id_unique` ON `system_messages` (`message_id`);--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY NOT NULL,
	`username` text NOT NULL,
	`password` text NOT NULL,
	`two_factor_secret` text,
	`two_factor_enabled` integer DEFAULT false,
	`created_at` text NOT NULL,
	`updated_at` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `users_username_unique` ON `users` (`username`);