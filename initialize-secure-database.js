#!/usr/bin/env node

/**
 * Initialize Secure Database Script
 * Sets up the database with proper security configurations
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔐 Initializing secure database...\n');

// Step 1: Create secure directories
function createSecureDirectories() {
  console.log('📁 Creating secure directories...');
  
  const directories = [
    { path: './backups', mode: 0o750, description: 'Backup directory' },
    { path: './logs', mode: 0o750, description: 'Logs directory' },
    { path: './uploads', mode: 0o755, description: 'Uploads directory' }
  ];
  
  directories.forEach(({ path: dirPath, mode, description }) => {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true, mode });
        console.log(`✅ Created ${description}: ${dirPath} (${mode.toString(8)})`);
      } else {
        fs.chmodSync(dirPath, mode);
        console.log(`✅ Secured ${description}: ${dirPath} (${mode.toString(8)})`);
      }
    } catch (error) {
      console.warn(`⚠️ Could not create/secure ${description}:`, error.message);
    }
  });
}

// Step 2: Generate encryption key
async function generateEncryptionKey() {
  console.log('\n🔑 Setting up encryption key...');

  const keyPath = './.db-key';

  try {
    if (!fs.existsSync(keyPath)) {
      const crypto = await import('crypto');
      const key = crypto.randomBytes(32).toString('hex');

      fs.writeFileSync(keyPath, key, { mode: 0o600 });
      console.log('✅ Generated new encryption key');
    } else {
      // Ensure existing key has correct permissions
      fs.chmodSync(keyPath, 0o600);
      console.log('✅ Encryption key already exists and secured');
    }
  } catch (error) {
    console.error('❌ Failed to setup encryption key:', error.message);
    return false;
  }

  return true;
}

// Step 3: Initialize database with security
async function initializeDatabase() {
  console.log('\n🗄️ Initializing secure database...');
  
  try {
    // Import database module
    const Database = (await import('better-sqlite3')).default;
    
    const dbPath = './data.db';
    const db = new Database(dbPath);
    
    // Set secure SQLite pragmas
    db.pragma('journal_mode = WAL');
    db.pragma('foreign_keys = ON');
    db.pragma('secure_delete = ON');
    db.pragma('auto_vacuum = FULL');
    
    // Create a simple test table to ensure database is working
    db.exec(`
      CREATE TABLE IF NOT EXISTS security_test (
        id INTEGER PRIMARY KEY,
        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // Insert test record
    const stmt = db.prepare('INSERT INTO security_test (id) VALUES (1)');
    stmt.run();
    
    // Verify test record
    const result = db.prepare('SELECT COUNT(*) as count FROM security_test').get();
    
    db.close();
    
    if (result.count > 0) {
      console.log('✅ Database initialized successfully');
      
      // Set secure file permissions
      fs.chmodSync(dbPath, 0o600);
      console.log('✅ Set secure database file permissions (600)');
      
      return true;
    } else {
      console.error('❌ Database initialization verification failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Failed to initialize database:', error.message);
    return false;
  }
}

// Step 4: Create security configuration
function createSecurityConfig() {
  console.log('\n⚙️ Creating security configuration...');
  
  const securityConfig = {
    database: {
      encryption: true,
      backupEncryption: true,
      queryLogging: false,
      accessControl: true
    },
    passwords: {
      bcryptRounds: 12,
      minLength: 8,
      requireStrong: true
    },
    monitoring: {
      securityLogging: true,
      failedLoginTracking: true,
      maxLoginAttempts: 5,
      lockoutDuration: 300
    },
    backup: {
      enabled: true,
      interval: 24,
      retention: 7,
      encrypted: true
    }
  };
  
  try {
    const configPath = './security-config.json';
    fs.writeFileSync(configPath, JSON.stringify(securityConfig, null, 2), { mode: 0o640 });
    console.log('✅ Created security configuration file');
    return true;
  } catch (error) {
    console.error('❌ Failed to create security configuration:', error.message);
    return false;
  }
}

// Step 5: Verify security setup
function verifySecuritySetup() {
  console.log('\n🔍 Verifying security setup...');
  
  const checks = [
    {
      name: 'Database file permissions',
      check: () => {
        if (fs.existsSync('./data.db')) {
          const stats = fs.statSync('./data.db');
          const permissions = (stats.mode & parseInt('777', 8)).toString(8);
          return permissions === '600';
        }
        return true; // OK if file doesn't exist yet
      }
    },
    {
      name: 'Encryption key permissions',
      check: () => {
        if (fs.existsSync('./.db-key')) {
          const stats = fs.statSync('./.db-key');
          const permissions = (stats.mode & parseInt('777', 8)).toString(8);
          return permissions === '600';
        }
        return false;
      }
    },
    {
      name: 'Backup directory permissions',
      check: () => {
        if (fs.existsSync('./backups')) {
          const stats = fs.statSync('./backups');
          const permissions = (stats.mode & parseInt('777', 8)).toString(8);
          return permissions === '750';
        }
        return false;
      }
    },
    {
      name: 'Logs directory permissions',
      check: () => {
        if (fs.existsSync('./logs')) {
          const stats = fs.statSync('./logs');
          const permissions = (stats.mode & parseInt('777', 8)).toString(8);
          return permissions === '750';
        }
        return false;
      }
    }
  ];
  
  let passed = 0;
  let failed = 0;
  
  checks.forEach(({ name, check }) => {
    try {
      if (check()) {
        console.log(`✅ ${name}`);
        passed++;
      } else {
        console.log(`❌ ${name}`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${name} (error: ${error.message})`);
      failed++;
    }
  });
  
  return { passed, failed };
}

// Main initialization function
async function main() {
  console.log('🔐 SQLite Database Security Initialization');
  console.log('==========================================\n');
  
  let success = true;
  
  // Run initialization steps
  createSecureDirectories();
  
  if (!await generateEncryptionKey()) {
    success = false;
  }
  
  if (!await initializeDatabase()) {
    success = false;
  }
  
  if (!createSecurityConfig()) {
    success = false;
  }
  
  // Verify setup
  const { passed, failed } = verifySecuritySetup();
  
  console.log('\n' + '='.repeat(50));
  console.log('🔐 Security Initialization Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);
  
  if (success && failed === 0) {
    console.log('\n🎉 Database security initialization completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Review the .env file and update security settings');
    console.log('2. Run: npm run dev (to start the application)');
    console.log('3. Run: node test-security.js (to verify security)');
    console.log('4. Set up automated backups if needed');
    
    process.exit(0);
  } else {
    console.log('\n⚠️ Some security initialization steps failed.');
    console.log('Please review the errors above and fix them manually.');
    process.exit(1);
  }
}

// Run initialization
main().catch(error => {
  console.error('❌ Initialization failed:', error);
  process.exit(1);
});
