import Database from 'better-sqlite3';
import SQLCipher from '@journeyapps/sqlcipher';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { securityMonitor, SecurityEventType, SecuritySeverity } from './security-monitoring';
import { DatabaseSecurityManager } from './database-security-manager';
import { initializeConnectionManager, getConnectionManager } from './connection-manager';

// Database security configuration
interface DatabaseSecurityConfig {
  encryptionKey: string;
  databasePath: string;
  backupPath: string;
  enableQueryLogging: boolean;
  enableAccessControl: boolean;
  maxConnections: number;
}

// Generate or retrieve encryption key
function getEncryptionKey(): string {
  const keyPath = path.join(process.cwd(), '.db-key');
  
  // Check if key file exists
  if (fs.existsSync(keyPath)) {
    try {
      const key = fs.readFileSync(keyPath, 'utf8').trim();
      if (key.length === 64) { // 32 bytes hex encoded
        return key;
      }
    } catch (error) {
      console.warn('Failed to read existing encryption key, generating new one');
    }
  }
  
  // Generate new encryption key
  const key = crypto.randomBytes(32).toString('hex');
  
  try {
    // Save key to file with restricted permissions
    fs.writeFileSync(keyPath, key, { mode: 0o600 });
    console.log('🔐 Generated new database encryption key');
  } catch (error) {
    console.error('Failed to save encryption key:', error);
    throw new Error('Cannot secure database without encryption key');
  }
  
  return key;
}

// Global security manager instance
let securityManager: DatabaseSecurityManager | null = null;

// Create secure database connection with connection pooling and SQLCipher support
export function createSecureDatabase(): Database.Database | SQLCipher.Database {
  const config: DatabaseSecurityConfig = {
    encryptionKey: getEncryptionKey(),
    databasePath: process.env.DATABASE_PATH || 'data.db',
    backupPath: process.env.BACKUP_PATH || './backups',
    enableQueryLogging: process.env.ENABLE_QUERY_LOGGING === 'true',
    enableAccessControl: process.env.ENABLE_ACCESS_CONTROL === 'true',
    maxConnections: parseInt(process.env.MAX_DB_CONNECTIONS || '10')
  };

  // Initialize security manager
  securityManager = new DatabaseSecurityManager(config.databasePath, config.backupPath);

  // Create database directory if it doesn't exist
  const dbDir = path.dirname(config.databasePath);
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true, mode: 0o750 });
  }

  // Check if we should use SQLCipher or better-sqlite3
  const useSQLCipher = process.env.USE_SQLCIPHER !== 'false';

  // Initialize connection manager
  const connectionManager = initializeConnectionManager(
    config.databasePath,
    config.encryptionKey,
    useSQLCipher
  );

  // For backward compatibility, return a single connection
  // In practice, you should use connectionManager.executeQuery() for pooled connections
  let db: Database.Database | SQLCipher.Database;

  if (useSQLCipher) {
    console.log('🔐 Initializing SQLCipher database...');

    // Initialize SQLCipher database
    db = new SQLCipher(config.databasePath);

    // Set encryption key
    (db as SQLCipher.Database).pragma(`key = '${config.encryptionKey}'`);

    // Set secure SQLCipher pragmas
    (db as SQLCipher.Database).pragma('cipher_page_size = 4096');
    (db as SQLCipher.Database).pragma('kdf_iter = 256000');
    (db as SQLCipher.Database).pragma('cipher_hmac_algorithm = HMAC_SHA512');
    (db as SQLCipher.Database).pragma('cipher_kdf_algorithm = PBKDF2_HMAC_SHA512');

    // Enable WAL mode for better concurrency and security
    (db as SQLCipher.Database).pragma('journal_mode = WAL');

    // Set secure SQLite pragmas
    (db as SQLCipher.Database).pragma('foreign_keys = ON');
    (db as SQLCipher.Database).pragma('secure_delete = ON');
    (db as SQLCipher.Database).pragma('auto_vacuum = FULL');

    console.log('✅ SQLCipher database initialized with encryption');
  } else {
    console.log('🔓 Initializing better-sqlite3 database...');

    // Initialize better-sqlite3 database
    db = new Database(config.databasePath, {
      verbose: config.enableQueryLogging ? console.log : undefined,
      fileMustExist: false
    });

    // Enable WAL mode for better concurrency and security
    (db as Database.Database).pragma('journal_mode = WAL');

    // Set secure SQLite pragmas
    (db as Database.Database).pragma('foreign_keys = ON');
    (db as Database.Database).pragma('secure_delete = ON');
    (db as Database.Database).pragma('auto_vacuum = FULL');
  }

  // Set file permissions for the database file
  try {
    // Ensure database file exists by running a simple query
    db.pragma('user_version');

    // Now set secure permissions
    if (fs.existsSync(config.databasePath)) {
      fs.chmodSync(config.databasePath, 0o600); // Owner read/write only
      console.log('🔒 Set secure database file permissions (600)');
    }
  } catch (error) {
    console.warn('Could not set database file permissions:', error);
  }

  // Enhanced query logging with security monitoring
  if (config.enableQueryLogging) {
    const originalPrepare = db.prepare.bind(db);
    db.prepare = function(sql: string) {
      const startTime = Date.now();

      try {
        const statement = originalPrepare(sql);
        const executionTime = Date.now() - startTime;

        // Log successful query
        securityMonitor.logDatabaseQuery(sql, {
          userId: undefined,
          username: 'system',
          role: 'admin' as any,
          permissions: [],
          ipAddress: 'localhost'
        }, executionTime);

        return statement;
      } catch (error) {
        const executionTime = Date.now() - startTime;

        // Log failed query
        securityMonitor.logDatabaseQuery(sql, {
          userId: undefined,
          username: 'system',
          role: 'admin' as any,
          permissions: [],
          ipAddress: 'localhost'
        }, executionTime, error as Error);

        throw error;
      }
    };
  }

  // Schedule automatic backups if enabled
  if (process.env.BACKUP_ENABLED === 'true') {
    const backupInterval = parseInt(process.env.BACKUP_INTERVAL || '24');
    securityManager.scheduleBackups(backupInterval);
    console.log(`📅 Scheduled automatic backups every ${backupInterval} hours`);
  }

  console.log('🔒 Secure database connection established with connection pooling and monitoring');
  return db;
}

// Get security manager instance
export function getSecurityManager(): DatabaseSecurityManager | null {
  return securityManager;
}

// Encrypt sensitive data before storing
export function encryptSensitiveData(data: string, key?: string): string {
  const encryptionKey = key || getEncryptionKey();
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
  
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return iv.toString('hex') + ':' + encrypted;
}

// Decrypt sensitive data after retrieving
export function decryptSensitiveData(encryptedData: string, key?: string): string {
  const encryptionKey = key || getEncryptionKey();
  const parts = encryptedData.split(':');
  
  if (parts.length !== 2) {
    throw new Error('Invalid encrypted data format');
  }
  
  const iv = Buffer.from(parts[0], 'hex');
  const encrypted = parts[1];
  
  const decipher = crypto.createDecipher('aes-256-cbc', encryptionKey);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}

// Secure backup function with encryption
export async function createSecureBackup(sourcePath: string, backupDir: string): Promise<string> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFileName = `secure_backup_${timestamp}.db.enc`;
  const backupPath = path.join(backupDir, backupFileName);
  
  // Ensure backup directory exists
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true, mode: 0o750 });
  }
  
  // Read source database
  const sourceData = fs.readFileSync(sourcePath);
  
  // Encrypt backup data
  const encryptionKey = getEncryptionKey();
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
  
  const encryptedData = Buffer.concat([
    iv,
    cipher.update(sourceData),
    cipher.final()
  ]);
  
  // Write encrypted backup
  fs.writeFileSync(backupPath, encryptedData, { mode: 0o600 });
  
  console.log(`🔐 Secure backup created: ${backupPath}`);
  return backupPath;
}

// Restore from secure backup
export async function restoreFromSecureBackup(backupPath: string, targetPath: string): Promise<void> {
  const encryptionKey = getEncryptionKey();
  const encryptedData = fs.readFileSync(backupPath);
  
  // Extract IV and encrypted data
  const iv = encryptedData.slice(0, 16);
  const encrypted = encryptedData.slice(16);
  
  // Decrypt data
  const decipher = crypto.createDecipher('aes-256-cbc', encryptionKey);
  const decryptedData = Buffer.concat([
    decipher.update(encrypted),
    decipher.final()
  ]);
  
  // Write restored database
  fs.writeFileSync(targetPath, decryptedData, { mode: 0o600 });
  
  console.log(`🔓 Database restored from secure backup: ${targetPath}`);
}
