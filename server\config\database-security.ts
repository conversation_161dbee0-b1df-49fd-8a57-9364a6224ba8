import Database from 'better-sqlite3';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';

// Database security configuration
interface DatabaseSecurityConfig {
  encryptionKey: string;
  databasePath: string;
  backupPath: string;
  enableQueryLogging: boolean;
  enableAccessControl: boolean;
  maxConnections: number;
}

// Generate or retrieve encryption key
function getEncryptionKey(): string {
  const keyPath = path.join(process.cwd(), '.db-key');
  
  // Check if key file exists
  if (fs.existsSync(keyPath)) {
    try {
      const key = fs.readFileSync(keyPath, 'utf8').trim();
      if (key.length === 64) { // 32 bytes hex encoded
        return key;
      }
    } catch (error) {
      console.warn('Failed to read existing encryption key, generating new one');
    }
  }
  
  // Generate new encryption key
  const key = crypto.randomBytes(32).toString('hex');
  
  try {
    // Save key to file with restricted permissions
    fs.writeFileSync(keyPath, key, { mode: 0o600 });
    console.log('🔐 Generated new database encryption key');
  } catch (error) {
    console.error('Failed to save encryption key:', error);
    throw new Error('Cannot secure database without encryption key');
  }
  
  return key;
}

// Create secure database connection
export function createSecureDatabase(): Database.Database {
  const config: DatabaseSecurityConfig = {
    encryptionKey: getEncryptionKey(),
    databasePath: process.env.DATABASE_PATH || 'data.db',
    backupPath: process.env.BACKUP_PATH || './backups',
    enableQueryLogging: process.env.ENABLE_QUERY_LOGGING === 'true',
    enableAccessControl: process.env.ENABLE_ACCESS_CONTROL === 'true',
    maxConnections: parseInt(process.env.MAX_DB_CONNECTIONS || '10')
  };

  // Create database directory if it doesn't exist
  const dbDir = path.dirname(config.databasePath);
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true, mode: 0o750 });
  }

  // Initialize database with security options
  const db = new Database(config.databasePath, {
    verbose: config.enableQueryLogging ? console.log : undefined,
    fileMustExist: false
  });

  // Enable WAL mode for better concurrency and security
  db.pragma('journal_mode = WAL');
  
  // Set secure SQLite pragmas
  db.pragma('foreign_keys = ON');
  db.pragma('secure_delete = ON');
  db.pragma('auto_vacuum = FULL');
  
  // Set encryption key (this is a placeholder - actual SQLCipher integration would be different)
  // For now, we'll implement application-level encryption for sensitive data
  
  // Set file permissions for the database file
  try {
    // Ensure database file exists by running a simple query
    db.pragma('user_version');

    // Now set secure permissions
    if (fs.existsSync(config.databasePath)) {
      fs.chmodSync(config.databasePath, 0o600); // Owner read/write only
      console.log('🔒 Set secure database file permissions (600)');
    }
  } catch (error) {
    console.warn('Could not set database file permissions:', error);
  }

  // Add query logging if enabled
  if (config.enableQueryLogging) {
    const originalPrepare = db.prepare.bind(db);
    db.prepare = function(sql: string) {
      console.log(`[DB Query] ${new Date().toISOString()}: ${sql}`);
      return originalPrepare(sql);
    };
  }

  console.log('🔒 Secure database connection established');
  return db;
}

// Encrypt sensitive data before storing
export function encryptSensitiveData(data: string, key?: string): string {
  const encryptionKey = key || getEncryptionKey();
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
  
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return iv.toString('hex') + ':' + encrypted;
}

// Decrypt sensitive data after retrieving
export function decryptSensitiveData(encryptedData: string, key?: string): string {
  const encryptionKey = key || getEncryptionKey();
  const parts = encryptedData.split(':');
  
  if (parts.length !== 2) {
    throw new Error('Invalid encrypted data format');
  }
  
  const iv = Buffer.from(parts[0], 'hex');
  const encrypted = parts[1];
  
  const decipher = crypto.createDecipher('aes-256-cbc', encryptionKey);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}

// Secure backup function with encryption
export async function createSecureBackup(sourcePath: string, backupDir: string): Promise<string> {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFileName = `secure_backup_${timestamp}.db.enc`;
  const backupPath = path.join(backupDir, backupFileName);
  
  // Ensure backup directory exists
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true, mode: 0o750 });
  }
  
  // Read source database
  const sourceData = fs.readFileSync(sourcePath);
  
  // Encrypt backup data
  const encryptionKey = getEncryptionKey();
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
  
  const encryptedData = Buffer.concat([
    iv,
    cipher.update(sourceData),
    cipher.final()
  ]);
  
  // Write encrypted backup
  fs.writeFileSync(backupPath, encryptedData, { mode: 0o600 });
  
  console.log(`🔐 Secure backup created: ${backupPath}`);
  return backupPath;
}

// Restore from secure backup
export async function restoreFromSecureBackup(backupPath: string, targetPath: string): Promise<void> {
  const encryptionKey = getEncryptionKey();
  const encryptedData = fs.readFileSync(backupPath);
  
  // Extract IV and encrypted data
  const iv = encryptedData.slice(0, 16);
  const encrypted = encryptedData.slice(16);
  
  // Decrypt data
  const decipher = crypto.createDecipher('aes-256-cbc', encryptionKey);
  const decryptedData = Buffer.concat([
    decipher.update(encrypted),
    decipher.final()
  ]);
  
  // Write restored database
  fs.writeFileSync(targetPath, decryptedData, { mode: 0o600 });
  
  console.log(`🔓 Database restored from secure backup: ${targetPath}`);
}
