#!/usr/bin/env node

/**
 * Security Structure Validation Script
 * 
 * This script validates that the project structure follows security best practices:
 * - Database files are in secure locations
 * - Sensitive configuration is protected
 * - Upload directories are properly secured
 * - Public directory only contains safe assets
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${description}: ${filePath}`, colors.green);
    return true;
  } else {
    log(`❌ ${description} NOT FOUND: ${filePath}`, colors.red);
    return false;
  }
}

function checkFileNotExists(filePath, description) {
  if (!fs.existsSync(filePath)) {
    log(`✅ ${description} properly secured (not in public): ${filePath}`, colors.green);
    return true;
  } else {
    log(`❌ SECURITY RISK - ${description} exposed: ${filePath}`, colors.red);
    return false;
  }
}

function checkDirectoryExists(dirPath, description) {
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    log(`✅ ${description}: ${dirPath}`, colors.green);
    return true;
  } else {
    log(`❌ ${description} NOT FOUND: ${dirPath}`, colors.red);
    return false;
  }
}

function validateSecurityStructure() {
  log(`${colors.bold}🔒 Security Structure Validation${colors.reset}\n`);
  
  let passed = 0;
  let failed = 0;
  
  const rootDir = process.cwd();
  
  // Check secure app directory structure
  log(`${colors.blue}📁 Checking Secure Directory Structure:${colors.reset}`);
  
  const secureChecks = [
    () => checkDirectoryExists(path.join(rootDir, 'app'), 'Secure app directory'),
    () => checkDirectoryExists(path.join(rootDir, 'app', 'data'), 'Secure data directory'),
    () => checkDirectoryExists(path.join(rootDir, 'app', 'config'), 'Secure config directory'),
    () => checkDirectoryExists(path.join(rootDir, 'app', 'storage'), 'Secure storage directory'),
    () => checkDirectoryExists(path.join(rootDir, 'app', 'logs'), 'Secure logs directory'),
    () => checkFileExists(path.join(rootDir, 'app', '.htaccess'), 'App directory protection'),
  ];
  
  secureChecks.forEach(check => {
    if (check()) passed++;
    else failed++;
  });
  
  // Check database security
  log(`\n${colors.blue}🗄️ Checking Database Security:${colors.reset}`);
  
  const dbChecks = [
    () => checkFileExists(path.join(rootDir, 'app', 'data', 'data.db'), 'Database in secure location'),
    () => checkFileNotExists(path.join(rootDir, 'data.db'), 'Database not in root'),
    () => checkFileNotExists(path.join(rootDir, 'public-secure', 'data.db'), 'Database not in public'),
  ];
  
  dbChecks.forEach(check => {
    if (check()) passed++;
    else failed++;
  });
  
  // Check configuration security
  log(`\n${colors.blue}⚙️ Checking Configuration Security:${colors.reset}`);
  
  const configChecks = [
    () => checkFileExists(path.join(rootDir, 'app', 'config', 'security-config.json'), 'Security config in secure location'),
    () => checkFileNotExists(path.join(rootDir, 'security-config.json'), 'Security config not in root'),
    () => checkFileNotExists(path.join(rootDir, 'public-secure', 'security-config.json'), 'Security config not in public'),
  ];
  
  configChecks.forEach(check => {
    if (check()) passed++;
    else failed++;
  });
  
  // Check upload security
  log(`\n${colors.blue}📤 Checking Upload Security:${colors.reset}`);
  
  const uploadChecks = [
    () => checkDirectoryExists(path.join(rootDir, 'app', 'storage', 'uploads'), 'Uploads in secure location'),
    () => checkFileNotExists(path.join(rootDir, 'uploads'), 'Uploads not in root'),
    () => checkFileNotExists(path.join(rootDir, 'public-secure', 'uploads'), 'Uploads not in public'),
  ];
  
  uploadChecks.forEach(check => {
    if (check()) passed++;
    else failed++;
  });
  
  // Check public directory safety
  log(`\n${colors.blue}🌐 Checking Public Directory Safety:${colors.reset}`);
  
  const publicChecks = [
    () => checkDirectoryExists(path.join(rootDir, 'public-secure'), 'Secure public directory'),
    () => checkFileExists(path.join(rootDir, 'public-secure', 'index.html'), 'Public index.html'),
    () => checkFileNotExists(path.join(rootDir, 'public-secure', 'data.db'), 'No database in public'),
    () => checkFileNotExists(path.join(rootDir, 'public-secure', 'server'), 'No server code in public'),
  ];
  
  publicChecks.forEach(check => {
    if (check()) passed++;
    else failed++;
  });
  
  // Check for development files in public
  log(`\n${colors.blue}🧹 Checking for Development Files in Public:${colors.reset}`);
  
  const devFileChecks = [
    () => checkFileNotExists(path.join(rootDir, 'public-secure', 'package.json'), 'No package.json in public'),
    () => checkFileNotExists(path.join(rootDir, 'public-secure', 'tsconfig.json'), 'No tsconfig.json in public'),
    () => checkFileNotExists(path.join(rootDir, 'public-secure', '.env'), 'No .env in public'),
    () => checkFileNotExists(path.join(rootDir, 'public-secure', 'node_modules'), 'No node_modules in public'),
  ];
  
  devFileChecks.forEach(check => {
    if (check()) passed++;
    else failed++;
  });
  
  // Summary
  log(`\n${colors.bold}📊 Validation Summary:${colors.reset}`);
  log(`✅ Passed: ${passed}`, colors.green);
  log(`❌ Failed: ${failed}`, failed > 0 ? colors.red : colors.green);
  
  const total = passed + failed;
  const percentage = Math.round((passed / total) * 100);
  
  if (failed === 0) {
    log(`\n🎉 All security checks passed! (${percentage}%)`, colors.green);
    log(`🔒 Your project structure follows security best practices.`, colors.green);
    return true;
  } else {
    log(`\n⚠️ Security issues found! (${percentage}% passed)`, colors.yellow);
    log(`🔧 Please address the failed checks above.`, colors.yellow);
    return false;
  }
}

// Run validation
if (require.main === module) {
  const isSecure = validateSecurityStructure();
  process.exit(isSecure ? 0 : 1);
}

module.exports = { validateSecurityStructure };
