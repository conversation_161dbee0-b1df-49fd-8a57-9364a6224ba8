import { db } from '../db';
import { sql } from 'drizzle-orm';

export async function addMissingInvoiceFields() {
  console.log('Adding missing fields (notes, appType, macAddress) to invoices table...');

  try {
    // Add notes column
    await db.execute(sql`
      ALTER TABLE invoices
      ADD COLUMN notes TEXT
    `);
    console.log('Added notes column to invoices table');

    // Add appType column
    await db.execute(sql`
      ALTER TABLE invoices
      ADD COLUMN app_type TEXT
    `);
    console.log('Added app_type column to invoices table');

    // Add macAddress column
    await db.execute(sql`
      ALTER TABLE invoices
      ADD COLUMN mac_address TEXT
    `);
    console.log('Added mac_address column to invoices table');

    console.log('Successfully added missing fields to invoices table');
  } catch (error) {
    console.error('Error adding missing fields to invoices table:', error);
    throw error;
  }
}
