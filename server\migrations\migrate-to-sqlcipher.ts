import Database from 'better-sqlite3';
import SQLCipher from '@journeyapps/sqlcipher';
import * as fs from 'fs';
import * as path from 'path';
import { getEncryptionKey } from '../config/database-security';

/**
 * Migration script to convert from better-sqlite3 to SQLCipher
 * This provides true database-level encryption
 */

interface MigrationResult {
  success: boolean;
  originalSize: number;
  encryptedSize: number;
  tablesCount: number;
  recordsCount: number;
  errors: string[];
  backupPath?: string;
}

export async function migrateToSQLCipher(): Promise<MigrationResult> {
  const result: MigrationResult = {
    success: false,
    originalSize: 0,
    encryptedSize: 0,
    tablesCount: 0,
    recordsCount: 0,
    errors: []
  };

  console.log('🔐 Starting migration from better-sqlite3 to SQLCipher...');

  const originalDbPath = './data.db';
  const encryptedDbPath = './data-encrypted.db';
  const backupDbPath = `./backups/data-backup-${Date.now()}.db`;

  try {
    // Step 1: Verify original database exists
    if (!fs.existsSync(originalDbPath)) {
      throw new Error(`Original database not found: ${originalDbPath}`);
    }

    result.originalSize = fs.statSync(originalDbPath).size;
    console.log(`📊 Original database size: ${(result.originalSize / 1024).toFixed(2)} KB`);

    // Step 2: Create backup
    console.log('📋 Creating backup of original database...');
    if (!fs.existsSync('./backups')) {
      fs.mkdirSync('./backups', { recursive: true });
    }
    fs.copyFileSync(originalDbPath, backupDbPath);
    result.backupPath = backupDbPath;
    console.log(`✅ Backup created: ${backupDbPath}`);

    // Step 3: Open original database
    console.log('🔓 Opening original database...');
    const originalDb = new Database(originalDbPath, { readonly: true });

    // Step 4: Get encryption key
    const encryptionKey = getEncryptionKey();
    console.log('🔑 Retrieved encryption key');

    // Step 5: Create encrypted database
    console.log('🔐 Creating encrypted database...');
    const encryptedDb = new SQLCipher(encryptedDbPath);
    
    // Set encryption key
    encryptedDb.pragma(`key = '${encryptionKey}'`);
    
    // Set secure pragmas
    encryptedDb.pragma('cipher_page_size = 4096');
    encryptedDb.pragma('kdf_iter = 256000');
    encryptedDb.pragma('cipher_hmac_algorithm = HMAC_SHA512');
    encryptedDb.pragma('cipher_kdf_algorithm = PBKDF2_HMAC_SHA512');

    // Step 6: Get schema from original database
    console.log('📋 Extracting schema...');
    const tables = originalDb.prepare(`
      SELECT name, sql FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
    `).all();

    result.tablesCount = tables.length;
    console.log(`📊 Found ${result.tablesCount} tables to migrate`);

    // Step 7: Create tables in encrypted database
    console.log('🏗️ Creating tables in encrypted database...');
    for (const table of tables) {
      if (table.sql) {
        encryptedDb.exec(table.sql);
        console.log(`✅ Created table: ${table.name}`);
      }
    }

    // Step 8: Copy data
    console.log('📦 Copying data...');
    let totalRecords = 0;

    for (const table of tables) {
      try {
        // Get all data from original table
        const data = originalDb.prepare(`SELECT * FROM ${table.name}`).all();
        
        if (data.length > 0) {
          // Get column names
          const columns = Object.keys(data[0]);
          const placeholders = columns.map(() => '?').join(', ');
          const columnNames = columns.join(', ');
          
          // Prepare insert statement
          const insertStmt = encryptedDb.prepare(
            `INSERT INTO ${table.name} (${columnNames}) VALUES (${placeholders})`
          );

          // Insert data in batches
          const batchSize = 1000;
          for (let i = 0; i < data.length; i += batchSize) {
            const batch = data.slice(i, i + batchSize);
            const transaction = encryptedDb.transaction((records: any[]) => {
              for (const record of records) {
                insertStmt.run(...columns.map(col => record[col]));
              }
            });
            transaction(batch);
          }

          totalRecords += data.length;
          console.log(`✅ Copied ${data.length} records from ${table.name}`);
        } else {
          console.log(`⏭️ Table ${table.name} is empty`);
        }
      } catch (error) {
        const errorMsg = `Failed to copy table ${table.name}: ${error.message}`;
        console.error(`❌ ${errorMsg}`);
        result.errors.push(errorMsg);
      }
    }

    result.recordsCount = totalRecords;
    console.log(`📊 Total records copied: ${totalRecords}`);

    // Step 9: Copy indexes
    console.log('🔍 Copying indexes...');
    const indexes = originalDb.prepare(`
      SELECT name, sql FROM sqlite_master 
      WHERE type='index' AND name NOT LIKE 'sqlite_%'
    `).all();

    for (const index of indexes) {
      if (index.sql) {
        try {
          encryptedDb.exec(index.sql);
          console.log(`✅ Created index: ${index.name}`);
        } catch (error) {
          console.warn(`⚠️ Failed to create index ${index.name}: ${error.message}`);
        }
      }
    }

    // Step 10: Verify encrypted database
    console.log('🔍 Verifying encrypted database...');
    const verificationResult = await verifyEncryptedDatabase(encryptedDbPath, encryptionKey);
    
    if (!verificationResult.success) {
      result.errors.push(...verificationResult.errors);
      throw new Error('Database verification failed');
    }

    // Step 11: Close databases
    originalDb.close();
    encryptedDb.close();

    // Step 12: Get encrypted database size
    result.encryptedSize = fs.existsSync(encryptedDbPath) ? fs.statSync(encryptedDbPath).size : 0;
    console.log(`📊 Encrypted database size: ${(result.encryptedSize / 1024).toFixed(2)} KB`);

    // Step 13: Replace original database
    console.log('🔄 Replacing original database with encrypted version...');
    const originalBackupPath = `${originalDbPath}.original`;
    fs.renameSync(originalDbPath, originalBackupPath);
    fs.renameSync(encryptedDbPath, originalDbPath);

    console.log('✅ Migration completed successfully!');
    console.log(`📊 Migration Summary:`);
    console.log(`  - Tables migrated: ${result.tablesCount}`);
    console.log(`  - Records migrated: ${result.recordsCount}`);
    console.log(`  - Original size: ${(result.originalSize / 1024).toFixed(2)} KB`);
    console.log(`  - Encrypted size: ${(result.encryptedSize / 1024).toFixed(2)} KB`);
    console.log(`  - Backup location: ${backupDbPath}`);
    console.log(`  - Original backup: ${originalBackupPath}`);

    result.success = true;

  } catch (error) {
    const errorMsg = `Migration failed: ${error.message}`;
    console.error(`❌ ${errorMsg}`);
    result.errors.push(errorMsg);

    // Cleanup on failure
    if (fs.existsSync(encryptedDbPath)) {
      fs.unlinkSync(encryptedDbPath);
    }
  }

  return result;
}

// Verify encrypted database integrity
async function verifyEncryptedDatabase(dbPath: string, encryptionKey: string): Promise<{
  success: boolean;
  errors: string[];
}> {
  const result = { success: false, errors: [] };

  try {
    console.log('🔍 Verifying encrypted database integrity...');
    
    const db = new SQLCipher(dbPath);
    db.pragma(`key = '${encryptionKey}'`);

    // Test basic operations
    const tables = db.prepare(`
      SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'
    `).all();

    if (tables.length === 0) {
      result.errors.push('No tables found in encrypted database');
      return result;
    }

    console.log(`✅ Found ${tables.length} tables in encrypted database`);

    // Test reading from each table
    for (const table of tables) {
      try {
        const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
        console.log(`✅ Table ${table.name}: ${count.count} records`);
      } catch (error) {
        result.errors.push(`Failed to read from table ${table.name}: ${error.message}`);
      }
    }

    // Test integrity check
    const integrityCheck = db.pragma('integrity_check');
    if (integrityCheck.length === 1 && integrityCheck[0].integrity_check === 'ok') {
      console.log('✅ Database integrity check passed');
    } else {
      result.errors.push('Database integrity check failed');
    }

    db.close();

    result.success = result.errors.length === 0;

  } catch (error) {
    result.errors.push(`Verification failed: ${error.message}`);
  }

  return result;
}

// Rollback migration
export async function rollbackSQLCipherMigration(): Promise<boolean> {
  console.log('🔄 Rolling back SQLCipher migration...');

  try {
    const originalDbPath = './data.db';
    const originalBackupPath = `${originalDbPath}.original`;

    if (fs.existsSync(originalBackupPath)) {
      if (fs.existsSync(originalDbPath)) {
        fs.unlinkSync(originalDbPath);
      }
      fs.renameSync(originalBackupPath, originalDbPath);
      console.log('✅ Rollback completed successfully');
      return true;
    } else {
      console.error('❌ Original backup not found');
      return false;
    }
  } catch (error) {
    console.error(`❌ Rollback failed: ${error.message}`);
    return false;
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];

  if (command === 'migrate') {
    migrateToSQLCipher()
      .then(result => {
        process.exit(result.success ? 0 : 1);
      })
      .catch(error => {
        console.error('Migration failed:', error);
        process.exit(1);
      });
  } else if (command === 'rollback') {
    rollbackSQLCipherMigration()
      .then(success => {
        process.exit(success ? 0 : 1);
      })
      .catch(error => {
        console.error('Rollback failed:', error);
        process.exit(1);
      });
  } else {
    console.log('Usage:');
    console.log('  npm run migrate:sqlcipher migrate   - Migrate to SQLCipher');
    console.log('  npm run migrate:sqlcipher rollback  - Rollback migration');
    process.exit(1);
  }
}
