import { Request, Response, NextFunction } from 'express';
import {
  createSecurityContext,
  hasPermission,
  requirePermission,
  requireRole,
  UserRole,
  Permission,
  logSecurityEvent
} from '../config/access-control';

// Enhanced middleware to check if user is authenticated as admin
export function isAdmin(req: Request, res: Response, next: NextFunction) {
  const securityContext = createSecurityContext(req);

  // Check if session exists and user is admin
  if (req.session && req.session.isAdmin) {
    // Log session info for debugging
    console.log('Checking session:', {
      id: req.session.id,
      isAdmin: req.session.isAdmin,
      role: securityContext.role
    });

    // Add security context to request
    (req as any).securityContext = securityContext;

    // Log successful admin access
    logSecurityEvent(securityContext, 'ADMIN_ACCESS_GRANTED', {
      endpoint: req.path,
      method: req.method
    });

    // User is authenticated as admin, proceed
    next();
  } else {
    // Log failed admin access attempt
    logSecurityEvent(securityContext, 'ADMIN_ACCESS_DENIED', {
      endpoint: req.path,
      method: req.method,
      reason: 'Not authenticated as admin'
    });

    // User is not authenticated as admin
    res.status(401).json({ message: 'Unauthorized' });
  }
}

// Enhanced middleware to check for admin access token
export function checkAdminAccessToken(req: Request, res: Response, next: NextFunction) {
  const adminToken = process.env.ADMIN_ACCESS_TOKEN;
  const securityContext = createSecurityContext(req);

  // If no admin token is configured, skip token check
  if (!adminToken) {
    return next();
  }

  // Check for token in various places
  const tokenFromHeader = req.headers['x-admin-token'] as string;
  const tokenFromQuery = req.query.token as string;
  const tokenFromBody = req.body?.accessToken as string;

  const providedToken = tokenFromHeader || tokenFromQuery || tokenFromBody;

  // If token matches, create admin session
  if (providedToken && providedToken === adminToken) {
    console.log('Valid admin access token provided, creating admin session');

    // Create admin session with enhanced security
    req.session.isAdmin = true;
    req.session.username = 'admin';
    req.session.role = UserRole.ADMIN;
    req.session.rememberMe = false;
    req.session.requiresTwoFactor = false;
    req.session.twoFactorVerified = false;
    req.session.userId = 1; // Default admin user ID

    // Set cookie expiration
    req.session.cookie.maxAge = 24 * 60 * 60 * 1000; // 24 hours

    // Log successful token authentication
    logSecurityEvent(securityContext, 'ADMIN_TOKEN_AUTH_SUCCESS', {
      endpoint: req.path,
      method: req.method,
      tokenSource: tokenFromHeader ? 'header' : tokenFromQuery ? 'query' : 'body'
    });

    return next();
  }

  // Log failed token authentication if token was provided
  if (providedToken) {
    logSecurityEvent(securityContext, 'ADMIN_TOKEN_AUTH_FAILED', {
      endpoint: req.path,
      method: req.method,
      providedToken: providedToken.substring(0, 8) + '...' // Log only first 8 chars for security
    });
  }

  // No valid token provided, continue with normal authentication
  next();
}

// Export access control functions for use in routes
export { requirePermission, requireRole, UserRole, Permission };
