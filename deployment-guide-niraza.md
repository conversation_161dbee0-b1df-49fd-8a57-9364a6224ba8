# 🚀 CloudPanel.io Deployment Guide - Niraza.site

## Project Structure Analysis
Your project is a full-stack Node.js application with:
- **Frontend**: React + Vite
- **Backend**: Express.js server
- **Database**: SQLite with existing data
- **Build System**: Vite + ESBuild
- **Process Management**: PM2 (to be configured)

## Files to Include in Deployment
✅ **Essential Files:**
- `server/` - Backend code
- `client/` - Frontend code  
- `shared/` - Shared utilities
- `data.db` - SQLite database with your data
- `uploads/` - User uploaded files
- `package.json` & `package-lock.json`
- `vite.config.ts`
- `drizzle.config.ts`
- `tsconfig.json`
- `tailwind.config.ts`
- `postcss.config.js`

❌ **Exclude from Upload:**
- `node_modules/` (will be installed on server)
- `dist/` (will be built on server)
- `.env` (create new production version)
- `test-*.js` files
- Development logs

## Target Deployment Path
- **Domain Path**: `/home/<USER>/htdocs/niraza.site`
- **Database Path**: `/home/<USER>/htdocs/niraza.site/data.db`
- **Uploads Path**: `/home/<USER>/htdocs/niraza.site/uploads`

---

## 📋 Step-by-Step Deployment Instructions

### **Step 1: Prepare Files for Upload**

1. **Create deployment folder locally:**
```bash
mkdir niraza-deployment-package
```

2. **Copy essential files:**
```bash
# Copy main application files
cp -r server/ niraza-deployment-package/
cp -r client/ niraza-deployment-package/
cp -r shared/ niraza-deployment-package/
cp -r uploads/ niraza-deployment-package/

# Copy configuration files
cp package.json niraza-deployment-package/
cp package-lock.json niraza-deployment-package/
cp vite.config.ts niraza-deployment-package/
cp drizzle.config.ts niraza-deployment-package/
cp tsconfig.json niraza-deployment-package/
cp tailwind.config.ts niraza-deployment-package/
cp postcss.config.js niraza-deployment-package/
cp components.json niraza-deployment-package/

# Copy database with your data
cp data.db niraza-deployment-package/

# Copy deployment files
cp .env.production.new niraza-deployment-package/.env
cp ecosystem.config.js niraza-deployment-package/
cp deploy.sh niraza-deployment-package/
cp fix-database-urls.js niraza-deployment-package/
cp backup-database.sh niraza-deployment-package/
cp health-check.js niraza-deployment-package/
```

### **Step 2: Upload to CloudPanel.io**

1. **Compress the deployment package:**
```bash
cd niraza-deployment-package
tar -czf ../niraza-site-deployment.tar.gz .
```

2. **Upload via CloudPanel File Manager or SCP:**
```bash
# Option A: Use CloudPanel File Manager
# - Upload niraza-site-deployment.tar.gz
# - Extract in /home/<USER>/htdocs/niraza.site/

# Option B: Use SCP (if you have SSH access)
scp niraza-site-deployment.tar.gz user@your-server:/home/<USER>/htdocs/niraza.site/
```

### **Step 3: Server Setup Commands**

**Connect to your server via SSH and run these commands:**

```bash
# Navigate to deployment directory
cd /home/<USER>/htdocs/niraza.site

# Extract files (if uploaded as archive)
tar -xzf niraza-site-deployment.tar.gz
rm niraza-site-deployment.tar.gz

# Make deploy script executable
chmod +x deploy.sh

# Run deployment script
./deploy.sh
```

### **Step 4: Manual Commands (Alternative to deploy.sh)**

If you prefer to run commands manually:

```bash
# 1. Set proper permissions
chmod 755 /home/<USER>/htdocs/niraza.site
chmod 644 package.json *.config.* .env
chmod 644 data.db
chmod -R 755 uploads/
mkdir -p logs
chmod -R 755 logs/

# 2. Set ownership
chown -R niraza2:niraza2 /home/<USER>/htdocs/niraza.site

# 3. Install Node.js (if not installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 4. Install dependencies
npm ci --production=false

# 5. Build application
npm run build:prod

# 6. Install PM2 globally
npm install -g pm2

# 7. Start application
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

### **Step 5: Environment Configuration**

**Update your `.env` file with production values:**

```bash
nano .env
```

**Required changes:**
- `SESSION_SECRET`: Generate a strong secret
- `ADMIN_ACCESS_TOKEN`: Set a secure admin token
- `BASE_URL`: Set to `https://niraza.site`
- Verify `DATABASE_URL=sqlite:./data.db`

### **Step 6: Database Verification**

**Verify your database is working:**

```bash
# Check if database file exists and has correct permissions
ls -la data.db

# Test database connection (optional)
node -e "
const Database = require('better-sqlite3');
const db = new Database('data.db');
console.log('Database tables:', db.prepare('SELECT name FROM sqlite_master WHERE type=\"table\"').all());
db.close();
"
```

### **Step 7: PM2 Process Management**

**Essential PM2 commands:**

```bash
# Check application status
pm2 status

# View logs
pm2 logs niraza-site

# Restart application
pm2 restart niraza-site

# Stop application
pm2 stop niraza-site

# Monitor in real-time
pm2 monit

# View detailed info
pm2 show niraza-site
```

### **Step 8: Database URL Migration**

**Fix localhost URLs in your database:**

```bash
# Run the database URL migration script
node fix-database-urls.js
```

This script will update all localhost:3002 URLs in your database to use https://niraza.site

### **Step 9: CloudPanel Configuration**

**In CloudPanel.io dashboard:**

1. **Domain Settings:**
   - Point domain `niraza.site` to `/home/<USER>/htdocs/niraza.site`
   - Enable SSL certificate
   - Set up redirects (www to non-www if needed)

2. **Reverse Proxy Setup:**
   - Create reverse proxy rule
   - Source: `niraza.site`
   - Destination: `http://localhost:3001`
   - Enable SSL

3. **Firewall Rules:**
   - Allow port 3001 for internal communication
   - Ensure ports 80 and 443 are open
