# ✅ CloudPanel.io Deployment Checklist - Niraza.site

## Pre-Deployment (Local)
- [ ] Create niraza-deployment-package folder
- [ ] Copy all essential files (server/, client/, shared/, uploads/, data.db)
- [ ] Copy configuration files (package.json, *.config.*)
- [ ] Include deployment scripts (.env.production.new, ecosystem.config.js, deploy.sh)
- [ ] Include utility scripts (fix-database-urls.js, backup-database.sh, health-check.js)
- [ ] Create compressed archive (tar.gz)
- [ ] Backup local database before deployment

## Server Setup
- [ ] Upload files to `/home/<USER>/htdocs/niraza.site`
- [ ] Extract files if uploaded as archive
- [ ] Set file permissions (chmod 755, 644 as needed)
- [ ] Set ownership (chown niraza2:niraza2)
- [ ] Create logs and backups directories

## Environment Configuration
- [ ] Copy .env.production.new to .env
- [ ] Update SESSION_SECRET with strong value
- [ ] Update ADMIN_ACCESS_TOKEN with secure token
- [ ] Set BASE_URL to https://niraza.site
- [ ] Verify DATABASE_URL=sqlite:./data.db

## Application Build & Start
- [ ] Install Node.js dependencies (npm ci)
- [ ] Build application (npm run build:prod)
- [ ] Verify dist/index.js exists
- [ ] Install PM2 globally
- [ ] Start application with PM2 (niraza-site)
- [ ] Save PM2 configuration
- [ ] Setup PM2 startup script

## Database Migration
- [ ] Run database URL migration script
- [ ] Verify localhost URLs updated to https://niraza.site
- [ ] Test database connectivity
- [ ] Check file permissions on data.db

## CloudPanel Configuration
- [ ] Point niraza.site domain to correct directory
- [ ] Enable SSL certificate
- [ ] Setup reverse proxy (port 3001)
- [ ] Configure firewall rules
- [ ] Test domain resolution

## Final Testing
- [ ] Application responds on https://niraza.site
- [ ] Admin panel accessible
- [ ] File uploads working
- [ ] Checkout pages functional
- [ ] Database queries working
- [ ] PM2 status shows running

## Backup Setup
- [ ] Test backup script
- [ ] Setup automated daily backups
- [ ] Verify backup directory permissions

## Monitoring
- [ ] PM2 monitoring setup
- [ ] Log rotation configured
- [ ] Error tracking enabled
- [ ] Performance monitoring active

---

## Quick Commands Reference

```bash
# Deploy
cd /home/<USER>/htdocs/niraza.site && ./deploy.sh

# Check status
pm2 status

# View logs
pm2 logs niraza-site

# Restart
pm2 restart niraza-site

# Backup database
./backup-database.sh

# Fix URLs
node fix-database-urls.js

# Health check
node health-check.js
```

## Important File Paths
- **Application**: `/home/<USER>/htdocs/niraza.site`
- **Database**: `/home/<USER>/htdocs/niraza.site/data.db`
- **Logs**: `/home/<USER>/htdocs/niraza.site/logs/`
- **Uploads**: `/home/<USER>/htdocs/niraza.site/uploads/`
- **Backups**: `/home/<USER>/htdocs/niraza.site/backups/`

## Domain Configuration
- **Production URL**: https://niraza.site
- **Internal Port**: 3001
- **PM2 App Name**: niraza-site
- **User**: niraza2
