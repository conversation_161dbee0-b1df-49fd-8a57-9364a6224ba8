{"version": "6", "dialect": "sqlite", "id": "5683436c-c2eb-44a0-b2ca-dc8bc50bbff1", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"allowed_emails": {"name": "allowed_emails", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_subject": {"name": "last_subject", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "smtp_provider": {"name": "smtp_provider", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_updated": {"name": "last_updated", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"allowed_emails_email_unique": {"name": "allowed_emails_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "custom_checkout_pages": {"name": "custom_checkout_pages", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_name": {"name": "product_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_description": {"name": "product_description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "custom_payment_link_id": {"name": "custom_payment_link_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "paypal_button_id": {"name": "paypal_button_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "trial_custom_payment_link_id": {"name": "trial_custom_payment_link_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "trial_paypal_button_id": {"name": "trial_paypal_button_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "embed_code_id": {"name": "embed_code_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "smtp_provider_id": {"name": "smtp_provider_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "contact_email": {"name": "contact_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "require_allowed_email": {"name": "require_allowed_email", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "is_trial_checkout": {"name": "is_trial_checkout", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "confirmation_message": {"name": "confirmation_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "header_title": {"name": "header_title", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "footer_text": {"name": "footer_text", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "header_logo": {"name": "header_logo", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "footer_logo": {"name": "footer_logo", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "theme_mode": {"name": "theme_mode", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'light'"}, "use_referrer_masking": {"name": "use_referrer_masking", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "redirect_delay": {"name": "redirect_delay", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 2000}, "expires_at": {"name": "expires_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "views": {"name": "views", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "conversions": {"name": "conversions", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "secondary_smtp_provider_id": {"name": "secondary_smtp_provider_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"custom_checkout_pages_slug_unique": {"name": "custom_checkout_pages_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "custom_invoices": {"name": "custom_invoices", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "invoice_number": {"name": "invoice_number", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_name": {"name": "customer_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_email": {"name": "customer_email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'USD'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "paypal_button_id": {"name": "paypal_button_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "due_date": {"name": "due_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "paid_at": {"name": "paid_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"custom_invoices_invoice_number_unique": {"name": "custom_invoices_invoice_number_unique", "columns": ["invoice_number"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "custom_payment_links": {"name": "custom_payment_links", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "payment_link": {"name": "payment_link", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "button_text": {"name": "button_text", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "success_redirect_url": {"name": "success_redirect_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "is_trial_link": {"name": "is_trial_link", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "devices": {"name": "devices", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip": {"name": "ip", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_login": {"name": "last_login", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"devices_user_id_users_id_fk": {"name": "devices_user_id_users_id_fk", "tableFrom": "devices", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "email_templates": {"name": "email_templates", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "template_id": {"name": "template_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "html_content": {"name": "html_content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "text_content": {"name": "text_content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'general'"}, "is_default": {"name": "is_default", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"email_templates_template_id_unique": {"name": "email_templates_template_id_unique", "columns": ["template_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "general_settings": {"name": "general_settings", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "site_name": {"name": "site_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "site_description": {"name": "site_description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "favicon_url": {"name": "favicon_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "secondary_color": {"name": "secondary_color", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "footer_text": {"name": "footer_text", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "enable_checkout": {"name": "enable_checkout", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "enable_custom_checkout": {"name": "enable_custom_checkout", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "enable_test_mode": {"name": "enable_test_mode", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "default_test_customer_enabled": {"name": "default_test_customer_enabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "default_test_customer_name": {"name": "default_test_customer_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "default_test_customer_email": {"name": "default_test_customer_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email_domain_restriction_enabled": {"name": "email_domain_restriction_enabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "email_domain_restriction_domains": {"name": "email_domain_restriction_domains", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "seo_privacy_settings": {"name": "seo_privacy_settings", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "telegram_bot_settings": {"name": "telegram_bot_settings", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "url_settings": {"name": "url_settings", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "homepage_config": {"name": "homepage_config", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "sections_data": {"name": "sections_data", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "seo_settings": {"name": "seo_settings", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "theme_settings": {"name": "theme_settings", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "invoices": {"name": "invoices", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "customer_name": {"name": "customer_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "customer_email": {"name": "customer_email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "product_id": {"name": "product_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "amount": {"name": "amount", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "paypal_invoice_id": {"name": "paypal_invoice_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "paypal_invoice_url": {"name": "paypal_invoice_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_trial_order": {"name": "is_trial_order", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "has_upgraded": {"name": "has_upgraded", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "upgraded_at": {"name": "upgraded_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "custom_checkout_page_id": {"name": "custom_checkout_page_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "app_type": {"name": "app_type", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "mac_address": {"name": "mac_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "paypal_buttons": {"name": "paypal_buttons", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "button_code": {"name": "button_code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "products": {"name": "products", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "price": {"name": "price", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "payment_method": {"name": "payment_method", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'manual-payment'"}, "custom_payment_link_id": {"name": "custom_payment_link_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "trial_custom_payment_link_id": {"name": "trial_custom_payment_link_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "embed_code_id": {"name": "embed_code_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "recovery_codes": {"name": "recovery_codes", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "used": {"name": "used", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "used_at": {"name": "used_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"recovery_codes_user_id_users_id_fk": {"name": "recovery_codes_user_id_users_id_fk", "tableFrom": "recovery_codes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "smtp_providers": {"name": "smtp_providers", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "host": {"name": "host", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "port": {"name": "port", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "secure": {"name": "secure", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "auth_user": {"name": "auth_user", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "auth_pass": {"name": "auth_pass", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "from_email": {"name": "from_email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "from_name": {"name": "from_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "admin_email": {"name": "admin_email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "active": {"name": "active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "is_default": {"name": "is_default", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "is_backup": {"name": "is_backup", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "system_messages": {"name": "system_messages", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "message_id": {"name": "message_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_html": {"name": "is_html", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"system_messages_message_id_unique": {"name": "system_messages_message_id_unique", "columns": ["message_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "two_factor_secret": {"name": "two_factor_secret", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "two_factor_enabled": {"name": "two_factor_enabled", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}