import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

// Password security configuration
const BCRYPT_ROUNDS = 12; // High security level
const MIN_PASSWORD_LENGTH = 8;
const MAX_PASSWORD_LENGTH = 128;

// Password strength requirements
interface PasswordRequirements {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
}

const DEFAULT_REQUIREMENTS: PasswordRequirements = {
  minLength: MIN_PASSWORD_LENGTH,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true
};

// Hash password using bcrypt
export async function hashPassword(password: string): Promise<string> {
  if (!password || password.length < MIN_PASSWORD_LENGTH) {
    throw new Error(`Password must be at least ${MIN_PASSWORD_LENGTH} characters long`);
  }
  
  if (password.length > MAX_PASSWORD_LENGTH) {
    throw new Error(`Password must be no more than ${MAX_PASSWORD_LENGTH} characters long`);
  }
  
  try {
    const salt = await bcrypt.genSalt(BCRYPT_ROUNDS);
    const hash = await bcrypt.hash(password, salt);
    return hash;
  } catch (error) {
    console.error('Error hashing password:', error);
    throw new Error('Failed to hash password');
  }
}

// Verify password against hash
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  if (!password || !hash) {
    return false;
  }
  
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error('Error verifying password:', error);
    return false;
  }
}

// Check if password meets security requirements
export function validatePasswordStrength(
  password: string, 
  requirements: PasswordRequirements = DEFAULT_REQUIREMENTS
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!password) {
    errors.push('Password is required');
    return { isValid: false, errors };
  }
  
  if (password.length < requirements.minLength) {
    errors.push(`Password must be at least ${requirements.minLength} characters long`);
  }
  
  if (password.length > MAX_PASSWORD_LENGTH) {
    errors.push(`Password must be no more than ${MAX_PASSWORD_LENGTH} characters long`);
  }
  
  if (requirements.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (requirements.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (requirements.requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (requirements.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  // Check for common weak patterns
  if (/(.)\1{2,}/.test(password)) {
    errors.push('Password cannot contain repeated characters');
  }
  
  if (/^(123|abc|qwe|password|admin)/i.test(password)) {
    errors.push('Password cannot start with common weak patterns');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Generate secure random password
export function generateSecurePassword(length: number = 16): string {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  const allChars = uppercase + lowercase + numbers + specialChars;
  
  let password = '';
  
  // Ensure at least one character from each category
  password += uppercase[crypto.randomInt(0, uppercase.length)];
  password += lowercase[crypto.randomInt(0, lowercase.length)];
  password += numbers[crypto.randomInt(0, numbers.length)];
  password += specialChars[crypto.randomInt(0, specialChars.length)];
  
  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += allChars[crypto.randomInt(0, allChars.length)];
  }
  
  // Shuffle the password
  return password.split('').sort(() => crypto.randomInt(0, 3) - 1).join('');
}

// Check if password hash is using old SHA256 format
export function isLegacyHash(hash: string): boolean {
  // SHA256 hashes are 64 characters long and contain only hex characters
  return /^[a-f0-9]{64}$/i.test(hash);
}

// Migrate legacy SHA256 hash to bcrypt (when user logs in)
export async function migrateLegacyPassword(
  plainPassword: string,
  legacyHash: string
): Promise<string | null> {
  // Verify the password against the legacy SHA256 hash
  const sha256Hash = crypto.createHash('sha256').update(plainPassword).digest('hex');

  if (sha256Hash === legacyHash) {
    // Password is correct, create new bcrypt hash
    return await hashPassword(plainPassword);
  }

  return null;
}

// Generate secure session token
export function generateSessionToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

// Generate secure API key
export function generateApiKey(): string {
  const prefix = 'sk_';
  const randomPart = crypto.randomBytes(24).toString('base64url');
  return prefix + randomPart;
}
