import { DatabaseStorage } from './database-storage';
import { initializeDatabase } from './init-database';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

console.log('🗄️ Using SQLite database storage - all data is now persistent!');

// Create database storage instance
const storage = new DatabaseStorage();

// Initialize database with sample data on startup
initializeDatabase().catch(error => {
  console.error('Failed to initialize database:', error);
});

export { storage };
