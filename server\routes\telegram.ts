import { Router, Request, Response } from 'express';
import { telegramBot } from '../services/telegram-bot';
import { storage } from '../storage-factory';
import { isAdmin } from '../middleware/auth';
import { z } from 'zod';

const telegramRouter = Router();

// Validation schemas
const telegramConfigSchema = z.object({
  enabled: z.boolean().default(false),
  botToken: z.string().optional().default(''),
  adminChatId: z.string().optional().default(''),
  webhookUrl: z.string().optional().default(''),
  notifications: z.object({
    newOrders: z.boolean().default(true),
    paymentConfirmations: z.boolean().default(true),
    trialUpgrades: z.boolean().default(true),
    orderStatusChanges: z.boolean().default(true)
  }).default({
    newOrders: true,
    paymentConfirmations: true,
    trialUpgrades: true,
    orderStatusChanges: true
  }),
  emailIntegration: z.object({
    enabled: z.boolean().default(true),
    allowQuickSend: z.boolean().default(true),
    defaultTemplateId: z.string().default('payment-confirmation')
  }).default({
    enabled: true,
    allowQuickSend: true,
    defaultTemplateId: 'payment-confirmation'
  }),
  m3uManagement: z.object({
    enabled: z.boolean().default(true),
    autoExtractCredentials: z.boolean().default(true),
    credentialFormat: z.string().default('Username: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uLink}}'),
    defaultM3uLinks: z.array(z.string()).default([])
  }).default({
    enabled: true,
    autoExtractCredentials: true,
    credentialFormat: 'Username: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uLink}}',
    defaultM3uLinks: []
  }),
  security: z.object({
    verifyAdminOnly: z.boolean().default(true),
    rateLimitEnabled: z.boolean().default(true),
    auditLogging: z.boolean().default(true)
  }).default({
    verifyAdminOnly: true,
    rateLimitEnabled: true,
    auditLogging: true
  })
});

// Get Telegram bot configuration
telegramRouter.get('/config', isAdmin, async (req: Request, res: Response) => {
  try {
    const dbSettings = await storage.getGeneralSettings();

    if (!dbSettings || !dbSettings.telegram_bot_settings) {
      return res.json({
        enabled: false,
        botToken: '',
        adminChatId: '',
        webhookUrl: '',
        notifications: {
          newOrders: true,
          paymentConfirmations: true,
          trialUpgrades: true,
          orderStatusChanges: true
        },
        emailIntegration: {
          enabled: true,
          allowQuickSend: true,
          defaultTemplateId: ''
        },
        m3uManagement: {
          enabled: true,
          autoExtractCredentials: true,
          credentialFormat: 'Username: {{username}}\nPassword: {{password}}\nM3U URL: {{m3uLink}}',
          defaultM3uLinks: []
        },
        security: {
          verifyAdminOnly: true,
          rateLimitEnabled: true,
          auditLogging: true
        }
      });
    }

    const telegramBot = JSON.parse(dbSettings.telegram_bot_settings);
    res.json(telegramBot);
  } catch (error) {
    console.error('Error fetching Telegram config:', error);
    res.status(500).json({ message: 'Failed to fetch Telegram configuration' });
  }
});

// Update Telegram bot configuration
telegramRouter.put('/config', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('PUT /api/telegram/config called with body:', JSON.stringify(req.body, null, 2));

    const validatedData = telegramConfigSchema.parse(req.body);
    console.log('Validation successful, validated data:', JSON.stringify(validatedData, null, 2));

    // Update the configuration in database
    const dbUpdate = {
      telegramBotSettings: JSON.stringify(validatedData),
      updatedAt: new Date().toISOString()
    };

    console.log('About to update database with:', dbUpdate);
    const updatedDbSettings = await storage.updateGeneralSettings(dbUpdate);
    console.log('Database update completed, result:', updatedDbSettings ? 'SUCCESS' : 'FAILED');

    if (!updatedDbSettings) {
      return res.status(500).json({ message: 'Failed to update Telegram configuration' });
    }

    console.log('Updated DB settings keys:', Object.keys(updatedDbSettings));
    console.log('telegramBotSettings value:', updatedDbSettings.telegramBotSettings);

    if (!updatedDbSettings.telegramBotSettings) {
      console.error('telegramBotSettings is undefined in updated settings');
      return res.status(500).json({ message: 'Failed to retrieve updated Telegram configuration' });
    }

    const updatedTelegramBot = JSON.parse(updatedDbSettings.telegramBotSettings);

    console.log('Telegram configuration updated successfully');
    res.json(updatedTelegramBot);
  } catch (error) {
    console.error('Error updating Telegram config:', error);

    if (error instanceof z.ZodError) {
      console.error('Validation errors:', error.errors);
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update Telegram configuration' });
  }
});

// Test bot connection
telegramRouter.post('/test-connection', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('Testing Telegram bot connection...');
    const result = await telegramBot.testConnection();

    console.log('Connection test result:', result);
    res.json(result);
  } catch (error) {
    console.error('Error testing Telegram connection:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to test connection'
    });
  }
});

// Set webhook
telegramRouter.post('/set-webhook', isAdmin, async (req: Request, res: Response) => {
  try {
    const { webhookUrl } = req.body;

    if (!webhookUrl) {
      return res.status(400).json({
        success: false,
        message: 'Webhook URL is required'
      });
    }

    console.log('Setting Telegram webhook to:', webhookUrl);
    const result = await telegramBot.setWebhook(webhookUrl);

    console.log('Webhook setup result:', result);
    res.json(result);
  } catch (error) {
    console.error('Error setting Telegram webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to set webhook'
    });
  }
});

// Send test notification
telegramRouter.post('/test-notification', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('Sending test Telegram notification...');

    const testOrder = {
      orderId: 9999,
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
      amount: '19.99',
      status: 'paid',
      country: 'Test Country',
      appType: 'IPTV Smarters Pro',
      isTrialOrder: false,
      createdAt: new Date().toISOString(),
      macAddress: '00:1A:79:B4:E7:2D'
    };

    // First check if bot is properly configured
    const dbSettings = await storage.getGeneralSettings();

    if (!dbSettings || !dbSettings.telegramBotSettings) {
      return res.json({
        success: false,
        message: 'Telegram bot is not configured. Please configure it in settings.'
      });
    }

    const config = JSON.parse(dbSettings.telegramBotSettings);

    if (!config.enabled) {
      return res.json({
        success: false,
        message: 'Telegram bot is disabled. Please enable it in settings.'
      });
    }

    if (!config.botToken) {
      return res.json({
        success: false,
        message: 'Bot token is not configured. Please add your bot token.'
      });
    }

    if (!config.adminChatId) {
      return res.json({
        success: false,
        message: 'Admin Chat ID is not configured. Please start your bot (@spovo_bot) and send /start to get your Chat ID.'
      });
    }

    const success = await telegramBot.sendOrderNotification(testOrder);

    if (success) {
      console.log('Test notification sent successfully');
      res.json({
        success: true,
        message: 'Test notification sent successfully! Check your Telegram.'
      });
    } else {
      console.log('Failed to send test notification');
      res.json({
        success: false,
        message: 'Failed to send test notification. Please check that you have started your bot (@spovo_bot) and added the correct Chat ID.'
      });
    }
  } catch (error) {
    console.error('Error sending test notification:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test notification: ' + (error instanceof Error ? error.message : 'Unknown error')
    });
  }
});

// Webhook endpoint for receiving updates from Telegram
telegramRouter.post('/webhook', async (req: Request, res: Response) => {
  try {
    console.log('Received Telegram webhook:', JSON.stringify(req.body, null, 2));

    // CRITICAL: Respond immediately to prevent Telegram timeout
    // Telegram expects a response within 10 seconds
    res.status(200).json({ ok: true });

    // Handle the update asynchronously after responding
    // This prevents webhook timeouts that can cause button freezing
    setImmediate(async () => {
      try {
        await telegramBot.handleUpdate(req.body);
      } catch (updateError) {
        console.error('Error processing Telegram update asynchronously:', updateError);
      }
    });

  } catch (error) {
    console.error('Error handling Telegram webhook:', error);
    // Still respond with 200 to prevent Telegram from retrying
    if (!res.headersSent) {
      res.status(200).json({ ok: true });
    }
  }
});

// Get webhook info
telegramRouter.get('/webhook-info', isAdmin, async (req: Request, res: Response) => {
  try {
    const dbSettings = await storage.getGeneralSettings();

    if (!dbSettings || !dbSettings.telegramBotSettings) {
      return res.json({
        success: false,
        message: 'Telegram bot not configured'
      });
    }

    const config = JSON.parse(dbSettings.telegramBotSettings);

    if (!config.botToken) {
      return res.json({
        success: false,
        message: 'Bot token not configured'
      });
    }

    const response = await fetch(`https://api.telegram.org/bot${config.botToken}/getWebhookInfo`);
    const data = await response.json();

    res.json({
      success: data.ok,
      webhookInfo: data.result
    });
  } catch (error) {
    console.error('Error getting webhook info:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get webhook info'
    });
  }
});

// Reset bot cache and sync with app
telegramRouter.post('/reset-cache', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('Admin requested Telegram bot cache reset');

    // Clear bot cache and sync with app
    await telegramBot.refreshAndSync();

    res.json({
      success: true,
      message: 'Telegram bot cache cleared and synced with app successfully'
    });
  } catch (error) {
    console.error('Error resetting Telegram bot cache:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset bot cache'
    });
  }
});

// Remove webhook and restart polling
telegramRouter.post('/remove-webhook', isAdmin, async (req: Request, res: Response) => {
  try {
    console.log('Admin requested webhook removal');

    const result = await telegramBot.removeWebhook();

    res.json(result);
  } catch (error) {
    console.error('Error removing webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove webhook'
    });
  }
});

// Send custom message to admin
telegramRouter.post('/send-message', isAdmin, async (req: Request, res: Response) => {
  try {
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        message: 'Message text is required'
      });
    }

    const success = await telegramBot.sendMessage(message);

    res.json({
      success,
      message: success ? 'Message sent successfully' : 'Failed to send message'
    });
  } catch (error) {
    console.error('Error sending custom message:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send message'
    });
  }
});

export { telegramRouter };
