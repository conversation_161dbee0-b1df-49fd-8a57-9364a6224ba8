#!/usr/bin/env node

/**
 * Security Headers Test Script
 * Tests CSP, HSTS, X-Frame-Options, and other security headers
 */

import fs from 'fs';

console.log('🛡️ Starting security headers tests...\n');

// Test 1: Security Headers Files
async function testSecurityHeadersFiles() {
  console.log('📋 Test 1: Security Headers Files');
  
  try {
    const requiredFiles = [
      'server/middleware/security-headers.ts'
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        console.log(`✅ File exists: ${file}`);
        passed++;
      } else {
        console.log(`❌ File missing: ${file}`);
        failed++;
      }
    }
    
    console.log(`📊 File Check Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ File check failed: ${error.message}\n`);
    return false;
  }
}

// Test 2: Environment Configuration
async function testEnvironmentConfig() {
  console.log('📋 Test 2: Environment Configuration');
  
  try {
    const envContent = fs.readFileSync('./.env', 'utf8');
    
    const requiredSettings = [
      'ENABLE_CSP',
      'CSP_REPORT_ONLY',
      'CSP_REPORT_URI',
      'ENABLE_HSTS',
      'HSTS_MAX_AGE',
      'HSTS_INCLUDE_SUBDOMAINS',
      'ENABLE_X_FRAME_OPTIONS',
      'X_FRAME_OPTIONS',
      'ENABLE_REFERRER_POLICY',
      'REFERRER_POLICY',
      'ENABLE_PERMISSIONS_POLICY'
    ];
    
    let passed = 0;
    let failed = 0;
    
    for (const setting of requiredSettings) {
      if (envContent.includes(setting)) {
        console.log(`✅ Environment setting found: ${setting}`);
        passed++;
      } else {
        console.log(`❌ Environment setting missing: ${setting}`);
        failed++;
      }
    }
    
    console.log(`📊 Environment Config Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ Environment config test failed: ${error.message}\n`);
    return false;
  }
}

// Test 3: CSP Directive Generation
async function testCSPDirectiveGeneration() {
  console.log('📋 Test 3: CSP Directive Generation');
  
  try {
    // Simulate CSP directive generation
    const directives = {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://js.stripe.com"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.stripe.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      objectSrc: ["'none'"],
      frameAncestors: ["'none'"],
      upgradeInsecureRequests: true,
      blockAllMixedContent: true
    };
    
    let passed = 0;
    let failed = 0;
    
    // Test directive formatting
    const cspParts = [];
    
    Object.entries(directives).forEach(([directive, values]) => {
      if (directive === 'upgradeInsecureRequests' || directive === 'blockAllMixedContent') {
        if (values) {
          const kebabCase = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
          cspParts.push(kebabCase);
        }
      } else if (Array.isArray(values) && values.length > 0) {
        const kebabCase = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
        cspParts.push(`${kebabCase} ${values.join(' ')}`);
      }
    });
    
    const cspHeader = cspParts.join('; ');
    
    // Test that CSP header is generated
    if (cspHeader.length > 0) {
      console.log('✅ CSP header generation working');
      passed++;
    } else {
      console.log('❌ CSP header generation failed');
      failed++;
    }
    
    // Test specific directives
    if (cspHeader.includes("default-src 'self'")) {
      console.log('✅ Default-src directive correct');
      passed++;
    } else {
      console.log('❌ Default-src directive incorrect');
      failed++;
    }
    
    if (cspHeader.includes("object-src 'none'")) {
      console.log('✅ Object-src directive correct');
      passed++;
    } else {
      console.log('❌ Object-src directive incorrect');
      failed++;
    }
    
    if (cspHeader.includes('upgrade-insecure-requests')) {
      console.log('✅ Upgrade-insecure-requests directive present');
      passed++;
    } else {
      console.log('❌ Upgrade-insecure-requests directive missing');
      failed++;
    }
    
    console.log(`📊 CSP Generation Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ CSP directive generation test failed: ${error.message}\n`);
    return false;
  }
}

// Test 4: HSTS Header Generation
async function testHSTSHeaderGeneration() {
  console.log('📋 Test 4: HSTS Header Generation');
  
  try {
    const hstsConfig = {
      maxAge: 31536000, // 1 year
      includeSubDomains: true,
      preload: false
    };
    
    let passed = 0;
    let failed = 0;
    
    // Generate HSTS header
    let hstsValue = `max-age=${hstsConfig.maxAge}`;
    if (hstsConfig.includeSubDomains) {
      hstsValue += '; includeSubDomains';
    }
    if (hstsConfig.preload) {
      hstsValue += '; preload';
    }
    
    // Test HSTS header format
    if (hstsValue.includes('max-age=31536000')) {
      console.log('✅ HSTS max-age correct');
      passed++;
    } else {
      console.log('❌ HSTS max-age incorrect');
      failed++;
    }
    
    if (hstsValue.includes('includeSubDomains')) {
      console.log('✅ HSTS includeSubDomains present');
      passed++;
    } else {
      console.log('❌ HSTS includeSubDomains missing');
      failed++;
    }
    
    if (!hstsValue.includes('preload')) {
      console.log('✅ HSTS preload correctly omitted');
      passed++;
    } else {
      console.log('❌ HSTS preload incorrectly included');
      failed++;
    }
    
    console.log(`📊 HSTS Generation Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ HSTS header generation test failed: ${error.message}\n`);
    return false;
  }
}

// Test 5: Permissions Policy Generation
async function testPermissionsPolicyGeneration() {
  console.log('📋 Test 5: Permissions Policy Generation');
  
  try {
    const permissionsDirectives = {
      camera: [],
      microphone: [],
      geolocation: [],
      payment: ["'self'"],
      fullscreen: ["'self'"],
      notifications: ["'self'"],
      autoplay: []
    };
    
    let passed = 0;
    let failed = 0;
    
    // Generate Permissions Policy header
    const policyParts = [];
    
    Object.entries(permissionsDirectives).forEach(([directive, allowlist]) => {
      const kebabCase = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
      
      if (Array.isArray(allowlist)) {
        if (allowlist.length === 0) {
          policyParts.push(`${kebabCase}=()`);
        } else {
          policyParts.push(`${kebabCase}=(${allowlist.join(' ')})`);
        }
      }
    });
    
    const permissionsPolicyHeader = policyParts.join(', ');
    
    // Test Permissions Policy header format
    if (permissionsPolicyHeader.includes('camera=()')) {
      console.log('✅ Camera permission correctly disabled');
      passed++;
    } else {
      console.log('❌ Camera permission format incorrect');
      failed++;
    }
    
    if (permissionsPolicyHeader.includes("payment=('self')")) {
      console.log('✅ Payment permission correctly configured');
      passed++;
    } else {
      console.log('❌ Payment permission format incorrect');
      failed++;
    }
    
    if (permissionsPolicyHeader.includes('geolocation=()')) {
      console.log('✅ Geolocation permission correctly disabled');
      passed++;
    } else {
      console.log('❌ Geolocation permission format incorrect');
      failed++;
    }
    
    console.log(`📊 Permissions Policy Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ Permissions policy generation test failed: ${error.message}\n`);
    return false;
  }
}

// Test 6: Security Headers Validation
async function testSecurityHeadersValidation() {
  console.log('📋 Test 6: Security Headers Validation');
  
  try {
    let passed = 0;
    let failed = 0;
    
    // Test X-Frame-Options values
    const validFrameOptions = ['DENY', 'SAMEORIGIN', 'ALLOW-FROM https://example.com'];
    const invalidFrameOptions = ['INVALID', 'deny', ''];
    
    for (const option of validFrameOptions) {
      const isValid = ['DENY', 'SAMEORIGIN'].includes(option) || option.startsWith('ALLOW-FROM ');
      if (isValid) {
        console.log(`✅ Valid X-Frame-Options: ${option}`);
        passed++;
      } else {
        console.log(`❌ Invalid X-Frame-Options: ${option}`);
        failed++;
      }
    }
    
    // Test Referrer-Policy values
    const validReferrerPolicies = [
      'no-referrer',
      'no-referrer-when-downgrade',
      'origin',
      'origin-when-cross-origin',
      'same-origin',
      'strict-origin',
      'strict-origin-when-cross-origin',
      'unsafe-url'
    ];
    
    const testPolicy = 'strict-origin-when-cross-origin';
    if (validReferrerPolicies.includes(testPolicy)) {
      console.log(`✅ Valid Referrer-Policy: ${testPolicy}`);
      passed++;
    } else {
      console.log(`❌ Invalid Referrer-Policy: ${testPolicy}`);
      failed++;
    }
    
    // Test HSTS max-age validation
    const hstsMaxAge = 31536000;
    if (hstsMaxAge >= 0) {
      console.log(`✅ Valid HSTS max-age: ${hstsMaxAge}`);
      passed++;
    } else {
      console.log(`❌ Invalid HSTS max-age: ${hstsMaxAge}`);
      failed++;
    }
    
    console.log(`📊 Security Headers Validation Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ Security headers validation test failed: ${error.message}\n`);
    return false;
  }
}

// Test 7: CSP Violation Reporting
async function testCSPViolationReporting() {
  console.log('📋 Test 7: CSP Violation Reporting');
  
  try {
    // Simulate CSP violation report
    const violationReport = {
      'csp-report': {
        'document-uri': 'https://example.com/page',
        'referrer': '',
        'violated-directive': 'script-src',
        'effective-directive': 'script-src',
        'original-policy': "default-src 'self'; script-src 'self'",
        'disposition': 'enforce',
        'blocked-uri': 'https://evil.com/script.js',
        'line-number': 1,
        'column-number': 1,
        'source-file': 'https://example.com/page',
        'status-code': 200,
        'script-sample': ''
      }
    };
    
    let passed = 0;
    let failed = 0;
    
    // Test violation report structure
    if (violationReport['csp-report']) {
      console.log('✅ CSP violation report structure correct');
      passed++;
    } else {
      console.log('❌ CSP violation report structure incorrect');
      failed++;
    }
    
    // Test required fields
    const report = violationReport['csp-report'];
    const requiredFields = ['document-uri', 'violated-directive', 'blocked-uri', 'original-policy'];
    
    const hasAllFields = requiredFields.every(field => report[field] !== undefined);
    if (hasAllFields) {
      console.log('✅ CSP violation report has all required fields');
      passed++;
    } else {
      console.log('❌ CSP violation report missing required fields');
      failed++;
    }
    
    // Test violation detection
    if (report['violated-directive'] === 'script-src' && report['blocked-uri'].includes('evil.com')) {
      console.log('✅ CSP violation correctly detected malicious script');
      passed++;
    } else {
      console.log('❌ CSP violation detection failed');
      failed++;
    }
    
    console.log(`📊 CSP Violation Reporting Results: ${passed} passed, ${failed} failed\n`);
    return failed === 0;
    
  } catch (error) {
    console.log(`❌ CSP violation reporting test failed: ${error.message}\n`);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  const tests = [
    { name: 'Security Headers Files', fn: testSecurityHeadersFiles },
    { name: 'Environment Configuration', fn: testEnvironmentConfig },
    { name: 'CSP Directive Generation', fn: testCSPDirectiveGeneration },
    { name: 'HSTS Header Generation', fn: testHSTSHeaderGeneration },
    { name: 'Permissions Policy Generation', fn: testPermissionsPolicyGeneration },
    { name: 'Security Headers Validation', fn: testSecurityHeadersValidation },
    { name: 'CSP Violation Reporting', fn: testCSPViolationReporting }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ Test "${test.name}" threw an error:`, error.message);
      failed++;
    }
  }
  
  console.log('='.repeat(50));
  console.log(`🛡️ Security Headers Test Results:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All security headers tests passed!');
    console.log('\n📋 Next steps:');
    console.log('1. Add security headers middleware to your Express app');
    console.log('2. Configure CSP directives for your specific needs');
    console.log('3. Set up CSP violation reporting endpoint');
    console.log('4. Test headers in browser developer tools');
    console.log('5. Monitor security violations and adjust policies');
    process.exit(0);
  } else {
    console.log('\n⚠️ Some security headers tests failed. Please review and fix issues.');
    process.exit(1);
  }
}

// Run tests
runAllTests().catch(error => {
  console.error('❌ Test runner error:', error);
  process.exit(1);
});
