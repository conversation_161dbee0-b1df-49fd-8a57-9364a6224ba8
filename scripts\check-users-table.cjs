const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '..', 'app', 'data', 'data.db');
const db = new Database(dbPath);

console.log('🔍 Checking users table schema...');

try {
  const schema = db.prepare('PRAGMA table_info(users)').all();
  console.log('Users table columns:');
  schema.forEach(col => {
    console.log(`  - ${col.name}: ${col.type} (nullable: ${!col.notnull}, default: ${col.dflt_value})`);
  });

  console.log('\n🔍 Checking if admin user exists...');
  const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
  console.log(`Total users: ${userCount.count}`);

  if (userCount.count > 0) {
    const users = db.prepare('SELECT * FROM users').all();
    users.forEach(user => {
      console.log(`User: ${user.username}, Created: ${user.created_at}`);
    });
  } else {
    console.log('❌ No users found in database');
  }

  // Check if role column exists
  const hasRoleColumn = schema.some(col => col.name === 'role');
  console.log(`\n🔍 Role column exists: ${hasRoleColumn ? 'YES' : 'NO'}`);

  if (!hasRoleColumn) {
    console.log('⚠️ Users table is missing the role column - this indicates schema mismatch');
  }

} catch (error) {
  console.error('❌ Error checking users table:', error.message);
} finally {
  db.close();
}
