import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize database - use the same path as the app
const dbPath = process.env.DATABASE_PATH || 'data.db';
console.log('Database path:', dbPath);

try {
  const db = new Database(dbPath);
  
  // Test simple query first
  console.log('Testing simple query...');
  const allInvoices = db.prepare("SELECT * FROM invoices LIMIT 5").all();
  console.log('Simple query result:', allInvoices);
  
  // Test with is_trial_order filter
  console.log('\nTesting is_trial_order filter...');
  const regularOrders = db.prepare("SELECT * FROM invoices WHERE is_trial_order = ? LIMIT 5").all(0);
  console.log('Regular orders:', regularOrders);
  
  const trialOrders = db.prepare("SELECT * FROM invoices WHERE is_trial_order = ? LIMIT 5").all(1);
  console.log('Trial orders:', trialOrders);
  
  // Test count query
  console.log('\nTesting count query...');
  const totalCount = db.prepare("SELECT COUNT(*) as count FROM invoices").get();
  console.log('Total count:', totalCount);
  
  // Test count with filter
  const regularCount = db.prepare("SELECT COUNT(*) as count FROM invoices WHERE is_trial_order = ?").get(0);
  console.log('Regular count:', regularCount);
  
  const trialCount = db.prepare("SELECT COUNT(*) as count FROM invoices WHERE is_trial_order = ?").get(1);
  console.log('Trial count:', trialCount);
  
  db.close();
} catch (error) {
  console.error('Error testing queries:', error);
}
