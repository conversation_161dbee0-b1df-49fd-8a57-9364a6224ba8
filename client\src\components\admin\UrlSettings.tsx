import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Globe, 
  Shield, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  ExternalLink,
  Database,
  Zap
} from 'lucide-react';

interface UrlSettings {
  baseUrl?: string;
  autoDetect: boolean;
  forceHttps: boolean;
}

interface MigrationStatus {
  localhostUrlsFound: number;
  needsMigration: boolean;
  details: Record<string, number>;
}

interface PortDetectionResult {
  detectedPorts: string[];
  mostCommonPort: string | null;
  totalLocalhostUrls: number;
}

interface PortDetectionInfo {
  portDetection: PortDetectionResult;
  currentPort: string;
  recommendations: {
    useDetectedPort: string | null;
    useCurrentPort: string;
    hasConflict: boolean;
  };
}

export function UrlSettings() {
  const [settings, setSettings] = useState<UrlSettings>({
    autoDetect: true,
    forceHttps: false
  });
  const [migrationStatus, setMigrationStatus] = useState<MigrationStatus | null>(null);
  const [portDetectionInfo, setPortDetectionInfo] = useState<PortDetectionInfo | null>(null);
  const [manualPort, setManualPort] = useState<string>('');
  const [customDomain, setCustomDomain] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [migrating, setMigrating] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    fetchSettings();
    fetchMigrationStatus();
    fetchPortDetectionInfo();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/url-settings');
      const data = await response.json();
      if (data.success) {
        setSettings(data.urlSettings);
      }
    } catch (error) {
      console.error('Error fetching URL settings:', error);
    }
  };

  const fetchMigrationStatus = async () => {
    try {
      const response = await fetch('/api/admin/url-migration/status');
      const data = await response.json();
      if (data.success) {
        setMigrationStatus(data);
      }
    } catch (error) {
      console.error('Error fetching migration status:', error);
    }
  };

  const fetchPortDetectionInfo = async () => {
    try {
      const response = await fetch('/api/admin/url-migration/port-detection');
      const data = await response.json();
      if (data.success) {
        setPortDetectionInfo(data);
        // Set default manual port to the most common detected port or current port
        if (data.portDetection.mostCommonPort) {
          setManualPort(data.portDetection.mostCommonPort);
        } else {
          setManualPort(data.currentPort);
        }
      }
    } catch (error) {
      console.error('Error fetching port detection info:', error);
    }
  };

  const saveSettings = async () => {
    setLoading(true);
    setMessage(null);
    
    try {
      const response = await fetch('/api/admin/url-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setMessage({ type: 'success', text: 'URL settings saved successfully!' });
      } else {
        setMessage({ type: 'error', text: data.message || 'Failed to save settings' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to save settings' });
    } finally {
      setLoading(false);
    }
  };

  const runMigration = async (type: 'relative' | 'auto' | 'domain' | 'smart-port' | 'with-port', domain?: string, port?: string) => {
    setMigrating(true);
    setMessage(null);

    try {
      let url = '/api/admin/url-migration/';
      let body = {};

      switch (type) {
        case 'relative':
          url += 'to-relative';
          break;
        case 'auto':
          url += 'auto';
          break;
        case 'domain':
          url += 'to-domain';
          body = { domain };
          break;
        case 'smart-port':
          url += 'smart-port';
          body = { targetDomain: domain };
          break;
        case 'with-port':
          url += 'with-port';
          body = { targetDomain: domain, port };
          break;
      }

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      const data = await response.json();

      if (data.success) {
        setMessage({ type: 'success', text: data.message });
        await fetchMigrationStatus(); // Refresh status
        await fetchPortDetectionInfo(); // Refresh port info
      } else {
        setMessage({ type: 'error', text: data.message || 'Migration failed' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Migration failed' });
    } finally {
      setMigrating(false);
    }
  };

  const currentDomain = window.location.origin;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Globe className="h-6 w-6" />
        <h2 className="text-2xl font-bold">URL Settings</h2>
      </div>

      {message && (
        <Alert className={message.type === 'error' ? 'border-red-500' : 'border-green-500'}>
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* URL Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            URL Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="baseUrl">Base URL (Optional)</Label>
            <Input
              id="baseUrl"
              placeholder="https://yourdomain.com"
              value={settings.baseUrl || ''}
              onChange={(e) => setSettings({ ...settings, baseUrl: e.target.value || undefined })}
            />
            <p className="text-sm text-muted-foreground">
              Leave empty to auto-detect from request headers. Set a specific URL to override auto-detection.
            </p>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Auto-detect Domain</Label>
              <p className="text-sm text-muted-foreground">
                Automatically detect the domain from incoming requests
              </p>
            </div>
            <Switch
              checked={settings.autoDetect}
              onCheckedChange={(checked) => setSettings({ ...settings, autoDetect: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Force HTTPS
              </Label>
              <p className="text-sm text-muted-foreground">
                Always use HTTPS for generated URLs (recommended for production)
              </p>
            </div>
            <Switch
              checked={settings.forceHttps}
              onCheckedChange={(checked) => setSettings({ ...settings, forceHttps: checked })}
            />
          </div>

          <div className="pt-4">
            <Button onClick={saveSettings} disabled={loading}>
              {loading ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : null}
              Save Settings
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Migration Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            URL Migration Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {migrationStatus ? (
            <>
              <div className="flex items-center gap-2">
                {migrationStatus.needsMigration ? (
                  <>
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    <span className="font-medium">Migration Needed</span>
                    <Badge variant="secondary">
                      {migrationStatus.localhostUrlsFound} localhost URLs found
                    </Badge>
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="font-medium">No Migration Needed</span>
                    <Badge variant="outline">All URLs are properly configured</Badge>
                  </>
                )}
              </div>

              {migrationStatus.needsMigration && (
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Found localhost URLs in the following areas:
                  </p>
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(migrationStatus.details).map(([key, count]) => (
                      count > 0 && (
                        <div key={key} className="flex justify-between text-sm">
                          <span className="capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                          <Badge variant="outline">{count}</Badge>
                        </div>
                      )
                    ))}
                  </div>
                </div>
              )}

              <Separator />

              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Migration Options
                </h4>

                <div className="grid gap-3">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Auto-migrate to Current Domain</p>
                      <p className="text-sm text-muted-foreground">
                        Update all URLs to use: {currentDomain}
                      </p>
                    </div>
                    <Button
                      onClick={() => runMigration('auto')}
                      disabled={migrating}
                      size="sm"
                    >
                      {migrating ? <RefreshCw className="h-4 w-4 animate-spin" /> : 'Migrate'}
                    </Button>
                  </div>

                  {portDetectionInfo && (
                    <div className="p-3 border rounded-lg space-y-3">
                      <div>
                        <p className="font-medium">Smart Port Migration</p>
                        <p className="text-sm text-muted-foreground">
                          Automatically detect and use the best port for migration
                        </p>
                      </div>

                      {portDetectionInfo.portDetection.detectedPorts.length > 0 && (
                        <div className="text-sm space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">Detected ports:</span>
                            {portDetectionInfo.portDetection.detectedPorts.map(port => (
                              <Badge key={port} variant="outline">{port}</Badge>
                            ))}
                          </div>
                          {portDetectionInfo.portDetection.mostCommonPort && (
                            <div className="flex items-center gap-2">
                              <span className="font-medium">Most common:</span>
                              <Badge variant="secondary">{portDetectionInfo.portDetection.mostCommonPort}</Badge>
                            </div>
                          )}
                          {portDetectionInfo.recommendations.hasConflict && (
                            <div className="flex items-center gap-2 text-yellow-600">
                              <AlertTriangle className="h-4 w-4" />
                              <span className="text-sm">
                                Detected port ({portDetectionInfo.portDetection.mostCommonPort}) differs from current port ({portDetectionInfo.currentPort})
                              </span>
                            </div>
                          )}
                        </div>
                      )}

                      <div className="flex items-center gap-2">
                        <Input
                          placeholder="Enter target domain (e.g., https://yourdomain.com)"
                          value={customDomain}
                          onChange={(e) => setCustomDomain(e.target.value)}
                          className="flex-1"
                        />
                        <Button
                          onClick={() => runMigration('smart-port', customDomain)}
                          disabled={migrating || !customDomain}
                          size="sm"
                        >
                          {migrating ? <RefreshCw className="h-4 w-4 animate-spin" /> : 'Smart Migrate'}
                        </Button>
                      </div>
                    </div>
                  )}

                  <div className="p-3 border rounded-lg space-y-3">
                    <div>
                      <p className="font-medium">Manual Port Migration</p>
                      <p className="text-sm text-muted-foreground">
                        Specify a custom port for the migration
                      </p>
                    </div>

                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="Target domain"
                        value={customDomain}
                        onChange={(e) => setCustomDomain(e.target.value)}
                        className="flex-1"
                      />
                      <Input
                        placeholder="Port"
                        value={manualPort}
                        onChange={(e) => setManualPort(e.target.value)}
                        className="w-20"
                        type="number"
                        min="1"
                        max="65535"
                      />
                      <Button
                        onClick={() => runMigration('with-port', customDomain, manualPort)}
                        disabled={migrating || !customDomain || !manualPort}
                        size="sm"
                      >
                        {migrating ? <RefreshCw className="h-4 w-4 animate-spin" /> : 'Migrate'}
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">Convert to Relative URLs</p>
                      <p className="text-sm text-muted-foreground">
                        Remove domain from URLs (most portable option)
                      </p>
                    </div>
                    <Button
                      onClick={() => runMigration('relative')}
                      disabled={migrating}
                      variant="outline"
                      size="sm"
                    >
                      {migrating ? <RefreshCw className="h-4 w-4 animate-spin" /> : 'Convert'}
                    </Button>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span>Loading migration status...</span>
            </div>
          )}

          <div className="pt-4">
            <Button
              onClick={() => {
                fetchMigrationStatus();
                fetchPortDetectionInfo();
              }}
              variant="outline"
              size="sm"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Status
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Current Configuration Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ExternalLink className="h-5 w-5" />
            Current Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Current Domain:</span>
              <p className="text-muted-foreground">{currentDomain}</p>
            </div>
            <div>
              <span className="font-medium">Protocol:</span>
              <p className="text-muted-foreground">{window.location.protocol}</p>
            </div>
            <div>
              <span className="font-medium">Auto-detect:</span>
              <p className="text-muted-foreground">{settings.autoDetect ? 'Enabled' : 'Disabled'}</p>
            </div>
            <div>
              <span className="font-medium">Force HTTPS:</span>
              <p className="text-muted-foreground">{settings.forceHttps ? 'Enabled' : 'Disabled'}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
