import { Request } from 'express';
import { storage } from '../storage-factory';

/**
 * URL utility functions for dynamic base URL handling
 */

interface UrlConfig {
  baseUrl?: string;
  autoDetect: boolean;
  forceHttps: boolean;
}

let cachedConfig: UrlConfig | null = null;
let configLastFetched = 0;
const CONFIG_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get URL configuration from database with caching
 */
async function getUrlConfig(): Promise<UrlConfig> {
  const now = Date.now();
  
  // Return cached config if still valid
  if (cachedConfig && (now - configLastFetched) < CONFIG_CACHE_TTL) {
    return cachedConfig;
  }

  try {
    const settings = await storage.getGeneralSettings();
    const urlSettings = settings?.urlSettings ? JSON.parse(settings.urlSettings) : {};
    
    cachedConfig = {
      baseUrl: urlSettings.baseUrl || process.env.BASE_URL,
      autoDetect: urlSettings.autoDetect !== false, // Default to true
      forceHttps: urlSettings.forceHttps || process.env.FORCE_HTTPS === 'true'
    };
    
    configLastFetched = now;
    return cachedConfig;
  } catch (error) {
    console.error('Error fetching URL config:', error);
    
    // Fallback configuration
    cachedConfig = {
      autoDetect: true,
      forceHttps: process.env.NODE_ENV === 'production'
    };
    
    configLastFetched = now;
    return cachedConfig;
  }
}

/**
 * Get the base URL from request headers
 */
function getBaseUrlFromRequest(req: Request): string {
  const protocol = req.get('x-forwarded-proto') || req.protocol;
  const host = req.get('x-forwarded-host') || req.get('host');
  return `${protocol}://${host}`;
}

/**
 * Get the current port from environment or default
 */
function getCurrentPort(): string {
  return process.env.PORT || '3001';
}

/**
 * Detect port from localhost URLs in the database
 */
export async function detectPortFromDatabase(): Promise<string | null> {
  try {
    const settings = await storage.getGeneralSettings();

    // Check various URL fields for localhost URLs with ports
    const urlFields = [settings?.logoUrl, settings?.faviconUrl].filter(Boolean);

    for (const url of urlFields) {
      if (url && isLocalhostUrl(url)) {
        try {
          const urlObj = new URL(url);
          if (urlObj.port) {
            return urlObj.port;
          }
        } catch {
          // Continue checking other URLs
        }
      }
    }

    // Check products
    const products = await storage.getProducts();
    for (const product of products) {
      if (product.imageUrl && isLocalhostUrl(product.imageUrl)) {
        try {
          const urlObj = new URL(product.imageUrl);
          if (urlObj.port) {
            return urlObj.port;
          }
        } catch {
          // Continue checking
        }
      }
    }

    return null;
  } catch (error) {
    console.error('Error detecting port from database:', error);
    return null;
  }
}

/**
 * Get the dynamic base URL based on configuration and request
 */
export async function getBaseUrl(req?: Request): Promise<string> {
  const config = await getUrlConfig();

  // If manual base URL is configured, use it
  if (config.baseUrl) {
    return config.baseUrl;
  }

  // If auto-detect is disabled and no manual URL, use environment variable or localhost
  if (!config.autoDetect) {
    const port = getCurrentPort();
    return process.env.BASE_URL || `http://localhost:${port}`;
  }

  // Auto-detect from request if available
  if (req) {
    let baseUrl = getBaseUrlFromRequest(req);

    // Force HTTPS if configured
    if (config.forceHttps && baseUrl.startsWith('http://')) {
      baseUrl = baseUrl.replace('http://', 'https://');
    }

    return baseUrl;
  }

  // Fallback to environment variable or localhost with current port
  const port = getCurrentPort();
  return process.env.BASE_URL || `http://localhost:${port}`;
}

/**
 * Convert a relative URL to an absolute URL
 */
export async function toAbsoluteUrl(relativePath: string, req?: Request): Promise<string> {
  const baseUrl = await getBaseUrl(req);
  
  // Remove leading slash if present to avoid double slashes
  const cleanPath = relativePath.startsWith('/') ? relativePath.slice(1) : relativePath;
  
  return `${baseUrl}/${cleanPath}`;
}

/**
 * Convert an absolute URL to use the current base URL
 */
export async function updateUrlToCurrentDomain(url: string, req?: Request): Promise<string> {
  if (!url) return url;
  
  // If it's already a relative URL, convert to absolute
  if (url.startsWith('/')) {
    return toAbsoluteUrl(url, req);
  }
  
  // If it's not an HTTP(S) URL, return as-is
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return url;
  }
  
  try {
    const urlObj = new URL(url);
    const currentBaseUrl = await getBaseUrl(req);
    const currentUrlObj = new URL(currentBaseUrl);
    
    // Update the protocol and host to match current domain
    urlObj.protocol = currentUrlObj.protocol;
    urlObj.host = currentUrlObj.host;
    
    return urlObj.toString();
  } catch (error) {
    console.error('Error updating URL to current domain:', error);
    return url; // Return original URL if parsing fails
  }
}

/**
 * Check if a URL is from localhost
 */
export function isLocalhostUrl(url: string): boolean {
  if (!url) return false;

  try {
    const urlObj = new URL(url);
    return urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1';
  } catch {
    return false;
  }
}

/**
 * Extract port from a localhost URL
 */
export function extractPortFromUrl(url: string): string | null {
  if (!url || !isLocalhostUrl(url)) return null;

  try {
    const urlObj = new URL(url);
    return urlObj.port || null;
  } catch {
    return null;
  }
}

/**
 * Smart URL migration that preserves paths and handles ports intelligently
 */
export async function smartUrlMigration(
  url: string,
  targetDomain: string,
  preservePort: boolean = false
): Promise<string> {
  if (!url) return url;

  // If it's already a relative URL, convert to absolute with target domain
  if (url.startsWith('/')) {
    return `${targetDomain}${url}`;
  }

  // If it's not an HTTP(S) URL, return as-is
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return url;
  }

  try {
    const urlObj = new URL(url);
    const targetObj = new URL(targetDomain);

    // Update protocol and hostname
    urlObj.protocol = targetObj.protocol;
    urlObj.hostname = targetObj.hostname;

    // Handle port logic
    if (preservePort && urlObj.port) {
      // Keep the original port if preservePort is true
      urlObj.port = urlObj.port;
    } else if (targetObj.port) {
      // Use target domain's port
      urlObj.port = targetObj.port;
    } else {
      // Remove port for standard protocols
      urlObj.port = '';
    }

    return urlObj.toString();
  } catch (error) {
    console.error('Error in smart URL migration:', error);
    return url; // Return original URL if parsing fails
  }
}

/**
 * Clear the URL configuration cache (useful for testing or when settings change)
 */
export function clearUrlConfigCache(): void {
  cachedConfig = null;
  configLastFetched = 0;
}

/**
 * Middleware to add base URL utilities to request object
 */
export function urlUtilsMiddleware(req: Request, res: any, next: any) {
  req.getBaseUrl = () => getBaseUrl(req);
  req.toAbsoluteUrl = (path: string) => toAbsoluteUrl(path, req);
  req.updateUrlToCurrentDomain = (url: string) => updateUrlToCurrentDomain(url, req);
  next();
}

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      getBaseUrl?: () => Promise<string>;
      toAbsoluteUrl?: (path: string) => Promise<string>;
      updateUrlToCurrentDomain?: (url: string) => Promise<string>;
    }
  }
}
