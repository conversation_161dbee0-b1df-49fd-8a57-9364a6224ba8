/**
 * Embed Code Configuration Types
 * This file defines the structure for external payment platform embed codes
 * like BillGang.io, SellPass, etc.
 */

export interface EmbedCode {
  id: string;
  name: string;
  description: string;
  platform: string; // e.g., 'billgang', 'sellpass', 'custom'
  headScript: string; // Script to be added to <head>
  buttonHtml: string; // HTML code for the button/embed
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface EmbedCodeConfig {
  codes: EmbedCode[];
  rotationMethod: 'round-robin' | 'random' | 'sequential';
  lastUsedIndex: number;
}

// Default embed code templates for popular platforms
export const EMBED_CODE_TEMPLATES = {
  billgang: {
    name: 'BillGang.io',
    platform: 'billgang',
    headScript: '<script src="https://platform.billgang.com/embed.js"></script>',
    buttonHtml: `<button
  data-billgang-product-path="your-product-path"
  data-billgang-domain="your-domain.bgng.io">
  Purchase
</button>`,
    description: 'BillGang.io payment embed code'
  },
  sellpass: {
    name: 'SellPass',
    platform: 'sellpass',
    headScript: '<script src="https://embed.sellpass.io/embed.js"></script>',
    buttonHtml: `<div 
  data-sellpass-product="your-product-id"
  data-sellpass-domain="your-domain">
  <button>Buy Now</button>
</div>`,
    description: 'SellPass payment embed code'
  },
  custom: {
    name: 'Custom Embed',
    platform: 'custom',
    headScript: '<!-- Add your custom head script here -->',
    buttonHtml: '<!-- Add your custom button HTML here -->',
    description: 'Custom payment embed code'
  }
};

export type EmbedCodeTemplate = keyof typeof EMBED_CODE_TEMPLATES;
