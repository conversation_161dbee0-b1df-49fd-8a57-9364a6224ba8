const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// Read the migration file
const migrationPath = path.join(__dirname, '..', 'migrations', '0000_unusual_lester.sql');
const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

// Connect to the database
const dbPath = path.join(__dirname, '..', 'app', 'data', 'data.db');
const db = new Database(dbPath);

console.log('🔄 Applying database migration...');

try {
  // Split the migration into individual statements
  const statements = migrationSQL
    .split('--> statement-breakpoint')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0);

  console.log(`📝 Found ${statements.length} SQL statements to execute`);

  // Execute each statement
  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i];
    if (statement.length > 0) {
      try {
        console.log(`⚡ Executing statement ${i + 1}/${statements.length}`);
        db.exec(statement);
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`⚠️ Table/index already exists, skipping: ${error.message}`);
        } else {
          console.error(`❌ Error executing statement ${i + 1}:`, error.message);
          console.error(`Statement: ${statement.substring(0, 100)}...`);
        }
      }
    }
  }

  console.log('✅ Migration completed successfully!');
  
  // Verify tables were created
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  console.log(`📊 Database now contains ${tables.length} tables:`);
  tables.forEach(table => console.log(`  - ${table.name}`));

} catch (error) {
  console.error('❌ Migration failed:', error);
} finally {
  db.close();
}
