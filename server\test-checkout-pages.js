import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize database - use the same path as the app
const dbPath = process.env.DATABASE_PATH || 'data.db';
console.log('Database path:', dbPath);

try {
  const db = new Database(dbPath);
  
  // Test simple query first
  console.log('Testing simple query on custom_checkout_pages...');
  const allPages = db.prepare("SELECT * FROM custom_checkout_pages LIMIT 5").all();
  console.log('Simple query result:', allPages);
  
  // Check the data types of boolean fields
  console.log('\nChecking boolean fields...');
  const booleanFields = db.prepare("SELECT id, require_allowed_email, is_trial_checkout FROM custom_checkout_pages").all();
  console.log('Boolean fields:', booleanFields);
  
  // Check if there are any non-integer values
  booleanFields.forEach((row, index) => {
    console.log(`Row ${index + 1}:`);
    console.log(`  require_allowed_email: ${row.require_allowed_email} (type: ${typeof row.require_allowed_email})`);
    console.log(`  is_trial_checkout: ${row.is_trial_checkout} (type: ${typeof row.is_trial_checkout})`);
  });
  
  db.close();
} catch (error) {
  console.error('Error testing custom checkout pages:', error);
}
