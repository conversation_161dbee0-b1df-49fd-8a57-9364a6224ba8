import React, { useEffect, useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useLocation, Link } from 'wouter';
import { <PERSON>, CardContent, CardHeader, CardT<PERSON>le, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { useAdminAuth } from '@/hooks/use-admin-auth';
import { Loader2, Shield } from 'lucide-react';
import TwoFactorAuth from '@/components/admin/TwoFactorAuth';
import PasswordChangeForm from '@/components/admin/PasswordChangeForm';
import { useToast } from '@/hooks/use-toast';

// Validation schema
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().default(false),
  turnstileToken: z.string().optional(),
});

type LoginFormData = z.infer<typeof loginSchema>;

// Cloudflare Turnstile configuration
const TURNSTILE_SITE_KEY = '0x4AAAAAAAkqiE_JYWMwJGzh'; // Test site key - replace with your actual site key

export default function AdminLogin() {
  const [, setLocation] = useLocation();
  const { login, isAuthenticated, isLoading, isPending, authState } = useAdminAuth();
  const [showTwoFactor, setShowTwoFactor] = useState(false);
  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [turnstileLoaded, setTurnstileLoaded] = useState(false);
  const [turnstileToken, setTurnstileToken] = useState<string>('');
  const [rateLimitError, setRateLimitError] = useState<string>('');
  const turnstileRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Form setup
  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false,
      turnstileToken: '',
    },
  });

  // Check for access token in URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const accessToken = urlParams.get('access');

    if (accessToken && !isAuthenticated) {
      // Automatically login with access token
      login({ accessToken, rememberMe: false });

      // Clean the URL to hide the token
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [login, isAuthenticated]);

  // Load Cloudflare Turnstile script
  useEffect(() => {
    const loadTurnstile = () => {
      if (window.turnstile) {
        setTurnstileLoaded(true);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        setTurnstileLoaded(true);
      };
      script.onerror = () => {
        console.error('Failed to load Turnstile script');
        toast({
          title: "Security verification unavailable",
          description: "Please refresh the page and try again.",
          variant: "destructive"
        });
      };
      document.head.appendChild(script);
    };

    loadTurnstile();
  }, [toast]);

  // Initialize Turnstile widget when loaded
  useEffect(() => {
    if (turnstileLoaded && turnstileRef.current && window.turnstile) {
      const widgetId = window.turnstile.render(turnstileRef.current, {
        sitekey: TURNSTILE_SITE_KEY,
        callback: (token: string) => {
          setTurnstileToken(token);
          form.setValue('turnstileToken', token);
        },
        'error-callback': () => {
          setTurnstileToken('');
          form.setValue('turnstileToken', '');
          toast({
            title: "Security verification failed",
            description: "Please try again.",
            variant: "destructive"
          });
        },
        'expired-callback': () => {
          setTurnstileToken('');
          form.setValue('turnstileToken', '');
        },
        theme: 'light',
        size: 'normal'
      });

      return () => {
        if (window.turnstile && widgetId) {
          window.turnstile.remove(widgetId);
        }
      };
    }
  }, [turnstileLoaded, form, toast]);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      // Small delay to ensure proper state synchronization
      setTimeout(() => {
        setLocation('/admin/dashboard');
      }, 50);
    }

    // Show 2FA form if required
    if (authState?.requiresTwoFactor) {
      setShowTwoFactor(true);
    }
  }, [isAuthenticated, isLoading, authState, setLocation]);

  // Submit handler
  const onSubmit = async (data: LoginFormData) => {
    // Clear any previous rate limit errors
    setRateLimitError('');

    // Validate Turnstile token
    if (!turnstileToken) {
      toast({
        title: "Security verification required",
        description: "Please complete the security verification.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Include Turnstile token in login data
      const loginData = {
        ...data,
        turnstileToken
      };

      const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData),
        credentials: 'include',
      });

      const result = await response.json();

      if (!response.ok) {
        // Handle rate limiting errors
        if (response.status === 429) {
          setRateLimitError(result.error || 'Too many failed attempts. Please try again later.');
          return;
        }
        throw new Error(result.message || 'Login failed');
      }

      // Check if password change is required
      if (result.requiresPasswordChange) {
        setShowPasswordChange(true);
        return;
      }

      // Check if 2FA is required
      if (result.requiresTwoFactor) {
        setShowTwoFactor(true);
        return;
      }

      // Successful login
      toast({
        title: "Login successful",
        description: "Welcome to the admin dashboard",
      });

      // Redirect to dashboard
      setTimeout(() => {
        setLocation('/admin/dashboard');
      }, 50);

    } catch (error) {
      console.error('Login error:', error);
      toast({
        title: "Login failed",
        description: error instanceof Error ? error.message : "Invalid credentials",
        variant: "destructive"
      });
    }
  };

  // Handle 2FA success
  const handleTwoFactorSuccess = () => {
    console.log("2FA verification successful, redirecting to dashboard...");

    try {
      // Try to refresh auth state, but don't block if it fails
      if (checkAuth) {
        checkAuth();
      }
    } catch (e) {
      console.error("Error refreshing auth state:", e);
    }

    // The redirect is now handled directly in the TwoFactorAuth component
    // This is just a backup in case the direct redirect fails
    setTimeout(() => {
      window.location.href = '/admin/dashboard';
    }, 1500);
  };

  // Handle 2FA cancel
  const handleTwoFactorCancel = () => {
    setShowTwoFactor(false);
  };

  // Handle password change success
  const handlePasswordChangeSuccess = () => {
    setShowPasswordChange(false);
    // The PasswordChangeForm component handles the redirect
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-slate-50">
      {showPasswordChange ? (
        <PasswordChangeForm onSuccess={handlePasswordChangeSuccess} />
      ) : showTwoFactor ? (
        <TwoFactorAuth
          onSuccess={handleTwoFactorSuccess}
          onCancel={handleTwoFactorCancel}
        />
      ) : (
        <Card className="w-full max-w-md shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="text-2xl text-center">Admin Login</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input placeholder="admin" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="••••••••" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="rememberMe"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Remember me
                        </FormLabel>
                        <FormDescription>
                          Stay logged in for 30 days
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                {/* Cloudflare Turnstile */}
                <div className="space-y-2">
                  <FormLabel className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Security Verification
                  </FormLabel>
                  <div className="flex justify-center">
                    <div ref={turnstileRef} />
                  </div>
                  {!turnstileLoaded && (
                    <div className="text-center text-sm text-muted-foreground">
                      Loading security verification...
                    </div>
                  )}
                </div>

                {/* Rate limit error display */}
                {rateLimitError && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <div className="flex items-center gap-2 text-red-800">
                      <Shield className="h-4 w-4" />
                      <span className="text-sm font-medium">Login Temporarily Blocked</span>
                    </div>
                    <p className="text-sm text-red-700 mt-1">{rateLimitError}</p>
                  </div>
                )}

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isPending || !turnstileToken}
                >
                  {isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Logging in...
                    </>
                  ) : "Log In"}
                </Button>
              </form>
            </Form>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Link href="/admin/forgot-password" className="text-sm text-blue-600 hover:text-blue-800">
              Forgot Password?
            </Link>
          </CardFooter>
        </Card>
      )}
    </div>
  );
}