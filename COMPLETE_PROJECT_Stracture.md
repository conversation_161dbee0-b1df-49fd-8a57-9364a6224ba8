# Complete Project Recreation Prompt: Niraza E-commerce Platform

## Project Overview
Create a full-stack e-commerce platform called "Niraza" - a comprehensive business management system with advanced features for product sales, customer management, payment processing, and administrative controls.

## Tech Stack & Architecture

### Backend (Node.js/Express)
- **Framework**: Express.js with TypeScript
- **Database**: Multi-database support (SQLite, MySQL, PostgreSQL) using Drizzle ORM
- **Session Management**: express-session with memorystore
- **Authentication**: Custom auth with 2FA (TOTP), device management, recovery codes
- **File Uploads**: Multer with 5MB limit, supports JPEG/PNG/GIF/WebP
- **Email**: Nodemailer + SendGrid integration with SMTP provider management
- **Security**: SHA256 password hashing, security headers, bot blocking
- **Process Management**: PM2 for production deployment
- **Additional Tools**: Puppeteer for browser automation, QR code generation

### Frontend (React/Vite)
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Routing**: Wouter
- **State Management**: TanStack Query (React Query)
- **UI Components**: Radix UI + Tailwind CSS
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Styling**: PostCSS, Tailwind CSS

### Database Schema (16 Core Tables)

#### Authentication & Security:
1. **users** - Admin accounts with 2FA support
   - Fields: id, username, password, twoFactorSecret, twoFactorEnabled, createdAt, updatedAt
2. **devices** - Device tracking for security
   - Fields: id, userId, name, ip, userAgent, lastLogin, createdAt
3. **recovery_codes** - 2FA backup codes
   - Fields: id, userId, code, used, createdAt, usedAt

#### E-commerce Core:
4. **products** - Product catalog with payment methods
   - Fields: id, name, description, price, imageUrl, active, paymentMethod, customPaymentLinkId, trialCustomPaymentLinkId, embedCodeId
5. **invoices** - Order management with trial/upgrade tracking
   - Fields: id, customerName, customerEmail, productId, amount, status, paypalInvoiceId, paypalInvoiceUrl, isTrialOrder, hasUpgraded, upgradedAt, createdAt, customCheckoutPageId, country
6. **custom_checkout_pages** - Dynamic checkout pages with analytics
   - Fields: id, title, slug, productName, productDescription, price, imageUrl, paymentMethod, customPaymentLinkId, paypalButtonId, trialCustomPaymentLinkId, trialPaypalButtonId, embedCodeId, smtpProviderId, requireAllowedEmail, isTrialCheckout, confirmationMessage, headerTitle, footerText, headerLogo, footerLogo, themeMode, useReferrerMasking, redirectDelay, expiresAt, active, views, conversions, createdAt, updatedAt

#### Communication & Templates:
7. **allowed_emails** - Email whitelist management
   - Fields: id, email, notes, lastSubject, smtpProvider, lastUpdated, createdAt
8. **email_templates** - Customizable email templates
   - Fields: id, templateId, name, description, subject, htmlContent, textContent, content, category, isDefault, createdAt, updatedAt

#### Payment & Financial:
10. **paypal_buttons** - PayPal integration management
    - Fields: id, name, buttonCode, description, active, createdAt, updatedAt
11. **custom_invoices** - Custom invoice generation
    - Fields: id, invoiceNumber, customerName, customerEmail, amount, currency, description, paypalButtonId, status, dueDate, viewCount, paidAt, createdAt, updatedAt
12. **custom_payment_links** - Custom payment link management
    - Fields: id, name, paymentLink, buttonText, successRedirectUrl, active, isTrialLink, createdAt, updatedAt

#### Configuration & System:
13. **general_settings** - Site configuration
    - Fields: id, siteName, siteDescription, logoUrl, faviconUrl, primaryColor, secondaryColor, footerText, enableCheckout, enableCustomCheckout, enableTestMode, defaultTestCustomerEnabled, defaultTestCustomerName, defaultTestCustomerEmail, emailDomainRestrictionEnabled, emailDomainRestrictionDomains, seoPrivacySettings, telegramBotSettings, urlSettings, createdAt, updatedAt
14. **homepage_config** - Dynamic homepage builder
    - Fields: id, sectionsData, seoSettings, themeSettings, version, createdAt, updatedAt
15. **system_messages** - System-wide messaging
    - Fields: id, messageId, category, name, description, content, isHtml, createdAt, updatedAt
16. **smtp_providers** - Multiple SMTP provider support
    - Fields: id, name, host, port, secure, authUser, authPass, fromEmail, fromName, adminEmail, active, isDefault, isBackup, createdAt, updatedAt

## Core Features

### 1. Multi-Database Support
- Conditional schema generation for SQLite, MySQL, PostgreSQL
- Environment-based database selection via DATABASE_URL
- Drizzle ORM with type-safe queries
- Database factory pattern for storage abstraction

### 2. Advanced Authentication System
- Username/password with SHA256 hashing (recommend upgrading to bcrypt)
- Two-Factor Authentication (TOTP) with QR codes using otplib
- Device tracking and management with IP and User-Agent logging
- Recovery codes system (10 codes per user)
- Password reset with email tokens and expiration
- Session management with secure cookies and proxy trust
- Admin-only routes with middleware protection

### 3. Product & Order Management
- Product catalog with images, descriptions, and pricing
- Multiple payment methods: manual, PayPal, custom links
- Trial orders with upgrade tracking and conversion analytics
- Custom checkout pages with unique slugs and branding
- Order analytics: views, conversions, revenue tracking
- Invoice generation with PayPal integration
- Country-based order tracking

### 4. Advanced Email System
- Multiple SMTP provider support with primary/backup configuration
- Customizable email templates with HTML/text versions
- Template categories: general, payment, notification, marketing
- Email domain restrictions with whitelist management
- Variable substitution in templates
- Email sending with provider failover
- SendGrid and Nodemailer integration

### 5. Custom Checkout Pages
- Dynamic checkout page creation with drag-and-drop builder
- Custom branding: logos, colors, themes (light/dark)
- Referrer masking and configurable redirect delays
- Analytics tracking: page views and conversion rates
- Expiration dates and activation controls
- Trial vs regular checkout modes
- Embedded payment forms and custom success messages
- SMTP provider selection per checkout page

### 6. Payment Integration
- PayPal button management with custom HTML code
- Custom payment link creation and management
- Trial payment links with separate configuration
- Payment confirmation handling and status tracking
- Success/cancel page redirects with custom URLs
- Invoice numbering and payment tracking
- Multi-currency support (default USD)

### 7. Administrative Dashboard
- Comprehensive admin panel with role-based access
- Real-time statistics: revenue, orders, products, customers
- Order management with status tracking and filtering
- Customer inquiry system with status management
- File upload management with image optimization
- System monitoring with performance metrics
- User device management and security monitoring

### 8. System Monitoring & Alerts
- CPU, memory, and disk usage monitoring
- Error logging with categorization (error, warning, critical)
- Telegram bot integration for real-time notifications
- System health checks and uptime monitoring
- Performance metrics collection and alerting
- Configurable alert thresholds
- Automatic error capture for uncaught exceptions

### 9. Telegram Bot Integration
- Order notifications with customer details
- Payment confirmations and status updates
- Trial upgrade alerts and conversion tracking
- M3U credential management for IPTV services
- Admin-only verification with chat ID validation
- Rate limiting and audit logging
- Webhook support for real-time updates
- Custom notification templates

### 10. SEO & Privacy Controls
- Dynamic meta tags and SEO settings per page
- Privacy headers: CSP, HSTS, X-Frame-Options, X-XSS-Protection
- Bot blocking with User-Agent detection
- Crawler management and robots.txt generation
- Content indexing controls (noindex, nofollow)
- Server header masking for security
- Referrer policy configuration

### 11. Homepage Builder
- Dynamic section management with JSON configuration
- Theme customization with color schemes
- SEO settings per page with meta tags
- Version control for homepage configurations
- Drag-and-drop interface for content management
- Responsive design with mobile optimization

### 12. Security Features
- Comprehensive security headers middleware
- Bot detection and blocking by User-Agent
- IP tracking and device fingerprinting
- Session security with proxy trust configuration
- File upload validation with MIME type checking
- CORS configuration with credential support
- Rate limiting (recommended implementation)
- CSRF protection (recommended implementation)

### 13. File Management System
- Secure file upload with Multer
- Image validation: JPEG, PNG, GIF, WebP support
- 5MB file size limit with configurable settings
- Timestamp-based filename generation
- File serving with absolute URL generation
- Upload directory management
- Image optimization and resizing capabilities

### 14. Contact & Inquiry System
- Customer inquiry form with validation
- Status tracking: new, in-progress, resolved
- Admin notes and response management
- SMTP provider selection for responses
- Checkout page association for context
- Email notification system for new inquiries

## Environment Configuration

### Required Environment Variables:
```env
# Database Configuration
DATABASE_URL=sqlite:./database.db

# Server Configuration
NODE_ENV=production
PORT=3001

# Security Configuration
SESSION_SECRET=your-strong-session-secret-here
SECURE_COOKIES=true
ADMIN_ACCESS_TOKEN=your-admin-access-token

# Domain Configuration
BASE_URL=https://your-domain.com
FORCE_HTTPS=true

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
```

### Optional Email Configuration:
```env
# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Email Settings
FROM_EMAIL=<EMAIL>
FROM_NAME=Your Site Name
ADMIN_EMAIL=<EMAIL>

# SendGrid Configuration (Optional)
SENDGRID_API_KEY=your-sendgrid-api-key
```

### Optional Database Backup:
```env
# Database Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=24
BACKUP_RETENTION=7
BACKUP_PATH=./backups
```

## API Endpoints Structure

### Authentication Routes (`/api/auth`):
- `POST /login` - Admin login with 2FA support
- `POST /logout` - Session logout and cleanup
- `POST /setup-2fa` - Two-factor authentication setup
- `POST /verify-2fa` - 2FA token verification
- `POST /generate-recovery-codes` - Generate new recovery codes
- `POST /forgot-password` - Password reset request
- `POST /reset-password` - Password reset confirmation
- `GET /session` - Current session information

### Admin Management (`/api/admin`):
- `GET /stats` - Dashboard statistics and analytics
- `GET /users` - User management
- `GET /devices` - Device tracking and management
- `POST /test-data` - Generate test data for development
- `GET /orders` - Order management and filtering
- `GET /trial-orders` - Trial order tracking
- `PUT /orders/:id` - Update order status

### Configuration Management (`/api/admin-config`):
- `GET/POST /email-config` - SMTP provider management
- `DELETE /email-config/:id` - Remove SMTP provider
- `GET/POST /payment-config` - Payment configuration
- `POST /payment-config/custom-link/add` - Add custom payment link
- `POST /payment-config/trial-custom-link/add` - Add trial payment link
- `PUT /payment-config/custom-link/:id` - Update payment link
- `DELETE /payment-config/custom-link/:id` - Remove payment link

### Product Management (`/api/products`):
- `GET /` - List all products
- `POST /` - Create new product
- `PUT /:id` - Update product
- `DELETE /:id` - Delete product
- `POST /:id/upload-image` - Upload product image

### Checkout & Orders (`/api/checkout`):
- `POST /` - Create new order
- `GET /:slug` - Get custom checkout page
- `POST /custom` - Process custom checkout
- `GET /success` - Payment success handling
- `GET /cancel` - Payment cancellation handling

### Custom Checkout Pages (`/api/custom-checkout`):
- `GET /` - List all custom checkout pages
- `POST /` - Create new checkout page
- `PUT /:id` - Update checkout page
- `DELETE /:id` - Delete checkout page
- `GET /:slug` - Get checkout page by slug
- `POST /:id/analytics` - Track page analytics

### Email Management (`/api/email-templates`):
- `GET /` - List all email templates
- `POST /` - Create new template
- `PUT /:id` - Update template
- `DELETE /:id` - Delete template
- `POST /send` - Send email using template
- `POST /test` - Test email sending

### System Monitoring (`/api/system-monitor`):
- `GET /metrics` - Current system metrics
- `GET /health` - Health check endpoint
- `GET /logs` - System error logs
- `POST /test-alert` - Test alert system

### Telegram Integration (`/api/telegram`):
- `GET /config` - Get Telegram bot configuration
- `PUT /config` - Update bot configuration
- `POST /test-connection` - Test bot connection
- `POST /set-webhook` - Configure webhook
- `POST /test-notification` - Send test notification

### File Management (`/api/upload`):
- `POST /image` - Upload image file
- `POST /image-base64` - Upload base64 encoded image
- `GET /list` - List uploaded files
- `DELETE /:filename` - Delete uploaded file

### Contact System (`/api/contact`):
- `POST /` - Submit contact inquiry
- `GET /inquiries` - List all inquiries (admin)
- `PUT /inquiries/:id` - Update inquiry status
- `DELETE /inquiries/:id` - Delete inquiry

### Settings Management:
- `GET/PUT /api/general-settings` - General site settings
- `GET/PUT /api/url-settings` - URL and domain settings
- `GET/PUT /api/seo-privacy-settings` - SEO and privacy controls
- `GET/PUT /api/homepage` - Homepage configuration
- `GET/PUT /api/system-messages` - System message management

## File Structure
```
project/
├── client/                          # Frontend React application
│   ├── src/
│   │   ├── components/
│   │   │   ├── admin/              # Admin-specific components
│   │   │   │   ├── AdminLayout.tsx
│   │   │   │   ├── AdminRoute.tsx
│   │   │   │   ├── TestDataCard.tsx
│   │   │   │   └── ...
│   │   │   └── ui/                 # Reusable UI components
│   │   │       ├── button.tsx
│   │   │       ├── card.tsx
│   │   │       ├── form.tsx
│   │   │       └── ...
│   │   ├── pages/
│   │   │   ├── admin/              # Admin pages
│   │   │   │   ├── Dashboard.tsx
│   │   │   │   ├── Products.tsx
│   │   │   │   ├── Settings.tsx
│   │   │   │   ├── EmailSettings.tsx
│   │   │   │   ├── PaymentSettings.tsx
│   │   │   │   ├── SecuritySettings.tsx
│   │   │   │   ├── SystemMonitoring.tsx
│   │   │   │   └── ...
│   │   │   ├── Home.tsx            # Public homepage
│   │   │   ├── Checkout.tsx        # Checkout pages
│   │   │   ├── AdminLogin.tsx      # Admin authentication
│   │   │   └── ...
│   │   ├── hooks/                  # Custom React hooks
│   │   │   ├── use-error-dialog.tsx
│   │   │   ├── use-confirmation-dialog.tsx
│   │   │   └── use-system-messages.tsx
│   │   ├── lib/                    # Utility libraries
│   │   │   ├── queryClient.ts
│   │   │   ├── email-validator.ts
│   │   │   └── utils.ts
│   │   ├── api/                    # API client functions
│   │   ├── App.tsx                 # Main application component
│   │   └── main.tsx               # Application entry point
│   ├── index.html
│   ├── package.json
│   └── vite.config.ts
├── server/                          # Backend Express application
│   ├── routes/                     # API route handlers
│   │   ├── admin-config.ts         # Admin configuration
│   │   ├── admin.ts               # Admin management
│   │   ├── auth.ts                # Authentication
│   │   ├── custom-checkout.ts     # Custom checkout pages
│   │   ├── email-templates.ts     # Email template management
│   │   ├── system-monitor.ts      # System monitoring
│   │   ├── telegram.ts            # Telegram integration
│   │   ├── upload.ts              # File upload handling
│   │   └── ...
│   ├── services/                   # Business logic services
│   │   ├── email.ts               # Email service
│   │   ├── system-monitor.ts      # Monitoring service
│   │   ├── telegram-bot.ts        # Telegram bot service
│   │   └── custom-link.ts         # Payment link service
│   ├── middleware/                 # Express middleware
│   │   ├── auth.ts                # Authentication middleware
│   │   └── privacy.ts             # Security headers middleware
│   ├── utils/                      # Utility functions
│   │   ├── url-utils.ts           # URL handling utilities
│   │   ├── invoice-utils.ts       # Invoice generation
│   │   └── excel-export.ts        # Data export utilities
│   ├── migrations/                 # Database migrations
│   ├── database-storage.ts         # Database operations
│   ├── storage-factory.ts          # Storage abstraction
│   ├── storage-interface.ts        # Storage interface definition
│   ├── index.ts                    # Server entry point
│   ├── routes.ts                   # Route registration
│   └── vite.ts                     # Vite development setup
├── shared/                          # Shared code between client/server
│   ├── schema.ts                   # Database schema definitions
│   ├── types.ts                    # TypeScript type definitions
│   ├── email-validator.ts          # Email validation logic
│   ├── email-templates.ts          # Default email templates
│   ├── system-messages.ts          # System message definitions
│   └── embed-codes.ts             # Embed code templates
├── uploads/                         # File upload directory
├── package.json                     # Project dependencies
├── .env                            # Environment configuration
├── drizzle.config.ts               # Database configuration
├── ecosystem.config.js             # PM2 configuration
├── deploy.sh                       # Deployment script
├── health-check.js                 # Health check script
└── README.md                       # Project documentation
```

## Key Implementation Details

### 1. Database Abstraction Layer
```typescript
// Conditional table definitions based on DATABASE_URL
const isSQLite = process.env.DATABASE_URL?.startsWith('sqlite:') ?? false;
const isMySQL = process.env.DATABASE_URL?.startsWith('mysql:') ?? false;

// Example table definition with multi-database support
export const users = isSQLite
  ? sqliteTable("users", { /* SQLite schema */ })
  : isMySQL
  ? mysqlTable("users", { /* MySQL schema */ })
  : pgTable("users", { /* PostgreSQL schema */ });
```

### 2. Security Implementation
```typescript
// Password hashing (current - recommend upgrading)
const hashedPassword = crypto.createHash('sha256').update(password).digest('hex');

// 2FA implementation with TOTP
const secret = authenticator.generateSecret();
const qrCodeUrl = authenticator.keyuri(username, 'YourApp', secret);

// Device tracking
const deviceId = crypto.randomBytes(16).toString('hex');
const deviceInfo = {
  id: deviceId,
  name: req.headers['user-agent'],
  ip: req.ip,
  userAgent: req.headers['user-agent'],
  lastLogin: new Date().toISOString()
};
```

### 3. Email System Architecture
```typescript
// SMTP provider management with failover
class EmailService {
  async sendEmail(to: string, subject: string, content: string, providerId?: string) {
    const provider = providerId 
      ? await this.getProvider(providerId)
      : await this.getDefaultProvider();
    
    if (!provider && !providerId) {
      provider = await this.getBackupProvider();
    }
    
    return this.sendWithProvider(provider, to, subject, content);
  }
}
```

### 4. Custom Checkout Page System
```typescript
// Dynamic checkout page rendering
interface CheckoutPageConfig {
  title: string;
  slug: string;
  productName: string;
  productDescription: string;
  price: string;
  themeMode: 'light' | 'dark';
  headerLogo?: string;
  footerLogo?: string;
  customPaymentLinkId?: string;
  paypalButtonId?: string;
  confirmationMessage?: string;
  redirectDelay: number;
  useReferrerMasking: boolean;
}
```

### 5. System Monitoring Implementation
```typescript
// System metrics collection
interface SystemMetrics {
  cpu: { usage: number; loadAverage: number[]; };
  memory: { total: number; used: number; free: number; percentage: number; };
  disk: { total: number; used: number; free: number; percentage: number; };
  uptime: number;
  timestamp: string;
}

// Alert thresholds
const alertThresholds = {
  cpu: 80,        // CPU usage percentage
  memory: 85,     // Memory usage percentage
  disk: 90,       // Disk usage percentage
  responseTime: 5000  // Response time in ms
};
```

### 6. Telegram Bot Integration
```typescript
// Telegram notification system
class TelegramBot {
  async sendOrderNotification(order: Order) {
    const message = `
🛒 New Order #${order.id}
👤 Customer: ${order.customerName}
📧 Email: ${order.customerEmail}
💰 Amount: $${order.amount}
📅 Date: ${order.createdAt}
    `;
    
    return this.sendMessage(message);
  }
}
```

## Deployment Configuration

### PM2 Ecosystem Configuration
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'niraza-site',
    script: 'server/index.js',
    cwd: '/home/<USER>/htdocs/niraza.site',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    instances: 1,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '1G',
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name niraza.site www.niraza.site;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### Database Backup Script
```bash
#!/bin/bash
# backup-database.sh
BACKUP_DIR="./backups"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/database_backup_$DATE.db"

mkdir -p $BACKUP_DIR
cp ./data.db $BACKUP_FILE

# Keep only last 7 backups
ls -t $BACKUP_DIR/database_backup_*.db | tail -n +8 | xargs -r rm

echo "Database backup created: $BACKUP_FILE"
```

## Security Recommendations

### Immediate Security Improvements:
1. **Password Hashing**: Replace SHA256 with bcrypt
   ```typescript
   import bcrypt from 'bcrypt';
   const hashedPassword = await bcrypt.hash(password, 12);
   ```

2. **Rate Limiting**: Implement express-rate-limit
   ```typescript
   import rateLimit from 'express-rate-limit';
   const limiter = rateLimit({
     windowMs: 15 * 60 * 1000, // 15 minutes
     max: 100 // limit each IP to 100 requests per windowMs
   });
   ```

3. **CSRF Protection**: Add csurf middleware
   ```typescript
   import csrf from 'csurf';
   app.use(csrf({ cookie: true }));
   ```

4. **Input Validation**: Enhance Zod schemas
   ```typescript
   const userSchema = z.object({
     username: z.string().min(3).max(50).regex(/^[a-zA-Z0-9_]+$/),
     password: z.string().min(8).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
   });
   ```

### Environment Security:
- Use strong, randomly generated secrets
- Enable HTTPS in production
- Configure proper CORS origins
- Set secure session cookies
- Implement proper error handling
- Add request logging and monitoring

## Testing Strategy

### Unit Tests:
- Database operations
- Authentication functions
- Email service
- Payment processing
- Validation schemas

### Integration Tests:
- API endpoints
- Authentication flow
- Payment processing
- Email sending
- File uploads

### E2E Tests:
- User registration/login
- Product creation/management
- Checkout process
- Admin dashboard
- Payment flow

## Performance Optimization

### Database Optimization:
- Add proper indexes
- Implement query optimization
- Use connection pooling
- Add caching layer (Redis)

### Frontend Optimization:
- Code splitting
- Lazy loading
- Image optimization
- Bundle analysis
- CDN integration

### Backend Optimization:
- Response compression
- Static file caching
- API response caching
- Database query optimization

## Monitoring & Analytics

### Application Monitoring:
- Error tracking (Sentry)
- Performance monitoring (New Relic)
- Uptime monitoring (Pingdom)
- Log aggregation (ELK Stack)

### Business Analytics:
- Order conversion tracking
- Customer behavior analysis
- Revenue analytics
- A/B testing framework

## Additional Features to Implement

### Advanced Features:
1. **Multi-language Support**: i18n implementation
2. **Advanced Analytics**: Custom dashboard with charts
3. **Inventory Management**: Stock tracking and alerts
4. **Customer Portal**: Self-service customer area
5. **Advanced Email Marketing**: Drip campaigns and automation
6. **API Documentation**: Swagger/OpenAPI integration
7. **Webhook System**: External integrations
8. **Advanced Search**: Full-text search with filters
9. **Subscription Management**: Recurring payments
10. **Advanced Reporting**: Custom report builder

### Integration Possibilities:
- Stripe payment processing
- Google Analytics integration
- Facebook Pixel tracking
- Mailchimp email marketing
- Zapier automation
- Slack notifications
- Discord webhooks
- SMS notifications (Twilio)

This comprehensive prompt provides everything needed to recreate the Niraza e-commerce platform with all its advanced features, security measures, and architectural decisions. The implementation should follow modern best practices for security, performance, and maintainability.