import { Request, Response, NextFunction } from 'express';

// Comprehensive list of known crawlers and bots to block
const BLOCKED_USER_AGENTS = [
  // Search engines
  'googlebot', 'bingbot', 'slurp', 'duckduckbot', 'yandexbot', 'baiduspider',
  'facebookexternalhit', 'twitterbot', 'linkedinbot', 'whatsapp', 'applebot',
  'ia_archiver', 'wayback',
  
  // SEO and analysis tools
  'ahrefsbot', 'semrushbot', 'mj12bot', 'dotbot', 'rogerbot', 'siteauditbot',
  'screaming frog seo spider', 'seokicks', 'seokicks-robot', 'spbot',
  'megaindex.ru', 'seznambot', 'linkdexbot', 'exabot', 'mojeekbot',
  'yisouspider', 'sogouspider', '360spider',
  
  // Archive crawlers
  'archive.org_bot', 'wayback',
  
  // Monitoring services
  'uptimerobot', 'statuscake', 'pingdom', 'gtmetrix', 'pagespeed',
  
  // Security scanners
  'nessus', 'nikto', 'openvas', 'sqlmap', 'nmap',
  
  // Content scrapers
  'httrack', 'wget', 'curl', 'libwww-perl', 'python-urllib', 'python-requests',
  'scrapy', 'beautifulsoup', 'mechanize',
  
  // Email harvesters
  'emailcollector', 'emailsiphon', 'webbandit', 'emailwolf',
  
  // Other bots
  'bot', 'crawler', 'spider', 'scraper', 'parser', 'extractor'
];

// Additional patterns to check
const BLOCKED_PATTERNS = [
  /bot/i,
  /crawler/i,
  /spider/i,
  /scraper/i,
  /parser/i,
  /extractor/i,
  /monitor/i,
  /check/i,
  /test/i,
  /scan/i,
  /audit/i,
  /seo/i,
  /analytics/i,
  /research/i,
  /harvest/i
];

// Known legitimate browsers to allow
const ALLOWED_BROWSERS = [
  'mozilla', 'chrome', 'safari', 'firefox', 'edge', 'opera', 'brave'
];

/**
 * Middleware to block search engine crawlers and SEO analysis tools
 */
export function crawlerBlocker(req: Request, res: Response, next: NextFunction) {
  const userAgent = req.get('User-Agent')?.toLowerCase() || '';
  
  // Skip blocking for API endpoints (they might be legitimate integrations)
  if (req.path.startsWith('/api/')) {
    return next();
  }
  
  // Check if user agent contains any blocked terms
  const isBlocked = BLOCKED_USER_AGENTS.some(agent => 
    userAgent.includes(agent.toLowerCase())
  );
  
  // Check against patterns
  const matchesBlockedPattern = BLOCKED_PATTERNS.some(pattern => 
    pattern.test(userAgent)
  );
  
  // Check if it's a legitimate browser
  const isLegitimateUser = ALLOWED_BROWSERS.some(browser => 
    userAgent.includes(browser.toLowerCase())
  );
  
  // Block if:
  // 1. User agent is in blocked list, OR
  // 2. User agent matches blocked patterns AND is not a legitimate browser
  if (isBlocked || (matchesBlockedPattern && !isLegitimateUser)) {
    console.log(`🚫 CRAWLER BLOCKED: ${userAgent} from ${req.ip}`);
    
    // Return 403 Forbidden with minimal information
    return res.status(403).send('Access Denied');
  }
  
  // Additional checks for suspicious behavior
  const suspiciousHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'x-cluster-client-ip',
    'cf-connecting-ip'
  ];
  
  // Check for headless browser indicators
  const headlessIndicators = [
    'headless', 'phantom', 'selenium', 'webdriver', 'puppeteer', 'playwright'
  ];
  
  const hasHeadlessIndicators = headlessIndicators.some(indicator => 
    userAgent.includes(indicator.toLowerCase())
  );
  
  if (hasHeadlessIndicators) {
    console.log(`🚫 HEADLESS BROWSER BLOCKED: ${userAgent} from ${req.ip}`);
    return res.status(403).send('Access Denied');
  }
  
  // Log legitimate access for monitoring
  if (isLegitimateUser) {
    console.log(`✅ LEGITIMATE ACCESS: ${userAgent} from ${req.ip}`);
  }
  
  next();
}

/**
 * Additional security headers to prevent indexing and analysis
 */
export function antiSeoHeaders(req: Request, res: Response, next: NextFunction) {
  // Set headers to prevent caching and indexing
  res.setHeader('X-Robots-Tag', 'noindex, nofollow, noarchive, nosnippet, noimageindex, notranslate, nopreview');
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate, private');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('Referrer-Policy', 'no-referrer');
  
  // Remove server information
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');
  
  next();
}
