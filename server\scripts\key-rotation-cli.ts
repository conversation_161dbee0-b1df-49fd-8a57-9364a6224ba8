#!/usr/bin/env node

/**
 * Key Rotation CLI Script
 * Provides command-line interface for key rotation operations
 */

import { keyRotationManager } from '../config/key-rotation';
import { dataReencryptionManager } from '../config/data-reencryption';

// CLI Commands
enum Command {
  STATUS = 'status',
  ROTATE = 'rotate',
  FORCE_ROTATE = 'force-rotate',
  REENCRYPT = 'reencrypt',
  ROLLBACK = 'rollback',
  REVOKE = 'revoke',
  HELP = 'help'
}

// Display help information
function displayHelp(): void {
  console.log(`
🔑 Key Rotation CLI

Usage: npm run key-rotation <command> [options]

Commands:
  status                    Show current key rotation status
  rotate                    Perform key rotation if needed
  force-rotate             Force immediate key rotation
  reencrypt <version>       Re-encrypt data with specified key version
  rollback <file>           Rollback re-encryption using rollback file
  revoke <version>          Revoke a specific key version
  help                      Show this help message

Examples:
  npm run key-rotation status
  npm run key-rotation rotate
  npm run key-rotation force-rotate
  npm run key-rotation reencrypt v2
  npm run key-rotation revoke v1

Environment Variables:
  KEY_ROTATION_INTERVAL_DAYS    Key rotation interval (default: 90)
  MAX_KEY_VERSIONS             Maximum key versions to keep (default: 5)
  ENABLE_AUTO_KEY_ROTATION     Enable automatic rotation (default: true)
  REENCRYPTION_BATCH_SIZE      Re-encryption batch size (default: 100)
`);
}

// Display key rotation status
async function displayStatus(): Promise<void> {
  try {
    console.log('🔑 Key Rotation Status\n');
    
    const status = keyRotationManager.getRotationStatus();
    const reencryptionStatus = await dataReencryptionManager.getReencryptionStatus();
    
    console.log(`Current Key Version: ${status.currentVersion}`);
    console.log(`Last Rotation: ${status.lastRotation.toISOString()}`);
    console.log(`Next Rotation: ${status.nextRotation.toISOString()}`);
    console.log(`Rotation Needed: ${status.isRotationNeeded ? 'Yes' : 'No'}`);
    console.log(`Total Key Versions: ${status.totalKeys}`);
    console.log(`Auto Rotation: ${status.autoRotationEnabled ? 'Enabled' : 'Disabled'}`);
    
    console.log('\n📊 Data Encryption Status\n');
    console.log(`Total Emails: ${reencryptionStatus.totalEmails}`);
    console.log(`Encrypted Emails: ${reencryptionStatus.encryptedEmails}`);
    console.log(`Unencrypted Emails: ${reencryptionStatus.unencryptedEmails}`);
    
    if (Object.keys(reencryptionStatus.keyVersions).length > 0) {
      console.log('\n🔐 Key Version Distribution:');
      for (const [version, count] of Object.entries(reencryptionStatus.keyVersions)) {
        console.log(`  ${version}: ${count} records`);
      }
    }
    
    // Check if rotation is overdue
    if (status.isRotationNeeded) {
      const daysSinceLastRotation = Math.floor((Date.now() - status.lastRotation.getTime()) / (1000 * 60 * 60 * 24));
      console.log(`\n⚠️ Key rotation is overdue by ${daysSinceLastRotation} days!`);
      console.log('💡 Run "npm run key-rotation rotate" to perform rotation');
    }
    
  } catch (error) {
    console.error('❌ Failed to get status:', error.message);
    process.exit(1);
  }
}

// Perform key rotation
async function performRotation(force: boolean = false): Promise<void> {
  try {
    console.log(`🔄 ${force ? 'Force' : 'Standard'} key rotation starting...\n`);
    
    if (!force && !keyRotationManager.isRotationNeeded()) {
      console.log('ℹ️ Key rotation is not needed at this time');
      console.log('💡 Use "force-rotate" to rotate anyway');
      return;
    }
    
    const result = force ? 
      await keyRotationManager.triggerManualRotation() :
      await keyRotationManager.rotateKeys();
    
    if (result.success) {
      console.log(`✅ Key rotation completed successfully!`);
      console.log(`🔑 New key version: ${result.newVersion}`);
      
      // Ask if user wants to re-encrypt data
      console.log('\n💡 Consider re-encrypting existing data with the new key:');
      console.log(`   npm run key-rotation reencrypt ${result.newVersion}`);
    } else {
      console.log('❌ Key rotation failed:');
      result.errors.forEach(error => console.log(`  - ${error}`));
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Key rotation failed:', error.message);
    process.exit(1);
  }
}

// Re-encrypt data with new key
async function reencryptData(keyVersion: string): Promise<void> {
  try {
    console.log(`🔄 Starting data re-encryption with key version: ${keyVersion}\n`);
    
    // Verify key version exists
    const key = keyRotationManager.getKeyByVersion(keyVersion);
    if (!key) {
      throw new Error(`Key version ${keyVersion} not found`);
    }
    
    const result = await dataReencryptionManager.reencryptEmailData(keyVersion);
    
    if (result.success) {
      console.log('✅ Data re-encryption completed successfully!');
      console.log(`📊 Re-encrypted ${result.reencryptedRecords}/${result.totalRecords} records`);
      
      // Save rollback data if available
      if (result.rollbackData && result.rollbackData.length > 0) {
        const rollbackFile = `rollback-${keyVersion}-${Date.now()}.json`;
        const fs = require('fs');
        fs.writeFileSync(rollbackFile, JSON.stringify(result.rollbackData, null, 2));
        console.log(`💾 Rollback data saved to: ${rollbackFile}`);
      }
    } else {
      console.log('❌ Data re-encryption failed:');
      result.errors.forEach(error => console.log(`  - ${error}`));
      
      if (result.rollbackData && result.rollbackData.length > 0) {
        console.log('\n🔄 Attempting automatic rollback...');
        const rollbackResult = await dataReencryptionManager.rollbackReencryption(result.rollbackData);
        
        if (rollbackResult.success) {
          console.log('✅ Automatic rollback completed');
        } else {
          console.log('❌ Automatic rollback failed:');
          rollbackResult.errors.forEach(error => console.log(`  - ${error}`));
        }
      }
      
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Data re-encryption failed:', error.message);
    process.exit(1);
  }
}

// Rollback re-encryption
async function rollbackReencryption(rollbackFile: string): Promise<void> {
  try {
    console.log(`🔄 Starting rollback from file: ${rollbackFile}\n`);
    
    const fs = require('fs');
    if (!fs.existsSync(rollbackFile)) {
      throw new Error(`Rollback file not found: ${rollbackFile}`);
    }
    
    const rollbackData = JSON.parse(fs.readFileSync(rollbackFile, 'utf8'));
    
    if (!Array.isArray(rollbackData) || rollbackData.length === 0) {
      throw new Error('Invalid or empty rollback data');
    }
    
    const result = await dataReencryptionManager.rollbackReencryption(rollbackData);
    
    if (result.success) {
      console.log('✅ Rollback completed successfully!');
      console.log(`📊 Rolled back ${rollbackData.length} records`);
    } else {
      console.log('❌ Rollback failed:');
      result.errors.forEach(error => console.log(`  - ${error}`));
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Rollback failed:', error.message);
    process.exit(1);
  }
}

// Revoke key version
async function revokeKey(keyVersion: string): Promise<void> {
  try {
    console.log(`🚨 Revoking key version: ${keyVersion}\n`);
    
    // Confirm revocation
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const answer = await new Promise<string>((resolve) => {
      rl.question(`Are you sure you want to revoke key version ${keyVersion}? (yes/no): `, resolve);
    });
    
    rl.close();
    
    if (answer.toLowerCase() !== 'yes') {
      console.log('❌ Key revocation cancelled');
      return;
    }
    
    const success = await keyRotationManager.revokeKey(keyVersion);
    
    if (success) {
      console.log(`✅ Key version ${keyVersion} revoked successfully`);
      console.log('⚠️ Data encrypted with this key version will no longer be accessible');
    } else {
      console.log(`❌ Failed to revoke key version ${keyVersion}`);
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Key revocation failed:', error.message);
    process.exit(1);
  }
}

// Main CLI function
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  const command = args[0] as Command;
  
  if (!command || command === Command.HELP) {
    displayHelp();
    return;
  }
  
  try {
    switch (command) {
      case Command.STATUS:
        await displayStatus();
        break;
        
      case Command.ROTATE:
        await performRotation(false);
        break;
        
      case Command.FORCE_ROTATE:
        await performRotation(true);
        break;
        
      case Command.REENCRYPT:
        const keyVersion = args[1];
        if (!keyVersion) {
          console.error('❌ Key version required for reencrypt command');
          console.log('💡 Usage: npm run key-rotation reencrypt <version>');
          process.exit(1);
        }
        await reencryptData(keyVersion);
        break;
        
      case Command.ROLLBACK:
        const rollbackFile = args[1];
        if (!rollbackFile) {
          console.error('❌ Rollback file required for rollback command');
          console.log('💡 Usage: npm run key-rotation rollback <file>');
          process.exit(1);
        }
        await rollbackReencryption(rollbackFile);
        break;
        
      case Command.REVOKE:
        const revokeVersion = args[1];
        if (!revokeVersion) {
          console.error('❌ Key version required for revoke command');
          console.log('💡 Usage: npm run key-rotation revoke <version>');
          process.exit(1);
        }
        await revokeKey(revokeVersion);
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        displayHelp();
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Command failed:', error.message);
    process.exit(1);
  } finally {
    // Shutdown key rotation manager
    keyRotationManager.shutdown();
  }
}

// Run CLI if this file is executed directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ CLI error:', error);
    process.exit(1);
  });
}
