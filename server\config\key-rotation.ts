import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { securityMonitor, SecurityEventType, SecuritySeverity } from './security-monitoring';

// Key rotation configuration
interface KeyRotationConfig {
  rotationIntervalDays: number;
  maxKeyVersions: number;
  enableAutoRotation: boolean;
  keyDerivationIterations: number;
  masterKeyPath: string;
  keyStorePath: string;
}

// Key version interface
interface KeyVersion {
  version: string;
  key: string;
  createdAt: Date;
  rotatedAt?: Date;
  status: 'active' | 'deprecated' | 'revoked';
  derivationSalt: string;
  algorithm: string;
}

// Key store interface
interface KeyStore {
  currentVersion: string;
  masterKeyHash: string;
  keys: Record<string, KeyVersion>;
  lastRotation: Date;
  nextRotation: Date;
}

// Default configuration
const DEFAULT_CONFIG: KeyRotationConfig = {
  rotationIntervalDays: parseInt(process.env.KEY_ROTATION_INTERVAL_DAYS || '90'),
  maxKeyVersions: parseInt(process.env.MAX_KEY_VERSIONS || '5'),
  enableAutoRotation: process.env.ENABLE_AUTO_KEY_ROTATION !== 'false',
  keyDerivationIterations: parseInt(process.env.KEY_DERIVATION_ITERATIONS || '100000'),
  masterKeyPath: process.env.MASTER_KEY_PATH || '.master-key',
  keyStorePath: process.env.KEY_STORE_PATH || '.key-store.json'
};

export class KeyRotationManager {
  private config: KeyRotationConfig;
  private keyStore: KeyStore | null = null;
  private rotationTimer: NodeJS.Timeout | null = null;

  constructor(config: Partial<KeyRotationConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initializeKeyStore();
    
    if (this.config.enableAutoRotation) {
      this.scheduleAutoRotation();
    }
  }

  // Initialize key store
  private initializeKeyStore(): void {
    try {
      if (fs.existsSync(this.config.keyStorePath)) {
        const keyStoreData = fs.readFileSync(this.config.keyStorePath, 'utf8');
        this.keyStore = JSON.parse(keyStoreData);
        
        // Convert date strings back to Date objects
        if (this.keyStore) {
          this.keyStore.lastRotation = new Date(this.keyStore.lastRotation);
          this.keyStore.nextRotation = new Date(this.keyStore.nextRotation);
          
          Object.values(this.keyStore.keys).forEach(key => {
            key.createdAt = new Date(key.createdAt);
            if (key.rotatedAt) {
              key.rotatedAt = new Date(key.rotatedAt);
            }
          });
        }
        
        console.log('🔑 Loaded existing key store');
      } else {
        this.createInitialKeyStore();
      }
    } catch (error) {
      console.error('Failed to initialize key store:', error);
      this.createInitialKeyStore();
    }
  }

  // Create initial key store
  private createInitialKeyStore(): void {
    console.log('🔑 Creating initial key store...');
    
    const masterKey = this.getOrCreateMasterKey();
    const initialKey = this.generateNewKey('v1', masterKey);
    
    const now = new Date();
    const nextRotation = new Date(now.getTime() + (this.config.rotationIntervalDays * 24 * 60 * 60 * 1000));
    
    this.keyStore = {
      currentVersion: 'v1',
      masterKeyHash: crypto.createHash('sha256').update(masterKey).digest('hex'),
      keys: {
        'v1': initialKey
      },
      lastRotation: now,
      nextRotation: nextRotation
    };
    
    this.saveKeyStore();
    console.log('✅ Initial key store created');
  }

  // Get or create master key
  private getOrCreateMasterKey(): string {
    try {
      if (fs.existsSync(this.config.masterKeyPath)) {
        const masterKey = fs.readFileSync(this.config.masterKeyPath, 'utf8').trim();
        if (masterKey.length >= 64) {
          return masterKey;
        }
      }
      
      // Generate new master key
      const masterKey = crypto.randomBytes(64).toString('hex');
      fs.writeFileSync(this.config.masterKeyPath, masterKey, { mode: 0o600 });
      console.log('🔐 Generated new master key');
      
      return masterKey;
    } catch (error) {
      console.error('Failed to get/create master key:', error);
      throw new Error('Master key initialization failed');
    }
  }

  // Generate new key version
  private generateNewKey(version: string, masterKey: string): KeyVersion {
    const salt = crypto.randomBytes(32);
    const derivedKey = crypto.pbkdf2Sync(
      masterKey,
      salt,
      this.config.keyDerivationIterations,
      32,
      'sha256'
    );
    
    return {
      version,
      key: derivedKey.toString('hex'),
      createdAt: new Date(),
      status: 'active',
      derivationSalt: salt.toString('hex'),
      algorithm: 'PBKDF2-SHA256'
    };
  }

  // Save key store to disk
  private saveKeyStore(): void {
    if (!this.keyStore) {
      throw new Error('Key store not initialized');
    }
    
    try {
      const keyStoreData = JSON.stringify(this.keyStore, null, 2);
      fs.writeFileSync(this.config.keyStorePath, keyStoreData, { mode: 0o600 });
    } catch (error) {
      console.error('Failed to save key store:', error);
      throw new Error('Key store save failed');
    }
  }

  // Get current active key
  getCurrentKey(): string {
    if (!this.keyStore) {
      throw new Error('Key store not initialized');
    }
    
    const currentKey = this.keyStore.keys[this.keyStore.currentVersion];
    if (!currentKey || currentKey.status !== 'active') {
      throw new Error('No active key found');
    }
    
    return currentKey.key;
  }

  // Get key by version
  getKeyByVersion(version: string): string | null {
    if (!this.keyStore) {
      return null;
    }
    
    const key = this.keyStore.keys[version];
    return key ? key.key : null;
  }

  // Check if rotation is needed
  isRotationNeeded(): boolean {
    if (!this.keyStore) {
      return false;
    }
    
    return new Date() >= this.keyStore.nextRotation;
  }

  // Perform key rotation
  async rotateKeys(): Promise<{ success: boolean; newVersion: string; errors: string[] }> {
    const result = {
      success: false,
      newVersion: '',
      errors: [] as string[]
    };

    try {
      if (!this.keyStore) {
        throw new Error('Key store not initialized');
      }

      console.log('🔄 Starting key rotation...');

      // Generate new version number
      const currentVersionNum = parseInt(this.keyStore.currentVersion.replace('v', ''));
      const newVersion = `v${currentVersionNum + 1}`;
      
      // Get master key
      const masterKey = this.getOrCreateMasterKey();
      
      // Verify master key integrity
      const masterKeyHash = crypto.createHash('sha256').update(masterKey).digest('hex');
      if (masterKeyHash !== this.keyStore.masterKeyHash) {
        throw new Error('Master key integrity check failed');
      }

      // Generate new key
      const newKey = this.generateNewKey(newVersion, masterKey);
      
      // Mark current key as deprecated
      const currentKey = this.keyStore.keys[this.keyStore.currentVersion];
      if (currentKey) {
        currentKey.status = 'deprecated';
        currentKey.rotatedAt = new Date();
      }

      // Add new key
      this.keyStore.keys[newVersion] = newKey;
      this.keyStore.currentVersion = newVersion;
      
      // Update rotation timestamps
      const now = new Date();
      this.keyStore.lastRotation = now;
      this.keyStore.nextRotation = new Date(now.getTime() + (this.config.rotationIntervalDays * 24 * 60 * 60 * 1000));

      // Clean up old keys
      this.cleanupOldKeys();

      // Save updated key store
      this.saveKeyStore();

      result.success = true;
      result.newVersion = newVersion;

      // Log security event
      securityMonitor.logSecurityEvent(
        SecurityEventType.SECURITY_VIOLATION, // Using this as closest match for key rotation
        {
          userId: undefined,
          username: 'system',
          role: 'system' as any,
          permissions: [],
          ipAddress: 'localhost'
        },
        {
          action: 'KEY_ROTATION',
          oldVersion: currentKey?.version,
          newVersion: newVersion,
          rotationTime: now.toISOString()
        },
        SecuritySeverity.HIGH
      );

      console.log(`✅ Key rotation completed: ${this.keyStore.currentVersion} -> ${newVersion}`);

    } catch (error) {
      const errorMsg = `Key rotation failed: ${error.message}`;
      console.error(`❌ ${errorMsg}`);
      result.errors.push(errorMsg);
    }

    return result;
  }

  // Clean up old keys
  private cleanupOldKeys(): void {
    if (!this.keyStore) {
      return;
    }

    const keyVersions = Object.keys(this.keyStore.keys).sort((a, b) => {
      const aNum = parseInt(a.replace('v', ''));
      const bNum = parseInt(b.replace('v', ''));
      return bNum - aNum; // Sort descending
    });

    // Keep only the configured number of key versions
    if (keyVersions.length > this.config.maxKeyVersions) {
      const keysToRemove = keyVersions.slice(this.config.maxKeyVersions);
      
      for (const version of keysToRemove) {
        delete this.keyStore.keys[version];
        console.log(`🗑️ Removed old key version: ${version}`);
      }
    }
  }

  // Schedule automatic rotation
  private scheduleAutoRotation(): void {
    if (!this.keyStore) {
      return;
    }

    const now = new Date();
    const timeUntilRotation = this.keyStore.nextRotation.getTime() - now.getTime();

    // Cap timeout to maximum safe value (24 hours)
    const maxTimeout = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    if (timeUntilRotation > 0) {
      const actualTimeout = Math.min(timeUntilRotation, maxTimeout);
      this.rotationTimer = setTimeout(() => {
        this.performScheduledRotation();
      }, actualTimeout);

      if (timeUntilRotation > maxTimeout) {
        console.log(`📅 Scheduled key rotation check in 24 hours (rotation due: ${this.keyStore.nextRotation.toISOString()})`);
      } else {
        console.log(`📅 Scheduled key rotation for: ${this.keyStore.nextRotation.toISOString()}`);
      }
    } else {
      // Rotation is overdue, schedule it soon
      this.rotationTimer = setTimeout(() => {
        this.performScheduledRotation();
      }, 60000); // 1 minute

      console.log('⚠️ Key rotation is overdue, scheduling immediate rotation');
    }
  }

  // Perform scheduled rotation
  private async performScheduledRotation(): Promise<void> {
    try {
      // Check if rotation is actually needed
      if (!this.isRotationNeeded()) {
        console.log('🔄 Rotation check: not needed yet, rescheduling...');
        this.scheduleAutoRotation();
        return;
      }

      console.log('🔄 Performing scheduled key rotation...');
      const result = await this.rotateKeys();

      if (result.success) {
        console.log('✅ Scheduled key rotation completed successfully');

        // Schedule next rotation
        this.scheduleAutoRotation();
      } else {
        console.error('❌ Scheduled key rotation failed:', result.errors);

        // Retry in 1 hour
        this.rotationTimer = setTimeout(() => {
          this.performScheduledRotation();
        }, 60 * 60 * 1000);
      }
    } catch (error) {
      console.error('❌ Scheduled key rotation error:', error);

      // Retry in 1 hour
      this.rotationTimer = setTimeout(() => {
        this.performScheduledRotation();
      }, 60 * 60 * 1000);
    }
  }

  // Manual key rotation trigger
  async triggerManualRotation(): Promise<{ success: boolean; newVersion: string; errors: string[] }> {
    console.log('🔄 Manual key rotation triggered...');
    
    // Cancel scheduled rotation
    if (this.rotationTimer) {
      clearTimeout(this.rotationTimer);
      this.rotationTimer = null;
    }
    
    const result = await this.rotateKeys();
    
    // Reschedule automatic rotation
    if (this.config.enableAutoRotation) {
      this.scheduleAutoRotation();
    }
    
    return result;
  }

  // Get key rotation status
  getRotationStatus(): {
    currentVersion: string;
    lastRotation: Date;
    nextRotation: Date;
    isRotationNeeded: boolean;
    totalKeys: number;
    autoRotationEnabled: boolean;
  } {
    if (!this.keyStore) {
      throw new Error('Key store not initialized');
    }

    return {
      currentVersion: this.keyStore.currentVersion,
      lastRotation: this.keyStore.lastRotation,
      nextRotation: this.keyStore.nextRotation,
      isRotationNeeded: this.isRotationNeeded(),
      totalKeys: Object.keys(this.keyStore.keys).length,
      autoRotationEnabled: this.config.enableAutoRotation
    };
  }

  // Emergency key revocation
  async revokeKey(version: string): Promise<boolean> {
    try {
      if (!this.keyStore) {
        throw new Error('Key store not initialized');
      }

      const key = this.keyStore.keys[version];
      if (!key) {
        throw new Error(`Key version ${version} not found`);
      }

      if (version === this.keyStore.currentVersion) {
        throw new Error('Cannot revoke current active key');
      }

      key.status = 'revoked';
      key.rotatedAt = new Date();
      
      this.saveKeyStore();

      // Log security event
      securityMonitor.logSecurityEvent(
        SecurityEventType.SECURITY_VIOLATION,
        {
          userId: undefined,
          username: 'system',
          role: 'system' as any,
          permissions: [],
          ipAddress: 'localhost'
        },
        {
          action: 'KEY_REVOCATION',
          revokedVersion: version,
          revocationTime: new Date().toISOString()
        },
        SecuritySeverity.CRITICAL
      );

      console.log(`🚨 Key version ${version} revoked`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to revoke key ${version}:`, error);
      return false;
    }
  }

  // Shutdown key rotation manager
  shutdown(): void {
    if (this.rotationTimer) {
      clearTimeout(this.rotationTimer);
      this.rotationTimer = null;
    }
    console.log('🔑 Key rotation manager shutdown');
  }
}

// Global key rotation manager instance
export const keyRotationManager = new KeyRotationManager();
