const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '..', 'app', 'data', 'data.db');
const db = new Database(dbPath);

console.log('🔍 COMPREHENSIVE DATABASE DATA INTEGRITY CHECK');
console.log('='.repeat(60));

try {
  // Get all tables
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").all();
  console.log(`📊 Found ${tables.length} application tables:`);
  
  let totalRecords = 0;
  const tableData = {};

  for (const table of tables) {
    const tableName = table.name;
    console.log(`\n📋 Table: ${tableName}`);
    
    try {
      // Get table schema
      const schema = db.prepare(`PRAGMA table_info(${tableName})`).all();
      console.log(`   Columns: ${schema.map(col => `${col.name} (${col.type})`).join(', ')}`);
      
      // Get record count
      const count = db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get();
      const recordCount = count.count;
      totalRecords += recordCount;
      tableData[tableName] = { recordCount, schema };
      
      console.log(`   Records: ${recordCount}`);
      
      // Show sample data for important tables
      if (recordCount > 0 && ['users', 'homepage_config', 'settings', 'invoices', 'customers'].includes(tableName)) {
        const sampleData = db.prepare(`SELECT * FROM ${tableName} LIMIT 3`).all();
        console.log(`   Sample data:`);
        sampleData.forEach((row, index) => {
          console.log(`     ${index + 1}. ${JSON.stringify(row, null, 0).substring(0, 100)}...`);
        });
      }
      
      // Specific checks for critical tables
      if (tableName === 'users') {
        const adminUsers = db.prepare("SELECT username, created_at FROM users WHERE role = 'admin'").all();
        console.log(`   Admin users: ${adminUsers.length}`);
        adminUsers.forEach(user => {
          console.log(`     - ${user.username} (created: ${user.created_at})`);
        });
      }
      
      if (tableName === 'homepage_config') {
        const configs = db.prepare("SELECT id, version, created_at, updated_at FROM homepage_config").all();
        configs.forEach(config => {
          console.log(`     - Config ID: ${config.id}, Version: ${config.version}`);
          console.log(`       Created: ${config.created_at}, Updated: ${config.updated_at}`);
        });
      }
      
      if (tableName === 'settings') {
        const settings = db.prepare("SELECT key, created_at FROM settings").all();
        console.log(`   Settings keys: ${settings.map(s => s.key).join(', ')}`);
      }
      
    } catch (error) {
      console.error(`   ❌ Error checking table ${tableName}:`, error.message);
    }
  }

  console.log('\n' + '='.repeat(60));
  console.log('📊 SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tables: ${tables.length}`);
  console.log(`Total Records: ${totalRecords}`);
  
  // Check for critical data
  console.log('\n🔍 CRITICAL DATA VERIFICATION:');
  
  // Check homepage configuration
  const homepageConfigs = db.prepare('SELECT COUNT(*) as count FROM homepage_config').get();
  console.log(`✅ Homepage configurations: ${homepageConfigs.count > 0 ? 'PRESENT' : '❌ MISSING'}`);
  
  // Check admin users
  const adminUsers = db.prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'").get();
  console.log(`✅ Admin users: ${adminUsers.count > 0 ? 'PRESENT' : '❌ MISSING'}`);
  
  // Check settings
  const settingsCount = db.prepare('SELECT COUNT(*) as count FROM settings').get();
  console.log(`✅ Application settings: ${settingsCount.count > 0 ? 'PRESENT' : '❌ MISSING'}`);
  
  // Check for any data loss indicators
  console.log('\n🔍 DATA INTEGRITY CHECKS:');
  
  // Check for empty critical tables
  const criticalTables = ['users', 'homepage_config'];
  let integrityIssues = 0;
  
  for (const tableName of criticalTables) {
    const count = db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get();
    if (count.count === 0) {
      console.log(`❌ CRITICAL: ${tableName} table is empty`);
      integrityIssues++;
    } else {
      console.log(`✅ ${tableName} table has data (${count.count} records)`);
    }
  }
  
  // Check for recent activity
  console.log('\n🕒 RECENT ACTIVITY CHECK:');
  try {
    const recentUsers = db.prepare("SELECT username, last_login FROM users WHERE last_login IS NOT NULL ORDER BY last_login DESC LIMIT 3").all();
    if (recentUsers.length > 0) {
      console.log('✅ Recent user activity found:');
      recentUsers.forEach(user => {
        console.log(`   - ${user.username}: ${user.last_login}`);
      });
    } else {
      console.log('⚠️ No recent user activity found');
    }
  } catch (error) {
    console.log('⚠️ Could not check recent activity:', error.message);
  }
  
  console.log('\n' + '='.repeat(60));
  if (integrityIssues === 0 && totalRecords > 0) {
    console.log('🎉 DATA INTEGRITY CHECK PASSED');
    console.log('✅ All critical data appears to be intact');
  } else if (integrityIssues > 0) {
    console.log('⚠️ DATA INTEGRITY ISSUES DETECTED');
    console.log(`❌ ${integrityIssues} critical table(s) are empty`);
  } else {
    console.log('⚠️ DATABASE APPEARS TO BE EMPTY');
    console.log('❌ No data found in any tables');
  }
  console.log('='.repeat(60));

} catch (error) {
  console.error('❌ Database integrity check failed:', error);
} finally {
  db.close();
}
