import { Router, Request, Response, NextFunction } from 'express';
import { storage } from '../storage-factory';
import { insertProductSchema } from '@shared/schema';
import { ZodError } from 'zod';
import { randomBytes, createHash } from 'crypto';
import { telegramBot } from '../services/telegram-bot';

const adminRouter = Router();



// Session augmentation for TypeScript
declare module 'express-session' {
  interface SessionData {
    isAdmin: boolean;
    username: string;
    userId: number;
    rememberMe: boolean;
    requiresTwoFactor: boolean;
    twoFactorVerified: boolean;
    pendingApprovalId?: string;
    pending2FARequestId?: string;
  }
}

// Admin access token middleware removed - no longer needed

// Admin authentication middleware
const isAdmin = (req: Request, res: Response, next: NextFunction) => {
  console.log('Checking admin session:', req.session);

  // Check if user is authenticated and has completed 2FA if required
  if (req.session.isAdmin) {
    // If 2FA is required but not verified, deny access
    if (req.session.requiresTwoFactor && !req.session.twoFactorVerified) {
      console.log('2FA required but not verified');
      return res.status(401).json({
        message: 'Two-factor authentication required',
        requiresTwoFactor: true
      });
    }

    console.log('Admin session verified:', true);
    next();
  } else {
    console.log('Admin session verified:', false);
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Check if user is authenticated
adminRouter.get('/check-session', (req: Request, res: Response) => {
  console.log('Checking session:', {
    id: req.session.id,
    isAdmin: req.session.isAdmin,
    username: req.session.username,
    requiresTwoFactor: req.session.requiresTwoFactor,
    twoFactorVerified: req.session.twoFactorVerified,
    cookie: req.session.cookie
  });

  if (req.session.isAdmin) {
    // User is fully authenticated
    res.status(200).json({
      isAuthenticated: true,
      user: {
        username: req.session.username
      }
    });
  } else if (req.session.requiresTwoFactor && !req.session.twoFactorVerified) {
    // User has completed first factor but needs to complete 2FA
    res.status(200).json({
      isAuthenticated: false,
      requiresTwoFactor: true,
      message: 'Two-factor authentication required'
    });
  } else {
    // User is not authenticated at all
    res.status(200).json({
      isAuthenticated: false,
      message: 'Not authenticated'
    });
  }
});

// Admin login
adminRouter.post('/login', async (req: Request, res: Response) => {
  const { username, password, rememberMe, accessToken } = req.body;

  // Get client IP and user agent for security tracking
  const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
  const userAgent = req.get('User-Agent') || 'unknown';
  const sessionId = req.session.id || 'unknown';

  console.log('Login attempt for:', username, 'from IP:', clientIP);

  try {
    // Check for admin access token first
    const adminToken = process.env.ADMIN_ACCESS_TOKEN;
    if (accessToken && adminToken && accessToken === adminToken) {
      console.log('Valid admin access token provided, logging in as admin');

      // Create admin session
      req.session.isAdmin = true;
      req.session.username = 'admin';
      req.session.rememberMe = rememberMe || false;
      req.session.requiresTwoFactor = false;
      req.session.twoFactorVerified = false;
      req.session.userId = 1; // Default admin user ID

      // Set cookie expiration based on rememberMe
      req.session.cookie.maxAge = rememberMe ?
        30 * 24 * 60 * 60 * 1000 : // 30 days
        24 * 60 * 60 * 1000; // 24 hours

      console.log('Token-based login successful, session info:', {
        id: req.session.id,
        isAdmin: req.session.isAdmin,
        username: req.session.username,
        rememberMe: req.session.rememberMe,
        cookieMaxAge: req.session.cookie.maxAge
      });

      // Set a non-httpOnly cookie as a flag for the frontend
      res.cookie('isLoggedIn', 'true', {
        httpOnly: false,
        maxAge: req.session.cookie.maxAge,
        path: '/',
        sameSite: 'lax'
      });

      // Return success response immediately (session auto-saves)
      return res.status(200).json({
        message: 'Login successful (token-based)',
        user: { username: 'admin' },
        isAuthenticated: true,
        rememberMe: req.session.rememberMe,
        requiresTwoFactor: false
      });
    }

    // Get user from storage (single call)
    let authenticatedUser = await storage.getUserByUsername(username);

    // If user not found, create default admin user for demo purposes
    if (!authenticatedUser && username === 'admin' && password === 'admin123') {
      // Create default admin user
      authenticatedUser = await storage.createUser({
        username: 'admin',
        password: 'admin123', // This will be hashed in the storage layer
        email: '<EMAIL>',
        isAdmin: true,
        rememberMe: false
      });
    }

    // Verify credentials (optimized - use user object to avoid double lookup)
    const isValidCredentials = authenticatedUser ?
      await storage.verifyUserCredentialsWithUser(authenticatedUser, password) : false;

    if (isValidCredentials) {
      // Security settings are now handled by database storage
      // For now, we'll use default security settings (all disabled)
      const securitySettings = {
        ipRestrictionEnabled: false,
        telegram2FAEnabled: false,
        loginAlertsEnabled: false
      };

      // IP restriction is disabled for now
      // 2FA is also disabled for now until we implement proper database-based security settings

      // Check if 2FA is enabled for this user
      if (authenticatedUser && authenticatedUser.twoFactorEnabled) {
        // Set up session for 2FA verification
        req.session.requiresTwoFactor = true;
        req.session.userId = authenticatedUser.id;
        req.session.username = username;
        req.session.rememberMe = rememberMe || false;
        req.session.twoFactorVerified = false;
        req.session.isAdmin = false; // Will be set to true after 2FA verification

        // Set cookie expiration based on rememberMe
        req.session.cookie.maxAge = rememberMe ?
          30 * 24 * 60 * 60 * 1000 : // 30 days
          24 * 60 * 60 * 1000; // 24 hours

        // Update auto-login settings asynchronously (don't wait)
        setImmediate(async () => {
          try {
            await storage.updateAutoLoginSettings(authenticatedUser.id, rememberMe || false);
          } catch (error) {
            console.error('Error updating auto-login settings:', error);
          }
        });

        // Request 2FA approval via Telegram (disabled for now)
        const twoFactorRequestId = 'disabled';
        req.session.pending2FARequestId = twoFactorRequestId;

        console.log('First-factor authentication successful, 2FA required:', {
          id: req.session.id,
          username: req.session.username,
          requiresTwoFactor: req.session.requiresTwoFactor,
          twoFactorRequestId: twoFactorRequestId
        });

        // Return response immediately (session auto-saves)
        return res.status(200).json({
          message: 'Two-factor authentication required',
          requiresTwoFactor: true,
          isAuthenticated: false,
          twoFactorRequestId: twoFactorRequestId
        });
      } else {
        // No 2FA required, complete login
        req.session.isAdmin = true;
        req.session.username = username;
        req.session.rememberMe = rememberMe || false;
        req.session.requiresTwoFactor = false;
        req.session.twoFactorVerified = false;

        if (authenticatedUser) {
          req.session.userId = authenticatedUser.id;
        }

        // Set cookie expiration based on rememberMe
        req.session.cookie.maxAge = rememberMe ?
          30 * 24 * 60 * 60 * 1000 : // 30 days
          24 * 60 * 60 * 1000; // 24 hours

        // Update auto-login settings asynchronously (don't wait)
        setImmediate(async () => {
          try {
            await storage.updateAutoLoginSettings(authenticatedUser.id, rememberMe || false);
          } catch (error) {
            console.error('Error updating auto-login settings:', error);
          }
        });

        console.log('Login successful, session info:', {
          id: req.session.id,
          isAdmin: req.session.isAdmin,
          username: req.session.username,
          rememberMe: req.session.rememberMe,
          cookieMaxAge: req.session.cookie.maxAge
        });

        // Set a non-httpOnly cookie as a flag for the frontend
        res.cookie('isLoggedIn', 'true', {
          httpOnly: false,
          maxAge: req.session.cookie.maxAge,
          path: '/',
          sameSite: 'lax'
        });

        // Return success response immediately (session auto-saves)
        return res.status(200).json({
          message: 'Login successful',
          user: { username },
          isAuthenticated: true,
          rememberMe: req.session.rememberMe,
          requiresTwoFactor: false
        });
      }
    } else {
      // Security settings are now handled by database storage
      res.status(401).json({ message: 'Invalid credentials' });
    }
  } catch (error) {
    console.error('Error during login:', error);
    res.status(500).json({ message: 'Login failed - server error' });
  }
});

// Check login approval status (disabled for now)
adminRouter.get('/check-approval/:approvalId', async (req: Request, res: Response) => {
  try {
    const { approvalId } = req.params;
    // Telegram bot is disabled for now
    const status = 'approved'; // Always approve for now

    res.json({ status });
  } catch (error) {
    console.error('Error checking approval status:', error);
    res.status(500).json({ message: 'Failed to check approval status' });
  }
});

// Check 2FA status (disabled for now)
adminRouter.get('/check-2fa/:requestId', async (req: Request, res: Response) => {
  try {
    const { requestId } = req.params;
    // Telegram bot is disabled for now
    const status = 'approved'; // Always approve for now

    if (status === 'approved') {
      // Complete the login process
      req.session.isAdmin = true;
      req.session.twoFactorVerified = true;
      req.session.requiresTwoFactor = false;

      // Set a non-httpOnly cookie as a flag for the frontend
      res.cookie('isLoggedIn', 'true', {
        httpOnly: false,
        maxAge: req.session.cookie.maxAge,
        path: '/',
        sameSite: 'lax'
      });
    }

    res.json({ status });
  } catch (error) {
    console.error('Error checking 2FA status:', error);
    res.status(500).json({ message: 'Failed to check 2FA status' });
  }
});

// Admin logout
adminRouter.post('/logout', (req: Request, res: Response) => {
  console.log('Logout requested, destroying session:', req.session.id);

  req.session.destroy((err) => {
    if (err) {
      console.error('Error destroying session:', err);
      return res.status(500).json({ message: 'Logout failed' });
    }

    // Clear the cookie
    res.clearCookie('isLoggedIn');
    res.clearCookie('connect.sid', { path: '/' });

    res.status(200).json({ message: 'Logout successful' });
  });
});

// Enable/Disable 2FA for current user
adminRouter.post('/toggle-2fa', isAdmin, async (req: Request, res: Response) => {
  try {
    const { enabled } = req.body;
    const userId = req.session.userId;

    if (!userId) {
      return res.status(400).json({ message: 'User ID not found in session' });
    }

    // Update user's 2FA setting
    await storage.updateUser(userId, { twoFactorEnabled: enabled });

    res.json({
      message: `Two-factor authentication ${enabled ? 'enabled' : 'disabled'} successfully`,
      twoFactorEnabled: enabled
    });
  } catch (error) {
    console.error('Error toggling 2FA:', error);
    res.status(500).json({ message: 'Failed to update 2FA settings' });
  }
});

// Get security settings
adminRouter.get('/security-settings', isAdmin, async (req: Request, res: Response) => {
  try {
    // Return default security settings since we're using database storage
    const securitySettings = {
      loginAlertsEnabled: false,
      ipRestrictionEnabled: false,
      telegram2FAEnabled: false
    };

    // Also get user's 2FA status
    const userId = req.session.userId;
    let userTwoFactorEnabled = false;

    if (userId) {
      const user = await storage.getUser(userId);
      if (user) {
        userTwoFactorEnabled = user.twoFactorEnabled || false;
      }
    }

    res.json({
      ...securitySettings,
      userTwoFactorEnabled,
      username: req.session.username
    });
  } catch (error) {
    console.error('Error fetching security settings:', error);
    res.status(500).json({ message: 'Failed to fetch security settings' });
  }
});

// Update security settings
adminRouter.post('/security-settings', isAdmin, async (req: Request, res: Response) => {
  try {
    const { loginAlertsEnabled, ipRestrictionEnabled, telegram2FAEnabled } = req.body;

    // Security settings are now handled by database storage
    // For now, we'll just return the default settings
    const updatedSettings = {
      loginAlertsEnabled: Boolean(loginAlertsEnabled),
      ipRestrictionEnabled: Boolean(ipRestrictionEnabled),
      telegram2FAEnabled: Boolean(telegram2FAEnabled)
    };

    res.json({
      message: 'Security settings updated successfully',
      settings: updatedSettings
    });
  } catch (error) {
    console.error('Error updating security settings:', error);
    res.status(500).json({ message: 'Failed to update security settings' });
  }
});

// Get admin dashboard stats
adminRouter.get('/stats', isAdmin, async (req: Request, res: Response) => {
  try {
    const products = await storage.getProducts();
    const invoices = await storage.getInvoices();

    const activeProducts = products.filter(p => p.active).length;
    const totalSales = invoices.length;
    const pendingInvoices = invoices.filter(i => i.status === 'pending').length;
    const paidInvoices = invoices.filter(i => i.status === 'paid').length;

    // Calculate total revenue from paid invoices
    const revenue = invoices
      .filter(i => i.status === 'paid')
      .reduce((sum, invoice) => sum + parseFloat(invoice.amount), 0);

    res.json({
      products: {
        total: products.length,
        active: activeProducts
      },
      sales: {
        total: totalSales,
        pending: pendingInvoices,
        completed: paidInvoices,
        revenue: revenue.toFixed(2)
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ message: 'Failed to fetch dashboard stats' });
  }
});

// Get all invoices with pagination
adminRouter.get('/invoices', isAdmin, async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const search = req.query.search as string || '';
    const orderType = req.query.orderType as 'all' | 'regular' | 'trial' || 'all';

    const result = await storage.getInvoicesPaginated(page, limit, search, orderType);
    const customCheckoutPages = await storage.getCustomCheckoutPages();

    console.log('Paginated invoices result:', JSON.stringify(result, null, 2));
    console.log('Custom checkout pages:', JSON.stringify(customCheckoutPages, null, 2));

    // Enhance invoices with product name and checkout page info for display
    const enhancedInvoices = await Promise.all(result.invoices.map(async (invoice) => {
      try {
        const product = await storage.getProduct(invoice.productId);

        // Check if this invoice was created from a custom checkout page
        let checkoutPageInfo = null;
        if (invoice.customCheckoutPageId) {
          checkoutPageInfo = customCheckoutPages.find(page => page.id === invoice.customCheckoutPageId);
          console.log(`Found checkout page for invoice ${invoice.id}:`, checkoutPageInfo);
        } else {
          console.log(`No customCheckoutPageId for invoice ${invoice.id}`);

          // Try to find a matching checkout page based on the product ID
          // This is a fallback for older orders that don't have customCheckoutPageId
          const matchingPage = customCheckoutPages.find(page => {
            // For trial orders, find a trial checkout page
            // Convert to boolean for comparison (SQLite stores as 0/1)
            if (invoice.isTrialOrder) {
              return page.isTrialCheckout;
            }
            // For regular orders, find a regular checkout page
            return !page.isTrialCheckout;
          });

          if (matchingPage) {
            console.log(`Found matching checkout page for invoice ${invoice.id} based on type:`, matchingPage);
            checkoutPageInfo = matchingPage;

            try {
              // Update the invoice with the checkout page ID for future reference
              await storage.updateInvoice(invoice.id, { customCheckoutPageId: matchingPage.id });
            } catch (updateError) {
              console.error(`Error updating invoice ${invoice.id} with checkout page ID:`, updateError);
            }
          }
        }

        return {
          ...invoice,
          productName: product ? product.name : 'Unknown Product',
          checkoutPageTitle: checkoutPageInfo ? checkoutPageInfo.title : null,
          checkoutPageId: checkoutPageInfo ? checkoutPageInfo.id : null,
          smtpProviderId: checkoutPageInfo ? checkoutPageInfo.smtpProviderId : null
        };
      } catch (error) {
        console.error(`Error enhancing invoice ${invoice.id}:`, error);
        // Return the invoice without enhancement if there's an error
        return {
          ...invoice,
          productName: 'Unknown Product',
          checkoutPageTitle: null,
          checkoutPageId: null,
          smtpProviderId: null
        };
      }
    }));

    res.json({
      invoices: enhancedInvoices,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages
      }
    });
  } catch (error) {
    console.error('Error fetching invoices:', error);
    res.status(500).json({ message: 'Failed to fetch invoices' });
  }
});

// Create new invoice (for testing purposes)
adminRouter.post('/invoices', isAdmin, async (req: Request, res: Response) => {
  try {
    const { customerName, customerEmail, productId, amount, status, createdAt } = req.body;

    // Validate required fields
    if (!customerName || !customerEmail || !productId || !amount) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Create the invoice
    const invoice = await storage.createInvoice({
      customerName,
      customerEmail,
      productId: Number(productId),
      amount: amount.toString(),
      status: status || 'pending',
      createdAt: createdAt || new Date().toISOString()
    });

    console.log('Created test invoice:', invoice);

    res.status(201).json(invoice);
  } catch (error) {
    console.error('Error creating invoice:', error);
    res.status(500).json({
      message: 'Failed to create invoice',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Update invoice
adminRouter.put('/invoices/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    const { customerName, customerEmail, status, notes, checkForTrialUpgrade, smtpProviderId } = req.body;

    // Get the existing invoice
    const existingInvoice = await storage.getInvoice(invoiceId);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Only update fields that are actually provided (not undefined)
    const updateData: any = {};
    if (customerName !== undefined) updateData.customerName = customerName;
    if (customerEmail !== undefined) updateData.customerEmail = customerEmail;
    if (status !== undefined) updateData.status = status;
    if (notes !== undefined) updateData.notes = notes;
    if (smtpProviderId !== undefined) updateData.smtpProviderId = smtpProviderId;

    // Update the invoice
    const updatedInvoice = await storage.updateInvoice(invoiceId, updateData);

    console.log('Updated invoice:', updatedInvoice);

    // Check if we need to upgrade a trial order
    let trialOrderUpgraded = false;

    if (checkForTrialUpgrade && status && status.toLowerCase() === 'paid' && customerEmail) {
      console.log(`Checking for trial orders to upgrade for customer email: ${customerEmail}`);

      // Find trial orders with the same email that haven't been upgraded yet
      const allInvoices = await storage.getInvoices();
      const trialOrders = allInvoices.filter(invoice =>
        invoice.customerEmail === customerEmail &&
        invoice.isTrialOrder === true &&
        invoice.hasUpgraded !== true &&
        invoice.id !== invoiceId // Don't include the current invoice
      );

      console.log(`Found ${trialOrders.length} trial orders for customer ${customerEmail} that can be upgraded`);

      if (trialOrders.length > 0) {
        // Upgrade the trial order(s)
        for (const trialOrder of trialOrders) {
          await storage.updateInvoice(trialOrder.id, {
            hasUpgraded: true,
            upgradedAt: new Date().toISOString()
          });
          console.log(`Automatically upgraded trial order ${trialOrder.id} for customer ${customerEmail}`);
          trialOrderUpgraded = true;
        }
      }
    }

    res.json({
      message: 'Invoice updated successfully',
      invoice: updatedInvoice,
      trialOrderUpgraded
    });
  } catch (error) {
    console.error('Error updating invoice:', error);
    res.status(500).json({
      message: 'Failed to update invoice',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Mark invoice as upgraded
adminRouter.put('/invoices/:id/upgrade', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    const { hasUpgraded, upgradedAt } = req.body;

    // Get the existing invoice
    const existingInvoice = await storage.getInvoice(invoiceId);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Update the invoice
    const updatedInvoice = await storage.updateInvoice(invoiceId, {
      hasUpgraded,
      upgradedAt
    });

    console.log('Marked invoice as upgraded:', updatedInvoice);

    res.json({
      message: 'Invoice marked as upgraded successfully',
      invoice: updatedInvoice
    });
  } catch (error) {
    console.error('Error marking invoice as upgraded:', error);
    res.status(500).json({
      message: 'Failed to mark invoice as upgraded',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Delete invoice (remove customer)
adminRouter.delete('/invoices/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const invoiceId = parseInt(req.params.id);
    if (isNaN(invoiceId)) {
      return res.status(400).json({ message: 'Invalid invoice ID' });
    }

    // Get the existing invoice
    const existingInvoice = await storage.getInvoice(invoiceId);
    if (!existingInvoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Delete the invoice from storage
    const deleted = await storage.deleteInvoice(invoiceId);
    if (!deleted) {
      throw new Error('Failed to delete invoice from database');
    }
    console.log(`Deleted invoice with ID ${invoiceId}`);

    res.json({
      message: 'Customer removed successfully',
      invoiceId
    });
  } catch (error) {
    console.error('Error removing customer:', error);
    res.status(500).json({
      message: 'Failed to remove customer',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get all products for admin
adminRouter.get('/products', isAdmin, async (req: Request, res: Response) => {
  try {
    const products = await storage.getProducts();
    res.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ message: 'Failed to fetch products' });
  }
});

// Create new product
adminRouter.post('/products', isAdmin, async (req: Request, res: Response) => {
  try {
    const productData = insertProductSchema.parse(req.body);
    const product = await storage.createProduct(productData);
    res.status(201).json(product);
  } catch (error) {
    console.error('Error creating product:', error);

    if (error instanceof ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to create product' });
  }
});

// Update product
adminRouter.patch('/products/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const productId = parseInt(req.params.id);
    if (isNaN(productId)) {
      return res.status(400).json({ message: 'Invalid product ID' });
    }

    // In a real application with a database, this would use a real update operation
    // For our in-memory store, we'll get the product, validate the update, and simulate the update
    const existingProduct = await storage.getProduct(productId);
    if (!existingProduct) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // Since we don't have a real update method in storage, let's simulate it
    // In a real app with a DB, you'd use storage.updateProduct() instead
    const updatedProduct = {
      ...existingProduct,
      ...req.body,
      id: productId  // Ensure ID doesn't change
    };

    // Validate the combined object
    insertProductSchema.parse(updatedProduct);

    // Simulate updating the product (would be a DB update in a real app)
    // This is a hack for our demo app; in a real app with a DB, you'd use storage.updateProduct
    const allProducts = await storage.getProducts();
    const productIndex = allProducts.findIndex(p => p.id === productId);
    if (productIndex !== -1) {
      allProducts[productIndex] = updatedProduct;
    }

    res.json(updatedProduct);
  } catch (error) {
    console.error('Error updating product:', error);

    if (error instanceof ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update product' });
  }
});

// Delete product
adminRouter.delete('/products/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const productId = parseInt(req.params.id);
    if (isNaN(productId)) {
      return res.status(400).json({ message: 'Invalid product ID' });
    }

    // Check if product exists
    const existingProduct = await storage.getProduct(productId);
    if (!existingProduct) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // In a real app with a DB, you'd use a storage.deleteProduct method
    // Since we're using in-memory storage for this demo, we'll simulate deletion
    const allProducts = await storage.getProducts();
    const updatedProducts = allProducts.filter(p => p.id !== productId);

    // This is a hack for our demo app; in a real app with DB, use storage.deleteProduct
    (storage as any).products = new Map(updatedProducts.map(p => [p.id, p]));

    res.status(200).json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ message: 'Failed to delete product' });
  }
});

adminRouter.post('/email-test', isAdmin, async (req: Request, res: Response) => {
  const { email, providerId } = req.body;

  if (!email) {
    return res.status(400).json({ message: 'Email address is required' });
  }

  try {
    // Import dynamically to avoid circular dependencies
    const { sendTestEmail } = await import('../services/email');

    // Send actual test email
    const success = await sendTestEmail(email, providerId);

    if (success) {
      res.json({
        message: `Test email sent to ${email}`,
        success: true
      });
    } else {
      res.status(500).json({
        message: 'Failed to send test email. Please check your SMTP configuration.',
        success: false
      });
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).json({
      message: 'Failed to send test email due to an error',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create a test sale for testing purposes
adminRouter.post('/create-test-sale', isAdmin, async (req: Request, res: Response) => {
  try {
    const { customerName, customerEmail, productId, amount } = req.body;

    // Create a test invoice
    const invoiceId = `TEST-${Date.now()}`;
    const invoice = await storage.createInvoice({
      customerName: customerName || 'Test Customer',
      customerEmail: customerEmail || '<EMAIL>',
      productId: productId || 3, // Default to Productivity App Template
      amount: amount || 79.99,
      status: 'draft',
      paypalInvoiceId: invoiceId,
      paypalInvoiceUrl: 'https://www.sandbox.paypal.com/invoice/manage',
      createdAt: new Date().toISOString(),
      notes: 'Test invoice created for demonstration purposes'
    });

    console.log('Created test sale:', invoice);

    res.status(201).json({
      message: 'Test sale created successfully',
      invoiceId,
      invoice
    });
  } catch (error) {
    console.error('Error creating test sale:', error);
    res.status(500).json({
      message: 'Failed to create test sale',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Test endpoint
adminRouter.post('/test-endpoint', (req: Request, res: Response) => {
  console.log('Test endpoint called with body:', req.body);
  res.json({
    message: 'Test endpoint called successfully',
    receivedData: req.body
  });
});

// Mock image upload endpoint
// In a real app, this would use a file storage service like AWS S3
adminRouter.post('/upload-image', isAdmin, (req: Request, res: Response) => {
  try {
    const { imageData } = req.body;

    if (!imageData || !imageData.startsWith('data:image/')) {
      return res.status(400).json({ message: 'Valid image data is required' });
    }

    // Generate a random filename
    const randomId = randomBytes(16).toString('hex');
    const imageType = imageData.split(';')[0].split('/')[1];
    const filename = `product-${randomId}.${imageType}`;

    // In a real app, we would save the image to disk or a cloud storage service here
    // For this demo, we'll just pretend we saved it and return a fake URL

    // Return a success response with the mockup image URL
    res.status(200).json({
      url: `https://images.unsplash.com/photo-${randomId}?w=500&q=80`,
      filename
    });
  } catch (error) {
    console.error('Error processing image upload:', error);
    res.status(500).json({ message: 'Failed to process image upload' });
  }
});

export default adminRouter;