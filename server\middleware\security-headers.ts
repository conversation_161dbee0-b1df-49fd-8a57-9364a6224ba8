import { Request, Response, NextFunction } from 'express';
import { securityMonitor, SecurityEventType, SecuritySeverity } from '../config/security-monitoring';

// Security headers configuration
interface SecurityHeadersConfig {
  // Content Security Policy
  csp: {
    enabled: boolean;
    directives: {
      defaultSrc: string[];
      scriptSrc: string[];
      styleSrc: string[];
      imgSrc: string[];
      connectSrc: string[];
      fontSrc: string[];
      objectSrc: string[];
      mediaSrc: string[];
      frameSrc: string[];
      childSrc: string[];
      workerSrc: string[];
      manifestSrc: string[];
      formAction: string[];
      frameAncestors: string[];
      baseUri: string[];
      upgradeInsecureRequests: boolean;
      blockAllMixedContent: boolean;
    };
    reportUri?: string;
    reportOnly: boolean;
  };
  
  // HTTP Strict Transport Security
  hsts: {
    enabled: boolean;
    maxAge: number;
    includeSubDomains: boolean;
    preload: boolean;
  };
  
  // X-Frame-Options
  frameOptions: {
    enabled: boolean;
    value: 'DENY' | 'SAMEORIGIN' | string; // string for ALLOW-FROM
  };
  
  // X-Content-Type-Options
  contentTypeOptions: {
    enabled: boolean;
    nosniff: boolean;
  };
  
  // X-XSS-Protection
  xssProtection: {
    enabled: boolean;
    mode: '0' | '1' | '1; mode=block';
  };
  
  // Referrer-Policy
  referrerPolicy: {
    enabled: boolean;
    policy: 'no-referrer' | 'no-referrer-when-downgrade' | 'origin' | 'origin-when-cross-origin' | 
            'same-origin' | 'strict-origin' | 'strict-origin-when-cross-origin' | 'unsafe-url';
  };
  
  // Permissions-Policy (formerly Feature-Policy)
  permissionsPolicy: {
    enabled: boolean;
    directives: {
      camera: string[];
      microphone: string[];
      geolocation: string[];
      payment: string[];
      usb: string[];
      magnetometer: string[];
      gyroscope: string[];
      accelerometer: string[];
      fullscreen: string[];
      notifications: string[];
      push: string[];
      vibrate: string[];
      autoplay: string[];
      encryptedMedia: string[];
      pictureInPicture: string[];
    };
  };
  
  // Additional security headers
  additionalHeaders: {
    'X-Permitted-Cross-Domain-Policies'?: string;
    'X-Download-Options'?: string;
    'X-DNS-Prefetch-Control'?: string;
    'Expect-CT'?: string;
    'Cross-Origin-Embedder-Policy'?: string;
    'Cross-Origin-Opener-Policy'?: string;
    'Cross-Origin-Resource-Policy'?: string;
  };
  
  // Configuration options
  enableReporting: boolean;
  logViolations: boolean;
  developmentMode: boolean;
}

// Default security headers configuration
const DEFAULT_CONFIG: SecurityHeadersConfig = {
  csp: {
    enabled: process.env.ENABLE_CSP !== 'false',
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: [
        "'self'",
        "'unsafe-inline'", // For development - should be removed in production
        "'unsafe-eval'", // For development - should be removed in production
        "https://js.stripe.com",
        "https://checkout.stripe.com",
        "https://www.paypal.com",
        "https://www.paypalobjects.com"
      ],
      styleSrc: [
        "'self'",
        "'unsafe-inline'", // For CSS-in-JS libraries
        "https://fonts.googleapis.com"
      ],
      imgSrc: [
        "'self'",
        "data:",
        "blob:",
        "https:",
        "https://www.paypal.com",
        "https://www.paypalobjects.com"
      ],
      connectSrc: [
        "'self'",
        "https://api.stripe.com",
        "https://api.paypal.com",
        "https://www.paypal.com"
      ],
      fontSrc: [
        "'self'",
        "https://fonts.gstatic.com"
      ],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: [
        "'self'",
        "https://js.stripe.com",
        "https://hooks.stripe.com",
        "https://www.paypal.com"
      ],
      childSrc: ["'self'"],
      workerSrc: ["'self'"],
      manifestSrc: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      baseUri: ["'self'"],
      upgradeInsecureRequests: process.env.NODE_ENV === 'production',
      blockAllMixedContent: process.env.NODE_ENV === 'production'
    },
    reportUri: process.env.CSP_REPORT_URI,
    reportOnly: process.env.CSP_REPORT_ONLY === 'true'
  },
  
  hsts: {
    enabled: process.env.ENABLE_HSTS !== 'false',
    maxAge: parseInt(process.env.HSTS_MAX_AGE || '31536000'), // 1 year
    includeSubDomains: process.env.HSTS_INCLUDE_SUBDOMAINS !== 'false',
    preload: process.env.HSTS_PRELOAD === 'true'
  },
  
  frameOptions: {
    enabled: process.env.ENABLE_X_FRAME_OPTIONS !== 'false',
    value: (process.env.X_FRAME_OPTIONS as any) || 'DENY'
  },
  
  contentTypeOptions: {
    enabled: process.env.ENABLE_X_CONTENT_TYPE_OPTIONS !== 'false',
    nosniff: true
  },
  
  xssProtection: {
    enabled: process.env.ENABLE_X_XSS_PROTECTION !== 'false',
    mode: (process.env.X_XSS_PROTECTION_MODE as any) || '1; mode=block'
  },
  
  referrerPolicy: {
    enabled: process.env.ENABLE_REFERRER_POLICY !== 'false',
    policy: (process.env.REFERRER_POLICY as any) || 'strict-origin-when-cross-origin'
  },
  
  permissionsPolicy: {
    enabled: process.env.ENABLE_PERMISSIONS_POLICY !== 'false',
    directives: {
      camera: [],
      microphone: [],
      geolocation: [],
      payment: ["'self'"],
      usb: [],
      magnetometer: [],
      gyroscope: [],
      accelerometer: [],
      fullscreen: ["'self'"],
      notifications: ["'self'"],
      push: [],
      vibrate: [],
      autoplay: [],
      encryptedMedia: [],
      pictureInPicture: ["'self'"]
    }
  },
  
  additionalHeaders: {
    'X-Permitted-Cross-Domain-Policies': 'none',
    'X-Download-Options': 'noopen',
    'X-DNS-Prefetch-Control': 'off',
    'Cross-Origin-Embedder-Policy': 'require-corp',
    'Cross-Origin-Opener-Policy': 'same-origin',
    'Cross-Origin-Resource-Policy': 'same-origin'
  },
  
  enableReporting: process.env.ENABLE_SECURITY_REPORTING !== 'false',
  logViolations: process.env.LOG_SECURITY_VIOLATIONS !== 'false',
  developmentMode: process.env.NODE_ENV !== 'production'
};

export class SecurityHeadersMiddleware {
  private config: SecurityHeadersConfig;

  constructor(config: Partial<SecurityHeadersConfig> = {}) {
    this.config = this.mergeConfig(DEFAULT_CONFIG, config);
  }

  // Deep merge configuration
  private mergeConfig(defaultConfig: SecurityHeadersConfig, userConfig: Partial<SecurityHeadersConfig>): SecurityHeadersConfig {
    const merged = { ...defaultConfig };
    
    if (userConfig.csp) {
      merged.csp = { ...defaultConfig.csp, ...userConfig.csp };
      if (userConfig.csp.directives) {
        merged.csp.directives = { ...defaultConfig.csp.directives, ...userConfig.csp.directives };
      }
    }
    
    if (userConfig.permissionsPolicy) {
      merged.permissionsPolicy = { ...defaultConfig.permissionsPolicy, ...userConfig.permissionsPolicy };
      if (userConfig.permissionsPolicy.directives) {
        merged.permissionsPolicy.directives = { ...defaultConfig.permissionsPolicy.directives, ...userConfig.permissionsPolicy.directives };
      }
    }
    
    Object.keys(userConfig).forEach(key => {
      if (key !== 'csp' && key !== 'permissionsPolicy') {
        (merged as any)[key] = { ...(defaultConfig as any)[key], ...(userConfig as any)[key] };
      }
    });
    
    return merged;
  }

  // Generate CSP header value
  private generateCSPHeader(): string {
    const { directives } = this.config.csp;
    const cspParts: string[] = [];

    // Add directive values
    Object.entries(directives).forEach(([directive, values]) => {
      if (directive === 'upgradeInsecureRequests' || directive === 'blockAllMixedContent') {
        if (values) {
          const kebabCase = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
          cspParts.push(kebabCase);
        }
      } else if (Array.isArray(values) && values.length > 0) {
        const kebabCase = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
        cspParts.push(`${kebabCase} ${values.join(' ')}`);
      }
    });

    // Add report URI if configured
    if (this.config.csp.reportUri) {
      cspParts.push(`report-uri ${this.config.csp.reportUri}`);
    }

    return cspParts.join('; ');
  }

  // Generate Permissions Policy header value
  private generatePermissionsPolicyHeader(): string {
    const { directives } = this.config.permissionsPolicy;
    const policyParts: string[] = [];

    Object.entries(directives).forEach(([directive, allowlist]) => {
      const kebabCase = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
      
      if (Array.isArray(allowlist)) {
        if (allowlist.length === 0) {
          policyParts.push(`${kebabCase}=()`);
        } else {
          policyParts.push(`${kebabCase}=(${allowlist.join(' ')})`);
        }
      }
    });

    return policyParts.join(', ');
  }

  // Main middleware function
  middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      try {
        // Content Security Policy
        if (this.config.csp.enabled) {
          const cspHeader = this.generateCSPHeader();
          const headerName = this.config.csp.reportOnly ? 'Content-Security-Policy-Report-Only' : 'Content-Security-Policy';
          res.setHeader(headerName, cspHeader);
        }

        // HTTP Strict Transport Security
        if (this.config.hsts.enabled && req.secure) {
          let hstsValue = `max-age=${this.config.hsts.maxAge}`;
          if (this.config.hsts.includeSubDomains) {
            hstsValue += '; includeSubDomains';
          }
          if (this.config.hsts.preload) {
            hstsValue += '; preload';
          }
          res.setHeader('Strict-Transport-Security', hstsValue);
        }

        // X-Frame-Options
        if (this.config.frameOptions.enabled) {
          res.setHeader('X-Frame-Options', this.config.frameOptions.value);
        }

        // X-Content-Type-Options
        if (this.config.contentTypeOptions.enabled && this.config.contentTypeOptions.nosniff) {
          res.setHeader('X-Content-Type-Options', 'nosniff');
        }

        // X-XSS-Protection
        if (this.config.xssProtection.enabled) {
          res.setHeader('X-XSS-Protection', this.config.xssProtection.mode);
        }

        // Referrer-Policy
        if (this.config.referrerPolicy.enabled) {
          res.setHeader('Referrer-Policy', this.config.referrerPolicy.policy);
        }

        // Permissions-Policy
        if (this.config.permissionsPolicy.enabled) {
          const permissionsPolicyHeader = this.generatePermissionsPolicyHeader();
          if (permissionsPolicyHeader) {
            res.setHeader('Permissions-Policy', permissionsPolicyHeader);
          }
        }

        // Additional security headers
        Object.entries(this.config.additionalHeaders).forEach(([header, value]) => {
          if (value) {
            res.setHeader(header, value);
          }
        });

        // Log security headers application
        if (this.config.enableReporting) {
          securityMonitor.logSecurityEvent(
            SecurityEventType.LOGIN_SUCCESS, // Using this as closest match for security headers
            {
              userId: undefined,
              username: 'system',
              role: 'system' as any,
              permissions: [],
              ipAddress: req.ip || 'unknown'
            },
            {
              action: 'SECURITY_HEADERS_APPLIED',
              userAgent: req.get('User-Agent'),
              path: req.path,
              method: req.method
            },
            SecuritySeverity.LOW
          );
        }

        next();

      } catch (error) {
        console.error('❌ Security headers middleware error:', error);
        
        // Log error but don't block the request
        if (this.config.logViolations) {
          securityMonitor.logSecurityEvent(
            SecurityEventType.SECURITY_VIOLATION,
            {
              userId: undefined,
              username: 'system',
              role: 'system' as any,
              permissions: [],
              ipAddress: req.ip || 'unknown'
            },
            {
              action: 'SECURITY_HEADERS_ERROR',
              error: error.message,
              path: req.path,
              method: req.method
            },
            SecuritySeverity.MEDIUM
          );
        }

        next();
      }
    };
  }

  // CSP violation reporting endpoint
  cspViolationReporter() {
    return (req: Request, res: Response) => {
      try {
        const violation = req.body;
        
        console.warn('🚨 CSP Violation Report:', JSON.stringify(violation, null, 2));
        
        if (this.config.logViolations) {
          securityMonitor.logSecurityEvent(
            SecurityEventType.SECURITY_VIOLATION,
            {
              userId: undefined,
              username: 'unknown',
              role: 'unknown' as any,
              permissions: [],
              ipAddress: req.ip || 'unknown'
            },
            {
              action: 'CSP_VIOLATION',
              violation: violation,
              userAgent: req.get('User-Agent')
            },
            SecuritySeverity.HIGH
          );
        }

        res.status(204).end();
      } catch (error) {
        console.error('❌ CSP violation reporter error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    };
  }

  // Get current configuration
  getConfig(): SecurityHeadersConfig {
    return { ...this.config };
  }

  // Update configuration
  updateConfig(newConfig: Partial<SecurityHeadersConfig>): void {
    this.config = this.mergeConfig(this.config, newConfig);
  }

  // Validate configuration
  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate HSTS max-age
    if (this.config.hsts.enabled && this.config.hsts.maxAge < 0) {
      errors.push('HSTS max-age must be non-negative');
    }

    // Validate CSP directives
    if (this.config.csp.enabled) {
      const { directives } = this.config.csp;
      
      // Check for unsafe directives in production
      if (!this.config.developmentMode) {
        if (directives.scriptSrc.includes("'unsafe-inline'")) {
          errors.push("'unsafe-inline' in script-src is not recommended for production");
        }
        if (directives.scriptSrc.includes("'unsafe-eval'")) {
          errors.push("'unsafe-eval' in script-src is not recommended for production");
        }
      }
    }

    // Validate frame options
    if (this.config.frameOptions.enabled) {
      const validValues = ['DENY', 'SAMEORIGIN'];
      const isAllowFrom = this.config.frameOptions.value.startsWith('ALLOW-FROM ');
      
      if (!validValues.includes(this.config.frameOptions.value) && !isAllowFrom) {
        errors.push('Invalid X-Frame-Options value');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Create and export default instance
export const securityHeaders = new SecurityHeadersMiddleware();

// Export middleware function for easy use
export const securityHeadersMiddleware = securityHeaders.middleware();
