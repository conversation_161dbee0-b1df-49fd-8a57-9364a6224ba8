# Security: Deny all access to the app directory
# This directory contains sensitive data and should never be web-accessible

# Deny all access
Deny from all

# Alternative for Apache 2.4+
<RequireAll>
    Require all denied
</RequireAll>

# Prevent directory browsing
Options -Indexes

# Disable server signature
ServerSignature Off

# Security headers for any accidental access
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "no-referrer"
    Header always set Content-Security-Policy "default-src 'none'"
</IfModule>

# Block common attack patterns
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Block all requests
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# Protect specific file types
<FilesMatch "\.(db|json|log|key|env|config|bak|backup|sql|sqlite|sqlite3)$">
    Deny from all
</FilesMatch>

# Additional protection for Apache 2.4+
<FilesMatch "\.(db|json|log|key|env|config|bak|backup|sql|sqlite|sqlite3)$">
    <RequireAll>
        Require all denied
    </RequireAll>
</FilesMatch>
